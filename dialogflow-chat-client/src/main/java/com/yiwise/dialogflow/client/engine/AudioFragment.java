package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import javax.sound.sampled.AudioSystem;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

@Slf4j
@Data
public class AudioFragment {

    private static final AtomicLong INDEX = new AtomicLong();
    public static final AudioFragment PLACE_HOLDER_FRAGMENT = new AudioFragment();

    public static final AudioFragment COMPLETE_FRAGMENT = new AudioFragment();

    long index = INDEX.getAndIncrement();
    /**
     * 音频在本地录音 /tmp/xxx.wav
     */
    String path;

    /**
     * oss 中的 key, 如果是真人录音的话
     */
    String ossKey;

    /**
     * 音频持续时长 = size / (320 * 50)
     */
    int duration;

    /**
     * 音频大小(去掉skip的大小)
     */
    int size;

    /**
     * 开始读取的偏移量，如果是wav文件，则去掉头部长度
     */
    int startOffset;

    /**
     * 文件结束的偏移量
     */
    int endOffset;

    /**
     * 是否准备好
     */
    volatile boolean ready;

    /**
     * 对应的文本
     */
    String text;

    /**
     * 对应的文本模板
     */
    String template;

    TextPlaceholderTypeEnum type;

    AnswerPlaceholderElement originElement;

    CompletableFuture<Boolean> future = new CompletableFuture<>();

    private Consumer<AudioFragment> task;

    boolean isComposeTask;

    AtomicBoolean submitted = new AtomicBoolean();

    boolean streaming;

    List<ChatHistoryItem> historyList;

    /**
     * 合成失败
     */
    volatile boolean failed;

    private void setTask(Consumer<AudioFragment> task) {

    }

    public void setTask(boolean isComposeTask, Consumer<AudioFragment> task) {
        this.isComposeTask = isComposeTask;
        this.task = task;
    }

    public boolean taskReady() {
        return task != null;
    }

    private Consumer<AudioFragment> getTask() {
        return task;
    }

    public void executeTask() {
        if (task != null) {
            task.accept(this);
        }
    }

    public void setOriginElement(AnswerPlaceholderElement originElement) {
        this.originElement = originElement;
        this.type = originElement.getType();
    }

    public void complete(String localPath) {
        this.setPath(localPath);
        try {
            if (StringUtils.isNotBlank(localPath)) {
                // 设置文件大小
                File file = new File(localPath);
                if (file.exists()) {
                    if (localPath.endsWith(".wav")) {
                        this.startOffset = DialogEngineConstant.WAV_TO_PCM_HEAD_LEN;
                    }
                    int audioSize = AudioSizeCache.getAudioByteSize(localPath);
                    if (audioSize < 1) {
                        log.warn("读取音频文件异常:{}, size:{}", localPath, audioSize);
                    }
                    audioSize = audioSize - audioSize % 320;
                    // 跳过文件末尾不足 320字节的部分
                    this.endOffset = audioSize + startOffset;
                    this.setSize(audioSize);
                    this.setDuration(audioSize  / DialogEngineConstant.BUFFER_SIZE_PER_SECOND);
                    this.setReady(true);
                    if (isComposeTask) {
                        log.debug("complete audio fragment: {}", JsonUtils.object2String(this));
                    }
                    return;
                }
            }

            log.debug("音频处理错误:{}:{}", template, text);
            this.failed = true;
            this.setSize(0);
            this.setDuration(0);
            this.setReady(true);
        } finally {
            future.complete(true);
        }
    }

    public String getCacheKey() {
        return String.format("%s__%s", template, text);
    }

    @Override
    public String toString() {
        return "AudioFragment{" +
                "path='" + path + '\'' +
                ", ossKey='" + ossKey + '\'' +
                ", duration=" + duration +
                ", size=" + size +
                ", startOffset=" + startOffset +
                ", endOffset=" + endOffset +
                ", ready=" + ready +
                ", text='" + text + '\'' +
                ", template='" + template + '\'' +
                '}';
    }

}
