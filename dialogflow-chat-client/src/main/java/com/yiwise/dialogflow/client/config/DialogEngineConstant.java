package com.yiwise.dialogflow.client.config;

import com.yiwise.base.common.utils.PropertyLoaderUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DialogEngineConstant {
    public static final int USER_INPUT_MERGE_INTERVAL_MS = 600;

    public static final int MONO_FILE_LENGTH_PER_SECOND = 16_000;

    public static final int MONO_FILE_LENGTH_PER_MS = 16;

    public static final int PACKET_BUFFER_SIZE = 320;

    public static final int PACKET_COUNT_PER_SECOND = 50;

    public static final int BUFFER_SIZE_PER_SECOND = PACKET_BUFFER_SIZE * PACKET_COUNT_PER_SECOND;

    public static final int PER_PACKAGE_DURATION_MS = 20;

    public static final int WAV_TO_PCM_HEAD_LEN = 44 + 16 * 5;

    /**
     * 用户输入结束和用户无应答事件间隔的阈值, 用来解决响应完用户无应答时间后, 紧接着理解响应了用户输入完成的事件,
     * 这个时候, 用户输入其实回复的是客户无应答之前的答案的
     */
    public static long ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS = 80L;

    public static boolean enableDebug = false;

    static {
        PropertyLoaderUtils.addPropertyChangeListener(DialogEngineConstant::reload);
        reload();
    }

    private static void reload() {
        if (PropertyLoaderUtils.containsProperty("dialogflow.engineClient.userSilenceIntervalMs")) {
            ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS = PropertyLoaderUtils.getLongProperty("dialogflow.engineClient.userSilenceIntervalMs");
        } else {
            ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS = 80L;
        }
        log.info("reload dialogflow.engineClient.userSilenceIntervalMs: {}", ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS);
        if (PropertyLoaderUtils.containsProperty("dialogflow.engineClient.logger.enableDebug")) {
            enableDebug = PropertyLoaderUtils.getBooleanProperty("dialogflow.engineClient.logger.enableDebug");
        } else {
            enableDebug = false;
        }
        log.info("reload dialogflow.engineClient.logger.enableDebug: {}", enableDebug);

    }

}
