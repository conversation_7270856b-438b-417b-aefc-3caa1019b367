package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import com.yiwise.dialogflow.client.listener.AudioPlayEventListener;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class FragmentAudioPlayManagerV2 implements AudioPlayManager {

    public static final int TIMEOUT_MS = 5000;

    private final List<AudioPlayEventListener> listenerList;

    private volatile long lastAiPlayTime;

    private volatile long lastPlayEndTime;

    private volatile boolean pause;

    private volatile boolean isPlayFinish = false;

    private volatile boolean isPlay = false;

    private final Map<String, AudioPlayStatus> playStatusMap = new ConcurrentHashMap<>();

    private final FragmentAudioManager audioManager;

    private volatile AudioPlayStatus currentAnswerPlayStatus;

    private final AtomicInteger index = new AtomicInteger();

    public FragmentAudioPlayManagerV2(FragmentAudioManager audioManager) {
        this.audioManager = audioManager;
        this.listenerList = new ArrayList<>();
    }

    private final Lock lock = new ReentrantLock(true);

    @Override
    public void reset() {
        lock.lock();
        try {
            log.debug("reset audioPlayManager");
            listenerList.clear();
            lastAiPlayTime = 0;
            lastPlayEndTime = 0;
            pause = false;
            isPlayFinish = false;
            isPlay = false;
            playStatusMap.clear();
            currentAnswerPlayStatus = null;
            index.set(0);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public byte[] readAudioFrame() {
        lock.lock();
        try {
            onReadData();
            byte[] buffer = new byte[DialogEngineConstant.PACKET_BUFFER_SIZE];
            if (!isPlayFinish && currentAnswerPlayStatus != null) {
                if (pause) {
                    return buffer;
                }
                readAndUpdateInputStream(buffer);
                //第一次调用执行onAiPlayBegin(),最后一次调用执行onAiPlayEnd()
                if (!isPlay) {
                    log.debug("录音开始播放处理，fragment:{}", currentAnswerPlayStatus.currentFragmentPlayStatus);
                    onAiPlayBegin();
                    isPlay = true;
                }
                if (isPlayFinish) {
                    log.debug("录音播放完成后处理,fragment:{}", currentAnswerPlayStatus.currentFragmentPlayStatus);
                    onAiPlayEnd();
                    isPlay = false;
                }
            }
            if (currentAnswerPlayStatus != null) {
                currentAnswerPlayStatus.tryMergeAndCompose();
            }
            return buffer;
        } catch (Exception e) {
            log.warn("[LogHub_Warn] 读取音频异常", e);
            return new byte[DialogEngineConstant.PACKET_BUFFER_SIZE];
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean isPlaying() {
        return Objects.nonNull(currentAnswerPlayStatus);
    }

    @Override
    public int playAudio(AnswerResult answer) {
        lock.lock();
        try {
            //获取语音文件地址
            index.getAndIncrement();
            List<AudioFragment> fragments = audioManager.getAudioFragments(answer);
            AudioPlayStatus audioPlayStatus = new AudioPlayStatus(index.get(), answer, fragments, this);

            //设置语音播放流
            playStatusMap.put(answer.getId(), audioPlayStatus);
            settingAudioStream(audioPlayStatus);
            return index.get();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void pause() {
        lock.lock();
        try {
            if (!pause) {
                log.info("暂停录音播放");
            }
            if (getCurrentAnswerPlayPercent() >= 100d) {
                log.info("当前答案:{}, 已播放完成，不需要暂停", currentAnswerPlayStatus);
                return;
            }
            pause = true;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void stop() {
        log.info("逻辑终止当前答案播放, 主要是在等待用户应答时, 能够触发 userSilence 事件");
        pause = true;
        isPlayFinish = false;
        lastPlayEndTime = System.currentTimeMillis();
    }

    @Override
    public void resume() {
        if (pause) {
            log.info("恢复录音播放");
        }
        pause = false;
    }

    @Override
    public void resume(String answerId) {
        lock.lock();
        try {
            log.info("恢复指定答案录音播放,answerId:{}", answerId);
            if (currentAnswerPlayStatus != null
                    && StringUtils.isNotBlank(answerId)
                    && currentAnswerPlayStatus.getAnswerResult() != null
                    && answerId.equals(currentAnswerPlayStatus.getAnswerResult().getId())) {
                resume();
                return;
            }
            if (pause) {
                log.info("恢复录音播放");
            }
            pause = false;

            AudioPlayStatus preStatus = playStatusMap.get(answerId);
            if (Objects.nonNull(preStatus)) {
                try {
                    log.debug("恢复音频播放, 恢复状态:{}", preStatus);
                    preStatus.resume();
                } catch (IOException e) {
                    log.error("恢复录音播放失败，answerId:{},filePlaySize:{},filePath:{}", answerId, "todo", "todo", e);
                }
                settingAudioStream(preStatus);
            } else {
                log.error("恢复录音播放失败，answerId:{},filePlaySize:{},filePath:{}", answerId, "todo", "todo");
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public double getCurrentAnswerPlayPercent() {
        return getAnswerPlayPercent(currentAnswerPlayStatus);
    }

    @Override
    public double getAnswerPlayPercentByAnswerId(String answerId) {
        if (StringUtils.isBlank(answerId)) {
            return 0.0;
        }
        return getAnswerPlayPercent(playStatusMap.get(answerId));
    }

    @Override
    public int getPlayedAnswerIndex() {
        return  currentAnswerPlayStatus == null ? 0 : currentAnswerPlayStatus.getIndex();
    }

    private double getAnswerPlayPercent(AudioPlayStatus status) {
        if (Objects.isNull(status)) {
            return 0.0;
        }
        return status.getReadPercent();
    }

    @Override
    public int getCurrentAnswerPlayTime() {
        return getAnswerPlayTime(currentAnswerPlayStatus);
    }

    @Override
    public int getAnswerPlayTimeByAnswerId(String answerId) {
        if (StringUtils.isBlank(answerId)) {
            return 0;
        }
        return getAnswerPlayTime(playStatusMap.get(answerId));
    }

    private int getAnswerPlayTime(AudioPlayStatus audioPlayStatus) {
        if (Objects.isNull(audioPlayStatus)) {
            return 0;
        }
        return audioPlayStatus.getReadDuration();
    }

    @Override
    public long getLastPlayTime() {
        return lastAiPlayTime;
    }

    @Override
    public long getLastPlayEndTime() {
        return lastPlayEndTime;
    }

    @Override
    public void registerListener(AudioPlayEventListener listener) {
        listenerList.add(listener);
    }

    @Override
    public String getLastPlayAnswerId() {
        return currentAnswerPlayStatus == null ? null : currentAnswerPlayStatus.getAnswerResult().getId();
    }

    private void readAndUpdateInputStream(byte[] buffer) {
        int readSize = currentAnswerPlayStatus.read(buffer);
        if (readSize < 0) {
            isPlayFinish = true;
            lastPlayEndTime = System.currentTimeMillis();
        } else {
            lastAiPlayTime = System.currentTimeMillis();
        }
        if (readSize > 0) {
            updatePlayAudioTime();
        }
    }

    private void updatePlayAudioTime() {
        emitterEvent(listener -> listener.updatePlayAudioTime(index.get()));
    }

    private void settingAudioStream(AudioPlayStatus answerAudioPlayInfo) {
        AnswerResult answer = answerAudioPlayInfo.getAnswerResult();
        log.debug("设置播放语音流，answerId:{}, realAnswer:{}, fragments:{}", answer.getId(),
                answer.getRealAnswer(), JsonUtils.object2String(answerAudioPlayInfo.getFragmentList()));
        if (currentAnswerPlayStatus != null) {
            currentAnswerPlayStatus.close();
        }
        pause = false;
        currentAnswerPlayStatus = answerAudioPlayInfo;
        lastAiPlayTime = System.currentTimeMillis();
        lastPlayEndTime = 0;
        isPlay = false;
        isPlayFinish = false;
    }

    private void onAiPlayBegin() {
        log.debug("onAiPlayBegin");
        emitterEvent(listener -> listener.onAiPlayBegin(index.get()));
    }

    private void onAiPlayEnd() {
        log.debug("onAiPlayEnd");
        emitterEvent(listener -> listener.onAiPlayEnd(index.get()));
    }

    private void onReadData() {
        emitterEvent(AudioPlayEventListener::onReadData);
    }

    private void emitterEvent(Consumer<AudioPlayEventListener> consumer) {
        if (CollectionUtils.isEmpty(listenerList)) {
            return;
        }
        List<AudioPlayEventListener> listenerCopy = new ArrayList<>(listenerList);
        for (AudioPlayEventListener audioPlayEventListener : listenerCopy) {
            try {
                consumer.accept(audioPlayEventListener);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    private void onWaitingTimeout() {
        log.debug("onWaitingTimeout, playStatus:{}", currentAnswerPlayStatus);
        emitterEvent(AudioPlayEventListener::onWaitingTimeout);
    }

    @Override
    public void onDynamicChanged(Map<String, String> dynamicVarMap) {
        audioManager.doOnDynamicVarMapMayChanged(dynamicVarMap);
    }

    @Override
    public void replay() {
        lock.lock();
        try {
            AudioPlayStatus preStatus = currentAnswerPlayStatus;
            if (Objects.nonNull(currentAnswerPlayStatus)) {
                preStatus.reset();
                settingAudioStream(preStatus);
            }
            resume();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void appendPlaceholderFragment() {
        lock.lock();
        try {
            if (currentAnswerPlayStatus != null) {
                currentAnswerPlayStatus.getFragmentList().add(AudioFragment.PLACE_HOLDER_FRAGMENT);
            }
        } finally {
            lock.unlock();
        }
    }
    @Override
    public void appendCompleteFragment() {
        lock.lock();
        try {
            if (currentAnswerPlayStatus != null) {
                currentAnswerPlayStatus.getFragmentList().add(AudioFragment.COMPLETE_FRAGMENT);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean isPause() {
        return pause;
    }

    @Override
    public int appendAnswer(AnswerResult answer, List<ChatHistoryItem> historyList) {
        lock.lock();
        try {
            // 获取当前正在播放的答案文本
            if (currentAnswerPlayStatus != null && currentAnswerPlayStatus.allowAppend()) {
                currentAnswerPlayStatus.append(audioManager.createAudioFragments(answer, true, historyList, true));
                appendPlaceholderFragment();
                currentAnswerPlayStatus.tryMergeAndCompose();
                log.debug("追加答案成功, 追加后播放状态:{}", currentAnswerPlayStatus);
            } else {
                log.warn("正在播放的答案与新答案不一致, 不做处理, 当前答案:{}, 新答案:{}", currentAnswerPlayStatus, answer);
            }
            return currentAnswerPlayStatus != null ? currentAnswerPlayStatus.getIndex() : index.get();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public int getCurrentIndex() {
        return index.get();
    }

    @Override
    public void onHangup() {
        if (currentAnswerPlayStatus != null) {
            currentAnswerPlayStatus.close();
        }
    }

    @Override
    public String getCurrentAnswerPlayedContent() {
        if (currentAnswerPlayStatus != null) {
            return currentAnswerPlayStatus.getPlayedContent();
        }
        return getAnswerPlayedContent(getLastPlayAnswerId());
    }
    @Override
    public String getCurrentAnswerFullContent() {
        if (currentAnswerPlayStatus != null) {
            return currentAnswerPlayStatus.getFullContent();
        }
        String lastAnswerId = getLastPlayAnswerId();
        if (StringUtils.isBlank(lastAnswerId)) {
            return "";
        }
        AudioPlayStatus playStatus = playStatusMap.get(lastAnswerId);
        if (Objects.isNull(playStatus)) {
            return "";
        }
        return playStatus.getFullContent();
    }

    @Override
    public String getAnswerPlayedContent(String answerId) {
        if (StringUtils.isBlank(answerId)) {
            return "";
        }
        AudioPlayStatus playStatus = playStatusMap.get(answerId);
        if (Objects.isNull(playStatus)) {
            return null;
        }
        return playStatus.getPlayedContent();
    }

    public Optional<AudioPlayStatus> getPlayStatus(String answerId) {
        return Optional.ofNullable(playStatusMap.get(answerId));
    }

    public Optional<AudioPlayStatus> getCurrentPlayStatus() {
        return Optional.ofNullable(currentAnswerPlayStatus);
    }

    @Data
    public static class AudioPlayStatus {
        final int index;
        /**
         * 答案
         */
        final AnswerResult answerResult;

        final List<AudioFragment> fragmentList;

        final FragmentAudioPlayManagerV2 manager;

        volatile FragmentPlayStatus currentFragmentPlayStatus;

        volatile long placeholderTimeout;

        volatile long waitingReadyTimeout;

        /**
         * 当前播放的音频段索引
         */
        final AtomicInteger nextFragmentIndex = new AtomicInteger(0);

        final AtomicInteger totalReadSize = new AtomicInteger(0);

        boolean appended;

        final Lock lock = new ReentrantLock(true);

        public AudioPlayStatus(int index,
                               AnswerResult answerResult,
                               List<AudioFragment> fragmentList,
                               FragmentAudioPlayManagerV2 fragmentAudioPlayManager) {
            this.index = index;
            this.answerResult = answerResult;
            this.fragmentList = new ArrayList<>(fragmentList);
            this.manager = fragmentAudioPlayManager;
        }

        public void append(List<AudioFragment> fragments) {
            lock.lock();
            try {
                if (fragments != null) {
                    appended = true;
                    fragmentList.addAll(fragments);
                }
            } finally {
                lock.unlock();
            }

        }

        /**
         * 读取音频数据到 buffer 中,
         * @return                  -1: 读取结束/失败, 调用方应该执行结束逻辑
         *                           0: 未读取到数据, 调用方应该播放空白音
         *       N(>0 < buffer.length): 读取到部分数据
         *               buffer.length: 读取完整数据
         */
        public int read(byte[] buffer) {
            lock.lock();
            try {
                int readSize = doRead(buffer);
                if (readSize > 0) {
                    totalReadSize.addAndGet(readSize);
                } else if (readSize == -1) {
                    totalReadSize.set(Integer.MAX_VALUE);
                }
                return readSize;
            } catch (Exception e) {
                log.warn("读取音频文件失败", e);
                totalReadSize.set(Integer.MAX_VALUE);
                return -1;
            } finally {
                lock.unlock();
            }
        }

        // 判断当前播放的 fragment 剩余音频是否不足 1000毫秒, 如果不足, 则开始合成后续的 fragment
        // 如果后续有多个 fragment 待合成, 则需要合并多个 fragment 之后, 再合成
        void tryMergeAndCompose() {
            lock.lock();
            try {
                // 判断 fragment 还有多少待合成的, 如果没有, 则直接返回
                if (waitComposeFragmentCount() < 1) {
                    return;
                }
                if (currentFragmentPlayStatus == null) {
                    return;
                }
                // 当前正在播放的fragment 还剩多少待播放的
                int leftAudioSize = currentFragmentPlayStatus.currentFragmentLeftAudioSize();
                if (leftAudioSize > 16000) {
                    return;
                }
                // 剩余音频小于 1 秒, 等待合并

                int nextFragmentIndex = getNextFragmentIndex().get();

                List<AudioFragment> subFragmentList = new ArrayList<>();

                AudioFragment startFragment = null;
                int startIndex = nextFragmentIndex;
                for (int i = nextFragmentIndex; i < fragmentList.size(); i++) {
                    AudioFragment fragment  = fragmentList.get(i);
                    if (fragment == AudioFragment.PLACE_HOLDER_FRAGMENT) {
                        continue;
                    }
                    if (fragment == AudioFragment.COMPLETE_FRAGMENT) {
                        continue;
                    }
                    if (fragment.isComposeTask && !fragment.submitted.get()) {
                        subFragmentList.add(fragment);
                        if (startFragment == null) {
                            startFragment = fragment;
                            startIndex = i;
                        }
                    } else {
                        if (startFragment != null && !fragment.isComposeTask) {
                            break;
                        }
                    }
                }
                if (CollectionUtils.isEmpty(subFragmentList)) {
                    return;
                }
                log.debug("当前音频:{}, 剩余音频待播放时间小于 1 秒, 开始合并后续待合并音频", currentFragmentPlayStatus);


                if (CollectionUtils.size(subFragmentList) == 1) {
                    // 直接提交
                    for (AudioFragment fragment : subFragmentList) {
                        manager.audioManager.submitTask(fragment);
                    }
                    return;
                }

                log.debug("合并前fragmentList:{}", JsonUtils.object2String(fragmentList));
                AudioFragment last = subFragmentList.get(subFragmentList.size() - 1);
                CompositeAudioFragment compositeAudioFragment = MyBeanUtils.copy(last, CompositeAudioFragment.class);
                compositeAudioFragment.setSubFragmentList(subFragmentList);
                // 创建新的 composeTask
                String mergeText = subFragmentList.stream().map(AudioFragment::getText).filter(StringUtils::isNotBlank).collect(Collectors.joining());
                String mergeTemplate = subFragmentList.stream().map(AudioFragment::getTemplate).filter(StringUtils::isNotBlank).collect(Collectors.joining());
                compositeAudioFragment.setText(mergeText);
                compositeAudioFragment.setTemplate(mergeTemplate);
                compositeAudioFragment.setTask(true, manager.audioManager.createComposeTask());
                log.debug("合并 fragment:{}", JsonUtils.object2String(compositeAudioFragment));
                // 替换原先的 fragmentList 中的 fragment
                fragmentList.add(startIndex, compositeAudioFragment);
                fragmentList.add(startIndex + 1, AudioFragment.PLACE_HOLDER_FRAGMENT);
                fragmentList.removeAll(subFragmentList);
                manager.audioManager.submitTask(compositeAudioFragment);
                log.debug("合并后fragmentList:{}", JsonUtils.object2String(fragmentList));
            } catch (Exception e) {
              log.warn("拼接音频处理异常", e);
            } finally {
                lock.unlock();
            }
        }

        private int waitComposeFragmentCount() {
            if (CollectionUtils.isEmpty(fragmentList)) {
                return 0;
            }
            int nextIndex = nextFragmentIndex.get();
            int count = 0;
            for (int i = nextIndex; i < fragmentList.size(); i++) {
                AudioFragment fragment = fragmentList.get(i);
                if (fragment == AudioFragment.PLACE_HOLDER_FRAGMENT || fragment == AudioFragment.COMPLETE_FRAGMENT) {
                    continue;
                }
                if (fragment.isComposeTask && !fragment.submitted.get()) {
                    count ++;
                }
            }
            return count;
        }

        /**
         * 获取当前读取进度
         */
        public double getReadPercent() {
            if (totalReadSize.get() == Integer.MAX_VALUE) {
                return 100;
            }

            int totalSize = getTotalSize();
            if (totalSize == 0) {
                return 0;
            }
            return totalReadSize.get() * 100.0 / totalSize;
        }


        public int getReadDuration() {
            int totalSize = getTotalSize();
            if (totalSize == 0) {
                return 0;
            }
            int totalDuration = totalSize / DialogEngineConstant.MONO_FILE_LENGTH_PER_MS;
            if (totalReadSize.get() >= totalSize){
                return totalDuration;
            }
            return totalReadSize.get() / DialogEngineConstant.MONO_FILE_LENGTH_PER_MS;
        }

        /**
         * 重置播放状态
         */
        public void reset() {
            nextFragmentIndex.set(0);
            totalReadSize.set(0);
            close();
        }

        public void resume() throws IOException {
            log.debug("resume: nextFragmentIndex:{}", nextFragmentIndex.get());
            if (nextFragmentIndex.get() > fragmentList.size()) {
                return;
            }
            AudioFragment fragment = fragmentList.get(Math.max(0, nextFragmentIndex.get() - 1));
            getFragmentPlayStatus(fragment)
                    .ifPresent(fragmentPlayStatus -> resumeStream(fragment, fragmentPlayStatus));
        }

        private void resumeStream(AudioFragment fragment, FragmentPlayStatus fragmentPlayStatus) {
            log.debug("恢复音频流, fragment:{}, fragmentPlayStatus:{}", fragment, fragmentPlayStatus);
            this.currentFragmentPlayStatus = fragmentPlayStatus;
        }

        /**
         * 关闭播放状态, 主要是关闭输入流
         * 同时记录下当前播放进度, 后面可能会恢复
         */
        public void close() {

        }

        private int getTotalSize() {
            return fragmentList.stream()
                    .filter(AudioFragment::isReady)
                    .mapToInt(AudioFragment::getSize)
                    .sum();
        }

        private int doRead(byte[] buffer) throws IOException {
            // 读取音频流至 buffer 中
            if (fragmentList.isEmpty()) {
                return 0;
            }

            if (currentFragmentPlayStatus == null || currentFragmentPlayStatus.isFinish()) {
                if (needWaitingPlaceholder()) {
                    return 0;
                }
                if (nextFragmentIndex.get() >= fragmentList.size()) {
                    log.debug("nextFragmentIndex:{} >= fragmentList.size():{}, 播放结束", nextFragmentIndex.get(), fragmentList.size());
                    return -1;
                }
                AudioFragment fragment = fragmentList.get(nextFragmentIndex.get());
                if (fragment == AudioFragment.COMPLETE_FRAGMENT) {
                    log.debug("COMPLETE_FRAGMENT");
                    return -1;
                }
                Optional<FragmentPlayStatus> streamOptional = getFragmentPlayStatus(fragment);
                if (streamOptional.isPresent()) {
                    nextFragmentIndex.incrementAndGet();
                    resetStream(fragment, streamOptional.get());
                } else {
                    if (waitingReadyTimeout < 1) {
                        waitingReadyTimeout = System.currentTimeMillis() + TIMEOUT_MS;
                    }
                    long now = System.currentTimeMillis();
                    if (now > waitingReadyTimeout) {
                        log.debug("等待音频就绪超时, fragment:{}", JsonUtils.object2String(fragment));
                        return -1;
                    }
                    return 0;
                }
            }

            int readSize = currentFragmentPlayStatus.read(buffer);
            if (readSize < 0) {
                log.debug("读取音频流结束, playStatus:{}", this);
                return 0;
            }

            return readSize;
        }

        private boolean needWaitingPlaceholder() {
            if (nextFragmentIndex.get() >= fragmentList.size()) {
                return false;
            }
            // 如果有占位符, 等待跳过
            AudioFragment nextFragment = fragmentList.get(nextFragmentIndex.get());
            if (nextFragment == AudioFragment.PLACE_HOLDER_FRAGMENT) {
                // 判断后面是在有音频来了
                int nextNextIndex = nextFragmentIndex.get() + 1;
                AudioFragment secondFragment = nextNextIndex < fragmentList.size() ? fragmentList.get(nextNextIndex) : null;
                if (secondFragment == null) {
                    long now = System.currentTimeMillis();
                    if (placeholderTimeout < 1) {
                        placeholderTimeout = now + TIMEOUT_MS;
                    }
                    if (now < placeholderTimeout) {
                        return true;
                    } else {
                        manager.onWaitingTimeout();
                    }
                }
                // 超时 或者后续音频已经到来
                nextFragmentIndex.incrementAndGet();
            }
            return false;
        }

        private void resetStream(AudioFragment fragment, FragmentPlayStatus fragmentPlayStatus) {
            log.debug("重置音频流, fragmentPlayStatus:{}", fragmentPlayStatus);
            currentFragmentPlayStatus = fragmentPlayStatus;
            waitingReadyTimeout = 0;
        }

        private Optional<FragmentPlayStatus> getFragmentPlayStatus(AudioFragment fragment) {
            if (fragment.isReady()) {
                // 处理动态变量为空时, 需要继续播放后续的音频
                if (StringUtils.isNotBlank(fragment.getPath())) {
                    FragmentPlayStatus status = new FragmentPlayStatus(fragment);
                    log.debug("获取音频流成功, status:{}", fragment);
                    return Optional.of(status);
                } else {
                    // 切换后续的fragment
                    log.debug("当前分片音频处理失败, 尝试播放后续的音频，当前fragment:{}", fragment);
                    nextFragmentIndex.incrementAndGet();
                }
            }
            return Optional.empty();
        }

        public boolean allowAppend() {
            if (fragmentList.isEmpty()) {
                return false;
            }

            if (fragmentList.get(fragmentList.size() - 1) == AudioFragment.PLACE_HOLDER_FRAGMENT) {
                return true;
            }

            return appended;
        }

        @Override
        public String toString() {
            return "AudioPlayStatus{" +
                    "nextFragmentIndex=" + nextFragmentIndex +
                    ", totalReadSize=" + totalReadSize +
                    ", appended=" + appended +
                    ", fragmentList=" + fragmentList +
                    '}';
        }

        public String getFullContent() {
            // 获取到已播放完成的文本内容
            if (CollectionUtils.isEmpty(fragmentList)) {
                return "";
            }
            try {
                // 判断是否播放完成, 如果播放完成, 则返回全部内容
                StringBuilder sb = new StringBuilder();
                for (AudioFragment fragment : fragmentList) {
                    if (StringUtils.isNotBlank(fragment.getText())) {
                        sb.append(fragment.getText());
                    }
                }
                return sb.toString();
            } catch (Exception e) {
                log.warn("[LogHub_Warn] 获取播放内容失败", e);
            }
            return "";
        }

        public String getPlayedContent() {
            // 获取到已播放完成的文本内容
            if (CollectionUtils.isEmpty(fragmentList)) {
                return "";
            }
            try {
                // 判断是否播放完成, 如果播放完成, 则返回全部内容
                StringBuilder sb = new StringBuilder();
                Set<AudioFragment> tmp = new HashSet<>();
                for (int i = 0; i < fragmentList.size() && i < nextFragmentIndex.get() - 1; i++) {
                    AudioFragment audioFragment = fragmentList.get(i);
                    tmp.add(audioFragment);
                    if (StringUtils.isNotBlank(audioFragment.getText())) {
                        sb.append(audioFragment.getText());
                    }
                }
                FragmentPlayStatus currentFragmentPlayStatus = this.currentFragmentPlayStatus;
                log.debug("currentFragmentPlayStatus:{}", currentFragmentPlayStatus);
                if (currentFragmentPlayStatus == null) {
                    return sb.toString();
                }
                if (tmp.contains(currentFragmentPlayStatus.getFragment())) {
                    return sb.toString();
                }
                String lastText = currentFragmentPlayStatus.getFragment().getText();
                if (StringUtils.isBlank(lastText)) {
                    return sb.toString();
                }
                // 计算播放进度
                int size = Math.max(1, currentFragmentPlayStatus.getFragment().getSize());
                int percent = currentFragmentPlayStatus.getPosition().get() * 100 / size;
                percent = Math.min(100, percent);
                log.debug("readSize:{}, fragmentSize:{}, percent:{}, text:{}", currentFragmentPlayStatus.getPosition().get(), currentFragmentPlayStatus.getFragment().getSize(), percent, lastText);
                if (percent >= 95) {
                    sb.append(lastText);
                    return sb.toString();
                }
                int lastIndex = lastText.length() * percent / 100;
                return sb.append(lastText, 0, lastIndex).toString();
            } catch (Exception e) {
                log.warn("[LogHub_Warn] 获取播放内容失败", e);
            }
            return "";
        }
    }


    @Data
    public static class FragmentPlayStatus {

        final AudioFragment fragment;

        final String filePath;

        final AtomicInteger position = new AtomicInteger();

        boolean finish = false;

        public FragmentPlayStatus(AudioFragment fragment) {
            this.fragment = fragment;
            this.filePath = fragment.getPath();
        }

        public int read(byte[] buffer) {
            try {
                int readSize = AudioFileReaderFactory.getReader(filePath, fragment.getStartOffset(), fragment.getSize())
                        .read(buffer, position.get(), buffer.length);
                if (readSize > 0) {
                    position.getAndAdd(readSize);
                } else {
                    finish = true;
                }
                return readSize;
            } catch (Exception e) {
                log.warn("读取音频异常", e);
                finish = true;
                return -1;
            }
        }

        public int currentFragmentLeftAudioSize() {
            int endOffset = fragment.getEndOffset();
            int currentPosition = position.get();
            return endOffset - currentPosition;
        }

        public int getSize() throws IOException {
            return AudioFileReaderFactory.getReader(filePath, fragment.getStartOffset(), fragment.getSize()).size();
        }

        public boolean readFinish() throws IOException{
            return finish || position.get() >= getSize();
        }
    }

    /**
     * 音频文件读取器
     */
    public static class AudioFileReader {
        private static final int HEADER_SIZE = 44;  // WAV文件头部大小
        private static final int FRAME_SIZE = 320;  // 音频帧大小


        final LocalDateTime createTime = LocalDateTime.now();
        // 使用 MappedByteBuffer 读取音频文件
        String filePath;

        RandomAccessFile file;

        MappedByteBuffer buffer;

        int beginPosition;

        int endPosition;

        boolean closed;

        boolean mapped;

        final ReadWriteLock lock = new ReentrantReadWriteLock();

        volatile int hitCount;

        public void incHitCount() {
            hitCount ++;
        }

        public AudioFileReader(String filePath, int start, int end) throws IOException {
            log.debug("创建音频文件读取器, 音频文件路径: {}", filePath);

            // 参数验证
            if (StringUtils.isBlank(filePath)) {
                throw new IllegalArgumentException("文件路径不能为空");
            }

            this.filePath = filePath;

            try {
                // 打开文件和通道
                this.file = new RandomAccessFile(filePath, "r");  // 改为只读模式，因为不需要写入

                // 验证文件大小
                long fileSize = file.getChannel().size();
                if (fileSize <= HEADER_SIZE) {
                    throw new IllegalArgumentException("无效的音频文件: 文件过小");
                }

                int size = end - start;
                size = size - (size % FRAME_SIZE);
                // 计算有效数据范围
                this.beginPosition = start;
                this.endPosition = size + start;

                // 映射文件到内存
                this.buffer = file.getChannel().map(
                        FileChannel.MapMode.READ_ONLY,
                        beginPosition,
                        size
                );
                this.mapped = true;
                log.debug("音频文件读取器初始化完成, 有效数据范围: {} - {}", beginPosition, endPosition);
            } catch (IOException e) {
                close();
                throw new IOException("初始化音频文件读取器失败: " + e.getMessage(), e);
            }
        }

        private void closeQuietly() {
            lock.writeLock().lock();
            try {
                closed = true;
                if (file != null) {
                    file.close();
                    file = null;
                }
                buffer = null;
            } catch (IOException e) {
                log.warn("关闭音频文件资源时发生异常", e);
            } finally {
                lock.writeLock().unlock();
            }
        }

        public void close() {
            closeQuietly();
        }

        public int read(byte[] audioFrame, int position, int size) {
            lock.readLock().lock();
            try {
                if (closed) {
                    throw new IllegalStateException("音频文件已关闭");
                }
                if (position >= buffer.capacity()) {
                    return -1;
                }

                if (position == 0) {
                    incHitCount();
                }

                // 从 MappedByteBuffer 中读取音频数据
                int availableSize = buffer.capacity() - position;
                int readSize = Math.min(size, availableSize);
                ByteBuffer readOnlyBuffer = buffer.asReadOnlyBuffer();
                readOnlyBuffer.position(position);
                readOnlyBuffer.get(audioFrame, 0, readSize);
                return readSize;
            } catch (Exception e) {
                log.warn("读取音频文件时发生错误, 音频文件路径:{}, position:{}, size:{}, buffer.capacity:{} ",
                        filePath, position, size, buffer.capacity(), e);
                throw e;
            } finally {
                lock.readLock().unlock();
            }
        }

        public int size() {
            return endPosition - beginPosition;
        }
    }
}
