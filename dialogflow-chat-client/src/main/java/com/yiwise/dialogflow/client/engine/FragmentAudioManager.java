package com.yiwise.dialogflow.client.engine;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.google.common.collect.Tables;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.constant.AuditionTtsConfigConstant;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.client.config.DialogEngineConfig;
import com.yiwise.dialogflow.engine.share.AnswerAudioMiddleResult;
import com.yiwise.dialogflow.engine.share.BotAudioConfig;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.tts.DialogVariablesPlaceholder;
import com.yiwise.middleware.tts.TtsConfig;
import com.yiwise.middleware.tts.convert.CustomVarConvertItem;
import com.yiwise.middleware.tts.convert.InterpretConvert;
import com.yiwise.middleware.tts.convert.InterpretConvertLoader;
import com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import com.yiwise.middleware.tts.utils.TtsCacheUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基于分片的音频管理器
 * 所谓分片, 是不提前将多个片段的音频合并为一个大音频, 而是按照分片顺序播放
 * 播放也是需要根据
 */
@Slf4j
public class FragmentAudioManager implements AudioManager {
    private static final ExecutorService executeService = new ThreadPoolExecutor(200, 1000, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(20480));

    private static final Integer AUDIO_TYPE_COMPOSE = 1;
    private static final ObjectStorageHelper objectStorageHelper = AppContextUtils.getBean(ObjectStorageHelper.class);

    private final ApplicationExecutor executor;

    private final BotMetaData botData;

    private final Long tenantId;

    /**
     * <变量|短句，实际值，本地录音地址>
     */
    private Table<String, String, String> varAudioCacheTable;

    private final Map<String, AudioFragment> answerAudioFragmentMap = new ConcurrentHashMap<>();

    private Map<String, String> properties;

    private Map<String, Map<String, String>> userVariableMap = Collections.emptyMap();

    /**
     * 重试次数
     */
    private final static Integer RETRY_TIMES = 3;
    /**
     * 发音转换器
     */
    private InterpretConvert interpretConvert;

    /**
     * 变量名称和发音类型的映射
     */
    private Map<String, VarInterpretTypeEnum> varNameInterpretTypeMap;

    /**
     * <动态变量名称,当前值>
     */
    private Map<String, String> dynamicMap;

    /**
     * 自定义+动态变量的集合
     */
    private Map<String, String> varMap;

    /**
     * <变量名称, 引用变量的答案id集合>
     */
    private Map<String, Set<String>> varNameAnswerIdSetMap;

    private Map<String, AnswerFragmentMapping> answerIdResultMap;

    private List<CustomVarConvertItem> customVarConvertItemList;

    private final boolean isMagicActivityCallout;

    private final BotAudioConfig botAudioConfig;

    private Map<String, String> magicTemplateVarValueMap;

    private final Consumer<AudioFragment> downloadTask = (fragment) -> {
        String localPath = null;
        try {
            if (StringUtils.isNotBlank(fragment.getOssKey())) {
                localPath = downloadAudio(fragment.getOssKey());
            }
        } finally {
            fragment.complete(localPath);
        }
    };

    private final Consumer<AudioFragment> composeTask = (fragment) -> {
        log.debug("startCompose:{}", JsonUtils.object2String(fragment));
        String localPath = null;
        try {
            localPath = composeAudio(fragment.getType(), fragment.getTemplate(), fragment.streaming, fragment.getHistoryList());
        } finally {
            fragment.complete(localPath);
        }
    };

    public FragmentAudioManager(Long tenantId, BotMetaData botData, ApplicationExecutor executor, Long callJobId, String customTtsVoice) {
        this.tenantId = tenantId;
        this.botData = botData;
        this.executor = executor;
        this.isMagicActivityCallout = V3BotTypeEnum.isMagicTemplate(botData.getBotType()) && RobotSnapshotUsageTargetEnum.CALL_OUT.equals(botData.getUsageTarget());
        BotAudioConfig tmpConfig =  botData.getBotAudioConfig();
        if (isMagicActivityCallout) {
            MagicActivityConfig magicActivityConfig = BotMetaDataLoader.getMagicActivityConfig(botData.getBotId(), botData.getUsageTarget(), botData.getVersion(), callJobId);
            if (magicActivityConfig == null) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "活动配置不存在");
            }
            tmpConfig = magicActivityConfig.getBotAudioConfig();
            magicTemplateVarValueMap = magicActivityConfig.getTemplateVarNameValueMap();
        }
        this.botAudioConfig =  tmpConfig;
        if (StringUtils.isNotBlank(customTtsVoice)) {
            try {
                TtsVoiceEnum ttsVoiceEnum = TtsVoiceEnum.valueOf(customTtsVoice);
                botAudioConfig.setTtsVoice(customTtsVoice);
                AuditionTtsConfigConstant.getDefaultSpeed(customTtsVoice).ifPresent(botAudioConfig::setTtsSpeed);
                AuditionTtsConfigConstant.getDefaultVolume(customTtsVoice).ifPresent(botAudioConfig::setTtsVolume);
            } catch (Exception e) {
                log.warn("无效的音色枚举={}", customTtsVoice);
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "自定义音色参数错误");
            }
        }
        log.debug("botAudioConfig:{}", JsonUtils.object2String(botAudioConfig));
    }

    @Override
    public boolean prepare(Map<String, String> originProperties,
                           Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                           Function<Long, Map<String, String>> getFamilyVariableAudioFunc,
                           boolean multiThreadLoad) {
        log.debug("开始录音预处理, properties={}", originProperties);
        interpretConvert = InterpretConvertLoader.load(() -> Optional.of(getTtsConfig())
                .map(TtsConfig::getVoice)
                .orElse(null));
        this.customVarConvertItemList = VarValueTtsReplaceDataLoader.getByTenantId(tenantId);
        log.debug("加载发音转换器={}", interpretConvert.getClass().getSimpleName());
        this.varNameInterpretTypeMap = botData.getVarNameInterpretTypeMap();
        this.properties = processProperties(originProperties);
        this.dynamicMap = new HashMap<>();
        resetVarMap();
        log.debug("聚合后的properties={}", this.properties);
        this.varNameAnswerIdSetMap = new HashMap<>();
        this.answerIdResultMap = new ConcurrentHashMap<>();
        this.varAudioCacheTable = Tables.synchronizedTable(HashBasedTable.create());
        prepareUserVariable(getUserVariableByRecordIdFunc);

        // 加载百家姓数据
        Long recordUserId = botData.getBotAudioConfig().getRecordUserId();
        loadFamilyDetailIfNeeded(getFamilyVariableAudioFunc.apply(recordUserId));

        try {
            //下载录音并保存到本地
            asyncPerLoadAudio(botData.getAllAnswerList(), multiThreadLoad);

            // 等待异步处理完成
            List<CompletableFuture<Boolean>> futures = this.answerAudioFragmentMap.values().stream()
                            .map(AudioFragment::getFuture)
                            .collect(Collectors.toList());

            CompletableFuture<?> allFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{}));
            // 阻塞最长 5 分钟
            try {
                allFuture.get(10, TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                log.error("录音预处理超时", e);
            }
        } catch (Exception e) {
            log.error("加载录音文件失败, botId = [{}]", botData.getBotId(), e);
        }

        // 检查是否音频是否全部完成
        boolean containsFailed = answerAudioFragmentMap.values().stream().anyMatch(fragment -> {
            if (fragment.failed) {
                // 判断是否包含动态变量, 如果是动态变量忽略
                // todo 后续需要重新梳理下预处理逻辑, 不应该在这里这样处理
                return Objects.nonNull(fragment.getOriginElement()) && !fragment.getOriginElement().isDynamicVariable();
            }
            return false;
        });

        if (containsFailed) {
            return true;
        }

        log.info("已完成录音预处理");
        return true;
    }

    public Consumer<AudioFragment> createComposeTask() {
        return composeTask;
    }

    public void submitTask(AudioFragment fragment) {
        if (fragment.submitted.compareAndSet(false, true)) {
            log.debug("submit:{}", JsonUtils.object2String(fragment));
            executor.execute("通话中音频分片任务:" + fragment.getText(), fragment::executeTask);
        }
    }

    private Map<String, String> processProperties(Map<String, String> originProperties) {
        Map<String, String> tmpMap = new HashMap<>();
        // 将模板变量值设置到properties中
        if (MapUtils.isNotEmpty(botData.getTemplateVarNameValueMap())) {
            botData.getTemplateVarNameValueMap().forEach((k, v) -> {
                if (Objects.nonNull(v)) {
                    tmpMap.put(k, v);
                }
            });
        }
        if (MapUtils.isNotEmpty(magicTemplateVarValueMap)) {
            magicTemplateVarValueMap.forEach((k, v) -> {
                if (Objects.nonNull(v)) {
                    tmpMap.put(k, v);
                }
            });
        }
        if (MapUtils.isNotEmpty(originProperties)) {
            originProperties.forEach((k, v) -> {
                if (Objects.nonNull(v)) {
                    tmpMap.put(k, v);
                }
            });
        }
        Map<String, String> result = new HashMap<>();

        tmpMap.forEach((k, v) -> {
            if (StringUtils.isNotBlank(v) && v.contains("${")) {
                result.put(k, renderSentence(v, tmpMap));
            } else {
                result.put(k, v);
            }
        });
        return result;
    }

    /**
     * 获取录音地址
     */
    @Override
    public String getAudioLocalPath(AnswerResult answer) {
        return null;
    }

    @Override
    public List<AudioFragment> getAudioFragments(AnswerResult answer) {
        if (CollectionUtils.isEmpty(answer.getAnswerElementList())) {
            return Collections.emptyList();
        }

        // 从缓存中获取到分片数据
        List<AudioFragment> fragments;
        AnswerFragmentMapping mapping = answerIdResultMap.get(answer.getId());
        if (mapping == null) {
            return createAudioFragments(answer, false, null);
        }

        fragments = mapping.getAudioFragments().stream()
                .map(fragment -> {
                    AudioFragment oldFragment = answerAudioFragmentMap.get(fragment.getCacheKey());
                    if (oldFragment != null) {
                        return oldFragment;
                    }
                    submitTask(fragment);
                    return fragment;
                })
                .collect(Collectors.toList());
        log.debug("getAudioFragments, answer:{}, fragments:{}",
                JsonUtils.object2String(answer), JsonUtils.object2String(fragments));
        return fragments;
    }

    @Override
    public List<AudioFragment> createAudioFragments(AnswerResult answer, boolean streaming, List<ChatHistoryItem> historyList) {
        return createAudioFragments(answer, streaming, historyList, false);
    }


    List<AudioFragment> createAudioFragments(AnswerResult answer,
                                             boolean streaming,
                                             List<ChatHistoryItem> historyList,
                                             boolean delayCompose) {
        if (CollectionUtils.isEmpty(answer.getAnswerElementList())) {
            return Collections.emptyList();
        }
        List<AudioFragment> fragments = createFragmentsFromElements(answer.getTemplate(),
                answer.getAnswerElementList(),
                false,
                streaming,
                historyList)
                .stream()
                .map(fragment -> {
                    AudioFragment oldFragment = answerAudioFragmentMap.get(fragment.getCacheKey());
                    if (oldFragment != null) {
                        return oldFragment;
                    }
                    if (fragment.taskReady()) {
                        if (fragment.isComposeTask) {
                            if (!delayCompose) {
                                submitTask(fragment);
                            }
                        } else {
                            submitTask(fragment);
                        }
                    }
                    return fragment;
                })
                .collect(Collectors.toList());
        log.debug("createAudioFragments, answer:{}, fragments:{}",
                JsonUtils.object2String(answer), JsonUtils.object2String(fragments));
        return fragments;
    }

    // 准备用户变量
    private void prepareUserVariable(Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc) {
        try {
            if (BooleanUtils.isTrue(botData.getBotAudioConfig().getEnableVariableAudio())
                    && Objects.nonNull(getUserVariableByRecordIdFunc)) {
                this.userVariableMap = getUserVariableByRecordIdFunc.apply(botData.getBotAudioConfig().getRecordUserId());
            } else {
                log.debug("变量录音功能未开启, 无需加载变量录音信息, recordUserId={}", botData.getBotAudioConfig().getRecordUserId());
            }
        } catch (Exception e) {
            log.warn("加载变量录音信息异常, 异常信息={}", e.getMessage());
        }
    }

    private void loadFamilyDetailIfNeeded(Map<String, String> familyVariableAudioMap) {
        try {
            // 只有真人录音模式下需要加载百家姓中的“姓名_总”
            boolean isManMade = Optional.of(botData).map(BotMetaData::getBotAudioConfig)
                    .map(BotAudioConfig::getAudioType)
                    .map(DialogVoiceTypeEnum.MAN_MADE.getCode()::equals)
                    .orElse(false);
            if (!isManMade) {
                log.info("非真人录音模式,不加载百家姓数据");
                return;
            }
            List<AnswerAudioMiddleResult> allAnswerList = botData.getAllAnswerList();
            if (CollectionUtils.isEmpty(allAnswerList)) {
                return;
            }

            // 话术中包含变量"姓名_总"则需要加载百家姓中对应的姓名录音
            boolean needLoadFamilyDetail = false;
            String bossKey = DialogVariablesPlaceholder.CUSTOMER_NAME_FAMILY_NAME_BOSS;
            for (AnswerAudioMiddleResult middleResult : allAnswerList) {
                for (AnswerPlaceholderElement element : middleResult.getElementList()) {
                    if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(element.getType()) && bossKey.equals(element.getValue())) {
                        needLoadFamilyDetail = true;
                    }
                }
            }
            if (!needLoadFamilyDetail) {
                log.info("话术变量中不包含‘姓名_总’变量,不加载百家姓数据");
                return;
            }

            if (MapUtils.isEmpty(familyVariableAudioMap)) {
                return;
            }
            String bossName = properties.get(bossKey);
            if (StringUtils.isBlank(bossName)) {
                return;
            }
            String audioUrl = familyVariableAudioMap.get(bossName);
            String path = null;
            log.info("bossName:{}, bossNameAudio:{}", bossName, audioUrl);
            if (StringUtils.isNotBlank(audioUrl)) {
                // 找到音频,下载到本地,这里要是下载失败,会返回null
                path = downloadAudio(audioUrl);
            }
            if (StringUtils.isBlank(path)) {
                // 没有找到对应音频或者录音下载失败，则用tts合成“哎”
                path = composeAudio(TextPlaceholderTypeEnum.PLACE_HOLDER, "哎", false, null);
            }
            if (StringUtils.isNotBlank(path)) {
                // 这里将“姓名_总”的音频提前放入缓存，后面变量合成时就会直接拿到
                varAudioCacheTable.put(bossKey, bossName, path);
            }
        } catch (Exception e) {
            log.warn("加载百家姓数据异常, 异常信息={}", e.getMessage());
        }
    }

    /**
     * 录音提前加载至本地
     */
    private void asyncPerLoadAudio(List<AnswerAudioMiddleResult> answerAudios, boolean multiThread) {
        if (CollectionUtils.isNotEmpty(answerAudios)) {
            //处理element中所有录音
            List<AudioFragment> allFragmentList = new ArrayList<>();
            answerAudios.forEach(audioMiddleResult -> {
                Set<String> variableSet = audioMiddleResult.getVariableSet();
                if (CollectionUtils.isNotEmpty(variableSet)) {
                    for (String varName : variableSet) {
                        varNameAnswerIdSetMap.computeIfAbsent(varName, k -> new HashSet<>()).add(audioMiddleResult.getId());
                    }
                }
                List<AudioFragment> fragments = createFragmentsFromElements(audioMiddleResult.getTemplate(),
                        audioMiddleResult.getElementList(), true, false, null);

                AnswerFragmentMapping mapping = new AnswerFragmentMapping();
                mapping.setAnswerAudioMiddleResult(audioMiddleResult);
                mapping.setAudioFragments(fragments);
                answerIdResultMap.put(audioMiddleResult.getId(), mapping);
                allFragmentList.addAll(fragments);
            });

            final Queue<AudioFragment> taskQueue = new ConcurrentLinkedQueue<>(allFragmentList);

            Runnable runTask = () -> {
                while (!taskQueue.isEmpty()) {
                    AudioFragment fragment = taskQueue.poll();
                    if (fragment != null) {
                        processSublist(Collections.singletonList(fragment));
                    }
                }
            };

            int concurrency = 10;
            if (multiThread) {
                log.debug("开启多线程加载");
                String mdcLog = MDC.get("MDC_LOG_ID");
                for (int i = 0; i < concurrency; i++) {
                    executeService.submit(() -> {
                        MDC.put("MDC_LOG_ID", mdcLog);
                        try {
                            runTask.run();
                        } catch (Exception e) {
                            log.warn("[LogHub_Warn]预处理音频失败", e);
                        } finally {
                            MDC.remove("MDC_LOG_ID");
                        }
                    });
                }
            }

            // 当前线程也执行, 防止线程池排队
            runTask.run();
        }
    }

    private void processSublist(List<AudioFragment> subList) {
        if (CollectionUtils.isEmpty(subList)) {
            return;
        }
        for (AudioFragment fragment : subList) {
            if (answerAudioFragmentMap.putIfAbsent(fragment.getCacheKey(), fragment) == null) {
                if (fragment.taskReady()) {
                    try {
                        fragment.executeTask();
                    } catch (Exception e) {
                        log.warn("[LogHub_Warn]预处理音频失败", e);
                    }
                } else {
                    fragment.complete(null);
                }
            }
        }
    }

    // 根据模板和元素列表生成音频片段
    // 每次都是生成新的音频片段, 但是没有真正提交任务,
    // 上游会判断是否已存在相同的片段(按照真实文本内容), 然后复用
    private List<AudioFragment> createFragmentsFromElements(String template,
                                                            List<AnswerPlaceholderElement> elementList,
                                                            boolean isPreload,
                                                            boolean streaming,
                                                            List<ChatHistoryItem> historyList) {
        if (CollectionUtils.isEmpty(elementList)) {
            return Collections.emptyList();
        }

        boolean containsVariable = elementList.stream()
                .map(AnswerPlaceholderElement::getType)
                .anyMatch(TextPlaceholderTypeEnum.PLACE_HOLDER::equals);

        boolean containsDynamic = containsVariable
                && elementList.stream()
                .filter(item -> TextPlaceholderTypeEnum.PLACE_HOLDER.equals(item.getType()))
                .anyMatch(item -> botData.getDynamicVariableNameSet().contains(item.getValue()));

        boolean containsSeparator = elementList.stream()
                .map(AnswerPlaceholderElement::getType)
                .anyMatch(TextPlaceholderTypeEnum.SEPARATOR::equals);

        // 合成音
        if (AUDIO_TYPE_COMPOSE.equals(botData.getBotAudioConfig().getAudioType())
                && containsVariable
                && !containsSeparator) {
            // 通话中 或 包不含动态变量, 直接合成整句
            if (!isPreload || !containsDynamic) {
                return Collections.singletonList(composeFullSentence(template));
            }
            // 通话前 + 包含动态变量, 暂时不需要提前处理
        }

        List<AudioFragment> fragments = new ArrayList<>();
        elementList.forEach(element -> convertElement2Fragment(element, streaming, historyList).ifPresent(fragments::add));

        return fragments;
    }

    /**
     * 整句合成
     */
    private AudioFragment composeFullSentence(String template) {
        String realAnswer = interpretConvert.convertSentence(template, varMap, varNameInterpretTypeMap, customVarConvertItemList);
        AudioFragment fragment = new AudioFragment();
        fragment.setTemplate(template);
        fragment.setText(renderSentence(template, varMap));
        fragment.setTask(true, (fg) -> {
            String localPath = null;
            try {
                localPath = ttsComposeVar(realAnswer, false, null);
            } finally {
                fg.complete(localPath);
            }
        });
        return fragment;
    }

    private Optional<AudioFragment> convertElement2Fragment(AnswerPlaceholderElement element, boolean streaming, List<ChatHistoryItem> historyList) {
        Set<TextPlaceholderTypeEnum> downloadTypeSet = new HashSet<>(Arrays.asList(TextPlaceholderTypeEnum.TEXT, TextPlaceholderTypeEnum.SEPARATOR));
        Set<TextPlaceholderTypeEnum> composeTypeSet = new HashSet<>(Arrays.asList(TextPlaceholderTypeEnum.TTS_SENTENCE, TextPlaceholderTypeEnum.PLACE_HOLDER));

        if (isMagicActivityCallout && AUDIO_TYPE_COMPOSE.equals(botAudioConfig.getAudioType())) {
            downloadTypeSet = new HashSet<>(Collections.singletonList(TextPlaceholderTypeEnum.SEPARATOR));
            composeTypeSet = new HashSet<>(Arrays.asList(TextPlaceholderTypeEnum.TEXT, TextPlaceholderTypeEnum.TTS_SENTENCE, TextPlaceholderTypeEnum.PLACE_HOLDER));
        }

        // 文本和分隔符类型(停顿时长)都是直接下载音频
        if (downloadTypeSet.contains(element.getType()) && StringUtils.isNotBlank(element.getUrl())) {
            AudioFragment fragment = new AudioFragment();
            fragment.setTemplate(element.getValue());
            fragment.setOriginElement(element);
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                fragment.setText(element.getRealValue());
            } else {
                fragment.setText("");
            }
            fragment.setOssKey(element.getUrl());
            fragment.setTask(false, downloadTask);
            return Optional.of(fragment);
        } else if (composeTypeSet.contains(element.getType())) {
            AudioFragment fragment = new AudioFragment();
            fragment.setStreaming(streaming);
            fragment.setHistoryList(historyList);
            fragment.setOriginElement(element);
            fragment.setTemplate(element.getValue());
            String realValue = element.getRealValue();
            if (TextPlaceholderTypeEnum.TTS_SENTENCE.equals(element.getType())) {
                realValue = renderSentence(element.getValue(), varMap);
            } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(element.getType())) {
                realValue = renderVar(element.getValue(), varMap);
            }
            fragment.setText(realValue);
            fragment.setTask(true, composeTask);
            return Optional.of(fragment);
        } else {
            log.debug("不支持的音频类型, element={}", element);
        }
        return Optional.empty();
    }

    private String renderSentence(String value, Map<String, String> varMap) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(value, false);
        StringBuilder sb = new StringBuilder();
        for (TextPlaceholderElement element : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                sb.append(element.getValue());
            } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(element.getType())) {
                sb.append(varMap.getOrDefault(element.getValue(), ""));
            } else {
                sb.append(element.getValue());
            }
        }
        return sb.toString();
    }

    private String renderVar(String value, Map<String, String> varMap) {
        return varMap.getOrDefault(value, "");
    }

    /**
     * 下载录音保存到本地
     * @param url 录音地址
     */
    private String downloadAudio(String url) {
        Throwable throwable = null;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                return doDownloadAudio(botData.getBotId(), botData.getVersion(), url);
            } catch (Exception e) {
                log.error("下载文件失败, 出错url = [{}], 剩余重试次数:{}", url,  RETRY_TIMES - i, e);
                throwable = e;
            }
        }
        log.error("下载文件失败, 出错url = [{}]", url, throwable);
        return null;
    }

    private static String doDownloadAudio(Long botId, Integer version, String url) throws IOException {
        String localKey = getLocalAudioPathPrefix(version, url);
        //创建本地录音文件，录音信息保存到本地缓存中
        if (!fileExist(localKey) || fileSizeError(localKey)) {
            if (fileExist(localKey)) {
                try {
                    FileUtils.forceDelete(new File(localKey));
                } catch (Exception e) {
                    log.warn("删除文件错误, 文件:{}", localKey, e);
                }
            }
            String tmpLocalKey = localKey + "." + MyRandomStringUtils.getRandomEasyStringByLength(10);
            log.info("开始下载录音文件,localKey:{}, tmpLocalKey:{} fileUrl:{},botId:{}", localKey, tmpLocalKey, url, botId);

            File tmpFile = new File(tmpLocalKey);
            if (!tmpFile.getParentFile().exists() && !tmpFile.getParentFile().mkdirs()) {
                log.debug("创建文件夹失败, localKey={}, tmpLocalKey:{}", localKey, tmpLocalKey);
            }
            // 下载文件, 并发情况下, 会重复下载, 但是不影响最终的文件写入
            byte[] bytes = objectStorageHelper.downloadToBytes(url);
            try (FileOutputStream fos = new FileOutputStream(tmpFile)) {
                fos.write(bytes);
            }
            try {
                File realFile = Files.createFile(Paths.get(localKey)).toFile();
                if (!tmpFile.renameTo(realFile)) {
                    log.warn("文件重命名失败, localKey={}, tmpLocalKey:{}", localKey, tmpLocalKey);
                    FileUtils.forceDelete(tmpFile);
                    throw new RuntimeException("文件重命名失败");
                }
            } catch (FileAlreadyExistsException e) {
                FileUtils.forceDelete(tmpFile);
            }
        }
        return localKey;
    }

    // 文件存在, 但是文件 size 为 0
    // 偶尔一些文件是这种情况, 但是还没有找到问题来源, 先兼容处理一下
    private static boolean fileSizeError(String localKey) {
        if (!fileExist(localKey)) {
            return false;
        }
        File file = new File(localKey);
        boolean fileSizeError = file.length() < 1;
        if (fileSizeError) {
            log.warn("音频文件异常, size 为 0, :{}", localKey);
        }
        return fileSizeError;
    }

    private static boolean fileExist(String localPath) {
        try {
            if (StringUtils.isBlank(localPath)) {
                return false;
            }
            File file = new File(localPath);
            return file.exists();
        } catch (Exception ignore) {}
        return false;
    }

    private static String getLocalAudioPathPrefix(Integer version, String url) {
        return String.format("%s%s/%s", DialogEngineConfig.LOCAL_AUDIO_ROOT_PATH, version, url);
    }

    /**
     * 合成变量或短句
     */
    private String composeAudio(TextPlaceholderTypeEnum type, String key, boolean streaming, List<ChatHistoryItem> historyList) {
        String ttsText = "";
        // 纯变量
        if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(type) && varMap.containsKey(key)) {
            // 先从用户变量中获取
            String filePath = getVariableAudioFilePath(key);
            if (StringUtils.isNotBlank(filePath)) {
                log.info("从用户变量中获取变量=[{}]值=[{}]的录音地址=[{}]", key, varMap.get(key), filePath);
                return filePath;
            }
            ttsText = interpretConvert.convertVar(key, varMap, varNameInterpretTypeMap, customVarConvertItemList);
        }
        // 含变量短句
        if (TextPlaceholderTypeEnum.TTS_SENTENCE.equals(type)) {
            ttsText = interpretConvert.convertSentence(key, varMap, varNameInterpretTypeMap, customVarConvertItemList);
        }

        if (StringUtils.isBlank(ttsText)) {
            log.debug("key={}内容为空", key);
            return null;
        }
        String existsFilePath = varAudioCacheTable.get(key, ttsText);
        if (StringUtils.isNotBlank(existsFilePath)) {
            return existsFilePath;
        }
        String path = ttsComposeVar(ttsText, streaming, historyList);
        if (StringUtils.isNotBlank(path)) {
            varAudioCacheTable.put(key, ttsText, path);
            return path;
        }
        return path;
    }

    private String getVariableAudioFilePath(String text) {
        //查询变量录音库中录音师的所有录音
        //如果录音师的录音中存在对应变量录音，返回对应录音地址
        if (userVariableMap.containsKey(text)
                && userVariableMap.get(text).containsKey(varMap.get(text))) {
            String url = userVariableMap.get(text).get(varMap.get(text));
            return downloadAudio(url);
        }
        return null;
    }

    /**
     * tts合成录音
     */
    private String ttsComposeVar(String text, boolean streaming, List<ChatHistoryItem> historyList) {
        try {
            return TtsCacheUtils.composeText(getTtsConfig(), text, streaming, historyList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取tts配置
     */
    private TtsConfig getTtsConfig() {
        TtsConfig ttsConfig = new TtsConfig();
        ttsConfig.setVolume(botAudioConfig.getTtsVolume());
        ttsConfig.setSpeed(botAudioConfig.getTtsSpeed());
        ttsConfig.setVoice(CodeDescEnum.getFromNameOrNull(TtsVoiceEnum.class, botAudioConfig.getTtsVoice()));
        return ttsConfig;
    }

    private void resetVarMap() {
        Map<String, String> varMap = new HashMap<>(properties);
        varMap.putAll(dynamicMap);
        this.varMap = varMap;
    }

    @Override
    public void doOnDynamicVarMapMayChanged(Map<String, String> newMap) {
        if (Objects.isNull(newMap)) {
            newMap = Collections.emptyMap();
        }
        log.info("动态变量集合={}", newMap);

        Map<String, String> oldMap = this.varMap;
        this.dynamicMap = newMap;
        resetVarMap();

        // 检查一下哪些动态变量的值更新了,获取需要重新生成的答案id
        Set<String> needReGenerateAnswerIdSet = getNeedReGenerateAnswerIdSet(oldMap);
        if (CollectionUtils.isEmpty(needReGenerateAnswerIdSet)) {
            log.debug("没有需要重新生成的答案");
            return;
        }

        List<AnswerFragmentMapping> answerAudioMiddleResultList = needReGenerateAnswerIdSet.stream()
                .map(answerIdResultMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 重新合成+拼接音频
        for (AnswerFragmentMapping fragmentMapping : answerAudioMiddleResultList) {
            AnswerAudioMiddleResult answerAudioMiddleResult = fragmentMapping.getAnswerAudioMiddleResult();
            log.info("重新生成答案id={} fragmentMapping:{}", answerAudioMiddleResult.getId(), JsonUtils.object2String(fragmentMapping));

            // 对于动态变量变更的文本, 重新生成
            List<AudioFragment> fragments = createFragmentsFromElements(answerAudioMiddleResult.getTemplate(),
                    answerAudioMiddleResult.getElementList(), false, false, null);
            fragmentMapping.setAudioFragments(fragments);

            for (AudioFragment fragment : fragments) {
                answerAudioFragmentMap.put(fragment.getCacheKey(), fragment);
                if (fragment.taskReady()) {
                    executor.execute("重新生成音频分片任务", fragment::executeTask);
                } else {
                    fragment.complete(null);
                }
            }
        }
    }

    private Set<String> getNeedReGenerateAnswerIdSet(Map<String, String> oldMap) {
        Set<String> needReGenerateAnswerIdSet = new HashSet<>();
        for (Map.Entry<String, String> entry : dynamicMap.entrySet()) {
            String varName = entry.getKey();
            String newValue = StringUtils.trimToEmpty(entry.getValue());
            String oldValue = StringUtils.trimToEmpty(oldMap.get(varName));
            if (!StringUtils.equals(oldValue, newValue)) {
                log.info("动态变量={}更新 旧值={} 新值={}", varName, oldValue, newValue);
                Set<String> answerIdSet = varNameAnswerIdSetMap.get(varName);
                if (CollectionUtils.isNotEmpty(answerIdSet)) {
                    needReGenerateAnswerIdSet.addAll(answerIdSet);
                }
            }
        }
        return needReGenerateAnswerIdSet;
    }

    @Data
    private static class AnswerFragmentMapping {

        AnswerAudioMiddleResult answerAudioMiddleResult;

        List<AudioFragment> audioFragments;
    }
}
