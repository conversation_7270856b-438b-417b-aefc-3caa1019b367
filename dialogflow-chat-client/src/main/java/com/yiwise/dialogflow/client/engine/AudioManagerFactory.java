package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;

public class AudioManagerFactory {

    public static AudioManager createAudioManager(Long tenantId, BotMetaData botMetaData, ApplicationExecutor executor) {
        return createAudioManager(tenantId, 0L, null, botMetaData, executor);
    }

    public static AudioManager createAudioManager(Long tenantId, Long calLJobId, String customTtsVoice, BotMetaData botMetaData, ApplicationExecutor executor) {
        if (RobotSnapshotUsageTargetEnum.TEXT_TEST.equals(botMetaData.getUsageTarget())) {
            return new TextTestAudioManager();
        }
        return new FragmentAudioManager(tenantId, botMetaData, executor, calLJobId, customTtsVoice);
    }

    public static AudioManager createAudioManager(BotMetaData botMetaData, ApplicationExecutor executor) {
        return createAudioManager(0L, botMetaData, executor);
    }
}
