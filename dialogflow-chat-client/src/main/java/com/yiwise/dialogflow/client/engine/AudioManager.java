package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.tts.model.ChatHistoryItem;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface AudioManager {

    boolean prepare(Map<String, String> properties,
                    Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                    Function<Long, Map<String, String>> getFamilyVariableAudioFunc,
                    boolean multiThread);

    default boolean prepare(Map<String, String> properties,
                            Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                            Function<Long, Map<String, String>> getFamilyVariableAudioFunc) {
        return prepare(properties, getUserVariableByRecordIdFunc, getFamilyVariableAudioFunc, false);
    }

    default boolean prepare(Map<String, String> properties,
                            Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc) {
        return prepare(properties, getUserVariableByRecordIdFunc, (recordId) -> new HashMap<>());
    }

    default boolean prepare(Map<String, String> properties) {
        return prepare(properties, (recordUserId) -> new HashMap<>());
    }

    String getAudioLocalPath(AnswerResult answer);

    List<AudioFragment> getAudioFragments(AnswerResult answer);

    default List<AudioFragment> createAudioFragments(AnswerResult answer) {
        return createAudioFragments(answer, false, null);
    }

    default List<AudioFragment> createAudioFragments(AnswerResult answer, boolean streaming, List<ChatHistoryItem> historyList) {
        return Collections.emptyList();
    }

    void doOnDynamicVarMapMayChanged(Map<String, String> newMap);
}
