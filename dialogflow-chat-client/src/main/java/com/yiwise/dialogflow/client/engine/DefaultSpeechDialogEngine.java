package com.yiwise.dialogflow.client.engine;


import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.base.common.thread.ApplicationExecutorHolder;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.client.asr.AsrResultFilter;
import com.yiwise.dialogflow.client.asr.AsrResultFilterFactory;
import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import com.yiwise.dialogflow.client.engine.asr.BaseAsrContext;
import com.yiwise.dialogflow.client.engine.asr.V3AsrContextFactory;
import com.yiwise.dialogflow.client.listener.*;
import com.yiwise.dialogflow.client.model.engine.SimpleAiSayResultInfo;
import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import com.yiwise.dialogflow.client.service.BotChatService;
import com.yiwise.dialogflow.client.service.LLMChatClient;
import com.yiwise.dialogflow.client.utils.AutoAnswerRobotDetector;
import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.action.*;
import com.yiwise.dialogflow.engine.share.enums.*;
import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import com.yiwise.dialogflow.engine.share.request.*;
import com.yiwise.dialogflow.engine.share.response.*;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import javaslang.control.Try;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.engine.share.model.SimpleChatHistory.ROLE_USER;

/**
 * 语音对话引擎, 原v3ChatController中所有和对话接口交互相关逻辑, 并提供一些扩展点
 */
@Slf4j
public class DefaultSpeechDialogEngine implements SpeechDialogEngine, AudioPlayEventListener, UserVoiceEventListener, FastHangupListener, KeyCaptureListener {

    private static final BotChatService botChatService = AppContextUtils.getBean(BotChatService.class);

    private static final LLMChatClient llmChatService = AppContextUtils.getBean(LLMChatClient.class);

    private final EventSender eventSender = new EventSender();

    protected final AtomicInteger sequence = new AtomicInteger();

    /**
     * 用户打断相关设置
     */
    @Getter
    private final InterruptConfig interruptConfig;

    private final InterruptConfig preInterruptConfig;

    /**
     * 通话开始时间
     */
    private long startTimestamp;

    /**
     * sessionId
     */
    private final SessionInfo session;

    /**
     * 录音播放
     */
    protected final AudioPlayManager audioPlayManager;

    private volatile String preSessionContextJson = null;

    @Getter
    private volatile String sessionContextJson = null;

    private volatile String beforeRollbackSessionContextJson = null;

    /**
     * 交互侧记录的所有用户输入信息, 目前只用来判断 断句补齐, 后续可能有其他作用
     */
    private final UserSayInfoStack userSayInfoStack;

    private final WaitUserSayFinishContext waitUserSayFinishContext = new WaitUserSayFinishContext();

    private final BotMetaData botMetaData;

    /**
     * vad识别到用户是否正在说话, 用户正在说话的时候, 需要暂停录音播放
     */
    private volatile boolean userSaying;

    /**
     * 用户最后说完时间, 这个是按照vad的来算, 还是asr识别结果的来算?
     */
    private volatile long lastUserSayFinishTime = System.currentTimeMillis();

    private final List<CallDetailListener> callDetailListenerList = new ArrayList<>();
    private final List<CallDetailListener> realCallDetailListenerList = new ArrayList<>();

    private final List<HangupEventListener> hangupEventListenerList = new ArrayList<>();

    /**
     * 记录最后一次用户无应答时间, 访问无应答时, 每次检查的时候持续触发
     */
    private volatile long lastUserSilenceTime = System.currentTimeMillis();

    /**
     * 对话是否结束了, 结束之后不再接收任何事件
     */
    private volatile boolean finish;

    /**
     * 用户无应答时长设置, 每个答案在播放完成后(aiSaiFinish)都会返回该值,
     */
    private final UserSilenceConfig userSilenceConfig;

    /**
     * 记录最后一次答案信息
     */
    private volatile AnswerResult lastAnswer;

    private volatile AnswerResult preLastAnswer;

    /**
     * 不会进行回退的最后播放的答案
     */
    private volatile AnswerResult realLastAnswer;

    /**
     * 执行挂机时间
     */
    private long hangupTimestamp;

    private boolean waitHangup;

    private volatile boolean executedFastHangup;

    /**
     * 最后一次ai开始播放音频的偏移量
     */
    private volatile long lastAiPlayBeginOffset = 0;

    /**
     * 最后一次ai播放完成的时间偏移量
     */
    private volatile long lastAiPlayEndOffset = 0;

    /**
     * 最后一次响应答案的时间偏移量
     */
    private volatile long lastResponseAnswerOffset = 0;


    private final UserSilenceEventStashContext userSilenceEventStashContext = new UserSilenceEventStashContext();

    /**
     * 用户两次说话间隔阈值, 如果小于这个阈值, 就会判断为一句话, 就是所谓的断句补齐, 需要对对话状态进行回退后重新判断
     * 这里的间隔是两次asr识别结果直接的间隔, 上一次asr识别结果的endTime和这一次startTime直接的间隔
     */
    private static final int USER_INPUT_MERGE_INTERVAL_MS = DialogEngineConstant.USER_INPUT_MERGE_INTERVAL_MS;

    private volatile boolean lastUserSayFinishIsInterrupted;

    private final ApplicationExecutor executor;

    private final AutoAnswerRobotDetector autoAnswerRobotDetector;

    private final AtomicBoolean startedAsyncThread = new AtomicBoolean(false);

    private final List<String> eventLogContentList = new ArrayList<>();

    private final BaseAsrContext asrContext;

    private final Map<Long, UserSayInfo> userSayInfoMap = new ConcurrentHashMap<>();

    private final boolean enableInputMerge;

    private final OneTimeAddWechatExecutor oneTimeAddWechatExecutor;

    private Runnable switchToHumanServiceExecutor;

    private Runnable humanInterventionExecutor;

    private final SendSmsExecutor sendSmsExecutor;

    private final Set<Long> sendSuccessSmsTemplateIdSet = new HashSet<>();

    private final DTMFManager dtmfManager;

    private final String logId;

    private final StreamRecordAccumulator streamRecordAccumulator = new StreamRecordAccumulator();

    private volatile StreamRecord lastLLMRecord;

    private final Lock lock = new ReentrantLock();

    private final List<SimpleChatHistory> historyList = new ArrayList<>();
    private final List<SimpleChatHistory> preHistoryList = new ArrayList<>();
    final AsrResultFilter asrResultFilter;
    private final CallDetailCallbackAdaptor callDetailCallbackAdaptor;

    private final Map<EventParam, List<SimpleChatHistory>> requestChatHistoryListMap = new HashMap<>();


    private volatile boolean executedTransferToHuman;

    public DefaultSpeechDialogEngine(BotMetaData botMetaData,
                                     SessionInfo sessionInfo,
                                     AudioPlayManager audioPlayManager,
                                     ApplicationExecutor executor,
                                     DTMFManager dtmfManager) {
        this.session = sessionInfo;
        this.sessionContextJson = session.getSessionContextJson();
        this.preSessionContextJson = session.getPreSessionContextJson();
        this.executor = executor;
        session.setSessionContextJson(null);
        session.setPreSessionContextJson(null);
        this.botMetaData = botMetaData;
        this.audioPlayManager = audioPlayManager;

        this.userSayInfoStack = new UserSayInfoStack();
        this.startTimestamp = System.currentTimeMillis();
        this.asrResultFilter = AsrResultFilterFactory.create(botMetaData, this);

        this.interruptConfig = new InterruptConfig();
        this.preInterruptConfig = new InterruptConfig();
        this.enableInputMerge = BooleanUtils.isTrue(botMetaData.getEnableInputMerge());
        this.autoAnswerRobotDetector = new AutoAnswerRobotDetector();
        this.asrContext = V3AsrContextFactory.createContext(botMetaData.getDialogFlowId(), sessionInfo.getUsageTarget());
        this.oneTimeAddWechatExecutor = new OneTimeAddWechatExecutor(session.getUsageTarget());
        this.userSilenceConfig = new UserSilenceConfig();
        this.sendSmsExecutor = new SendSmsExecutor();
        initUserSilenceThreshold(this.userSilenceConfig, botMetaData);
        this.dtmfManager = dtmfManager;
        this.dtmfManager.registerListener(this);
        if (this.dtmfManager instanceof AudioPlayEventListener) {
            this.audioPlayManager.registerListener((AudioPlayEventListener) this.dtmfManager);
        }
        this.logId = MDC.get("MDC_LOG_ID");
        callDetailCallbackAdaptor = new CallDetailCallbackAdaptor();
        if (RobotSnapshotUsageTargetEnum.CALL_OUT.equals(botMetaData.getUsageTarget())) {
            callDetailListenerList.add(callDetailCallbackAdaptor);
        }
        this.audioPlayManager.registerListener(this);
        this.audioPlayManager.registerListener(callDetailCallbackAdaptor);
    }

    @Override
    public void registerAddWechatCallback(Runnable addWechatCallback) {
        oneTimeAddWechatExecutor.setAddWechatCallback(addWechatCallback);
    }

    @Override
    public void registerSwitchToHumanServiceExecutor(Runnable switchToHumanServiceExecutor) {
        this.switchToHumanServiceExecutor = switchToHumanServiceExecutor;
    }

    @Override
    public void registerHumanInterventionExecutor(Runnable humanInterventionExecutor) {
        if (Objects.nonNull(humanInterventionExecutor)) {
            this.humanInterventionExecutor = new OneTimeExecutorWrapper(humanInterventionExecutor);
        }
    }

    @Override
    public void writeData(short[] shorts) {
        asrResultFilter.writeData(shorts);
    }

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        asrResultFilter.waitingAsrInitSkipAudioData(length);
    }

    @Override
    public void registerSendSmsConsumer(Consumer<Collection<Long>> sendSmsConsumer) {
        this.sendSmsExecutor.setSendSmsConsumer(templateIdList -> {
            sendSmsConsumer.accept(templateIdList);
            sendSuccessSmsTemplateIdSet.addAll(templateIdList);
        });
    }

    @Override
    public void registerCallDetailListener(CallDetailListener listener) {
        if (Objects.nonNull(listener)) {
            if (RobotSnapshotUsageTargetEnum.CALL_OUT.equals(botMetaData.getUsageTarget())) {
                realCallDetailListenerList.add(listener);
            } else {
                callDetailListenerList.add(listener);
            }
        }
    }

    @Override
    public void onReplay() {
        audioPlayManager.replay();
        List<String> debugLogList = Collections.singletonList("用户按键【*】,重播当前话术");
        createUserSayRecord(SimpleUserSayResultInfo.builder()
                .userInput("*")
                .simpleDebugLogList(debugLogList)
                .debugLogList(debugLogList)
                .sequence(sequence.getAndIncrement())
                .build());
    }

    @Override
    public void onSuccess(String result) {
        submitAsyncRequest(new KeyCaptureSuccessEvent(result));
    }

    @Override
    public void onFailed() {
        submitAsyncRequest(new KeyCaptureFailedEvent());
    }

    private void initUserSilenceThreshold(UserSilenceConfig userSilenceConfig, BotMetaData botMetaData) {
        userSilenceConfig.defaultUserSilenceMs = 7000;
        if (Objects.nonNull(botMetaData.getUserSilenceThreshold())) {
            userSilenceConfig.defaultUserSilenceMs = (int) (botMetaData.getUserSilenceThreshold() * 1000);
        } else {
            log.info("未设置用户无应答时长, 设置为默认7秒");
        }
        userSilenceConfig.userSilenceMs = userSilenceConfig.defaultUserSilenceMs;
        log.info("userSilenceConfig={}", userSilenceConfig);
    }

    /**
     * 判断用户是否无应答超时了
     */
    private boolean checkUserSilence() {
        long userSilenceTime = userSilenceConfig.userSilenceMs;
        // 用户当前没有说话 && ai是有播放过的 && 超过阈值没有说话的
        boolean aiPlayed = audioPlayManager.getLastPlayEndTime() > 0;
        long currentTimeMillis = System.currentTimeMillis();
        long lastVoiceActiveTime = getLastVoiceActiveTime();
        boolean timeout = (currentTimeMillis - lastVoiceActiveTime) > userSilenceTime;
        boolean silenceTimeout = (currentTimeMillis - lastUserSilenceTime) > userSilenceTime;
        if (!userSaying && aiPlayed && timeout && silenceTimeout) {
            log.info("用户无应答，userSilenceTime={}，currentTimeMillis={}，lastUserSilenceTime={}, lastVoiceActiveTime={}",
                    userSilenceTime, currentTimeMillis, lastUserSilenceTime, lastVoiceActiveTime);
            return true;
        }
        return false;
    }

    /**
     * 返回最后有声音活动的时间, 不管是ai说话还是用户说话
     */
    private long getLastVoiceActiveTime() {
        return Math.max(audioPlayManager.getLastPlayTime(), lastUserSayFinishTime);
    }

    @Override
    public void userSilence() {
        if (userSilenceConfig.waitUserSayFinish
                && waitUserSayFinishContext.preUserSayInfo != null) {
            waitUserSayFinishDone();
        } else {
            SimpleChatHistory historyItem = new SimpleChatHistory();
            historyItem.setRole(SimpleChatHistory.ROLE_ASSISTANT);
            historyItem.setContent(audioPlayManager.getCurrentAnswerPlayedContent());
            historyItem.setIndex(audioPlayManager.getPlayedAnswerIndex());
            historyList.add(historyItem);

            SimpleChatHistory userItem = new SimpleChatHistory();
            userItem.setRole(ROLE_USER);
            userItem.setContent("");
            userItem.setIndex(audioPlayManager.getPlayedAnswerIndex());
            historyList.add(userItem);

            this.lastUserSilenceTime = System.currentTimeMillis();
            UserSilenceEvent userSilenceEvent = new UserSilenceEvent();
            userSilenceEvent.setSilenceMs(userSilenceConfig.userSilenceMs);
            submitAsyncRequest(userSilenceEvent);
        }
    }

    @Override
    public void init() {

    }

    @Override
    public void enter() {
        startTimestamp = System.currentTimeMillis();
        log.info("enter, startTimestamp:{}", startTimestamp);
        EnterEvent event = new EnterEvent();
        submitAsyncRequest(event);
    }

    @Override
    public void onSentenceBegin() {
        asrResultFilter.onSentenceBegin();
    }

    @Override
    public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        asrResultFilter.processUserSay(userSayText, userSayFinish, beginTime, endTime);
    }

    @Override
    public void realProcessUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        // aiProgress 和 aiSayTime单独从audioPlayManager重新获取, 这里是获取的SoundManager的结果, 已经分开处理了
        lastUserSayFinishTime = System.currentTimeMillis();
        if (userSayFinish) {
            compensationUserVoiceEnd();
        } else {
            compensationUserVoiceBegin();
        }
        if (userSayFinish && StringUtils.isBlank(userSayText)) {
            log.info("用户说话为空, 仅恢复音频播放(如果已暂停的话");
            resumePlay();
        } else {
            try {
                innerProcessUserSay(userSayText,
                        audioPlayManager.getCurrentAnswerPlayPercent(),
                        audioPlayManager.getCurrentAnswerPlayTime(),
                        userSayFinish,
                        beginTime,
                        endTime,
                        audioPlayManager.getCurrentAnswerPlayedContent());
            } catch (Exception e) {
                log.warn("[LogHub_Warn]处理用户输入异常", e);
            }
        }
    }

    @Override
    public void registerHangupEventListener(HangupEventListener hangupEventListener) {
        if (hangupEventListener != null) {
            this.hangupEventListenerList.add(hangupEventListener);
        }
    }

    @Override
    public String getSessionContextContent() {
        return sessionContextJson;
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysis(CallDataInfo callDataInfo) {
        streamRecordAccumulator.callbackAll();

        callDetailCallbackAdaptor.submitCallback(true);

        callDataInfo.setSessionContextJson(sessionContextJson);
        callDataInfo.setEventLogContentList(eventLogContentList);
        IntentLevelAnalysisResult intentLevelAnalysisResult =  botChatService.intentLevelAnalysis(session.getCallTaskId(), session.getUsageTarget(), callDataInfo);

        // 对于发送短信, 需要去重一下
        Set<Long> alreadySendSmsTemplateIdSet = sendSuccessSmsTemplateIdSet;
        if (CollectionUtils.isNotEmpty(alreadySendSmsTemplateIdSet)
                && CollectionUtils.isNotEmpty(intentLevelAnalysisResult.getSmsTemplateIds())) {
            log.info("对短信模板进行去重, 去重前:{}", intentLevelAnalysisResult.getSmsTemplateIds());
            intentLevelAnalysisResult.getSmsTemplateIds().removeAll(alreadySendSmsTemplateIdSet);
            log.info("对短信模板进行去重, 去重后:{}", intentLevelAnalysisResult.getSmsTemplateIds());
        }

        intentLevelAnalysisResult.setHangupOn(calculateHangupOn());
        log.debug("意向等级分析结果:{}", JsonUtils.object2String(intentLevelAnalysisResult));
        return intentLevelAnalysisResult;
    }

    private String calculateHangupOn() {
        if (lastAnswer == null || lastAnswer.getLocate() == null) {
            return null;
        }

        log.debug("lastAnswer:{}", lastAnswer);
        AnswerLocateBO locate = lastAnswer.getLocate();
        if (StringUtils.isNotBlank(locate.getStepName()) && StringUtils.isNotBlank(locate.getNodeName())) {
            return String.format("%s-%s", locate.getStepName(), locate.getNodeName());
        }
        if (StringUtils.isNotBlank(locate.getStepName())) {
            return locate.getStepName();
        }

        if (StringUtils.isNotBlank(locate.getKnowledgeName())) {
            return locate.getKnowledgeName();
        }

        if (StringUtils.isNotBlank(locate.getSpecialAnswerConfigName())) {
            return locate.getSpecialAnswerConfigName();
        }

        return null;
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysisInCall(CallDataInfo callDataInfo) {
        callDataInfo.setSessionContextJson(sessionContextJson);
        callDataInfo.setEventLogContentList(eventLogContentList);
        return botChatService.intentLevelAnalysisInCall(session.getCallTaskId(), session.getUsageTarget(), callDataInfo);
    }

    @Override
    public void release(String reason) {
        log.info("release, reason={}", reason);
        this.finish = true;
    }

    /**
     * 处理用户输入, 第二版, 精简交互层逻辑, 仅负责音频的暂停播放逻辑, 其他逻辑交由对话层处理
     * 进行此改动原因是:
     * 1. 对话层接口响应时间可以做到<10毫秒级响应, 基本不会对体验产生影响(对于调用算法的接口, 该慢的还是一样慢)
     * 2. 对话逻辑变复杂了, 打断的逻辑和对话层的逻辑耦合度太高, 交互层的逻辑变得复杂, 且不易维护
     * @param userSayText 用户输入文本
     * @param aiProgress AI侧音频放音进度
     * @param aiSayTime 用户说话时长
     * @param userSayFinish 用户是否输入完成
     * @param beginTime 用户输入开始时间
     * @param endTime 用户输入结束时间
     */
    protected void innerProcessUserSay(String userSayText, double aiProgress, Integer aiSayTime, boolean userSayFinish, Integer beginTime, Integer endTime, String lastAnswerPlayedContent) {
        // 1. 去掉句尾的符号
        // 2. 判断是否断句补齐
        // 3. 调用对话层接口

        log.info("processUserSay, userSayText={}, aiProgress={}, aiSayTime={}, userSayFinish={}, beginTime={}, endTime={}, lastAnswerPlayedContent:{}",
                userSayText, aiProgress, aiSayTime, userSayFinish, beginTime, endTime, lastAnswerPlayedContent);

        userSayText = removeLastSymbol(userSayText);

        // 对于断句补齐的打断逻辑处理
        // 比如用户先说完前半部分, 这个时候开始播放, 且更新了打断配置, 然后接着用户又开始说话, 这个时候要判断是否满足断句补齐的特点,
        // 满足断句补齐, 如果用户说完了, 则进行相应, 如果没说完, 这个时候, 可以先暂停录音播放, 等用户说完再处理
        Optional<UserSayInfo> lastUserSayInfo = userSayInfoStack.peek();
        UserSayInfo userSayInfo = new UserSayInfo(userSayText, beginTime, endTime);
        boolean necessaryMerge = lastUserSayInfo.isPresent() && isMergeInput(lastUserSayInfo.get(), userSayInfo);
        log.info("necessaryMerge={}", necessaryMerge);
        if (necessaryMerge && !userSayFinish) {
            log.info("用户输入中间结果, 且被判断为断句补齐, 所以暂停录音播放, 等待用户输入完成");
            pausePlay();
            return;
        }

        // 断句补齐后, 录音播放进度需要回退到断句补齐前的进度
        if (necessaryMerge && Objects.nonNull(preLastAnswer)) {
            // 回退录音播放进度
            double preAiPlayProgress = audioPlayManager.getAnswerPlayPercentByAnswerId(preLastAnswer.getId());
            int preAiSayTime = audioPlayManager.getAnswerPlayTimeByAnswerId(preLastAnswer.getId());
            log.info("回退录音播放进度, aiProgress={}, aiSayTime={}, preAiPlayProgress={}, preAiSayTime={}", aiProgress, aiSayTime, preAiPlayProgress, preAiSayTime);
            aiProgress = preAiPlayProgress;
            aiSayTime = preAiSayTime;
        }

        // 判断用户是否输入完成, 执行对应的逻辑
        if (userSayFinish) {
            // 判断是否符合断句补齐
            UserSayInfo finalInfo = userSayInfo;
            if (necessaryMerge) {
                // 进行断句补齐, 合并
                UserSayInfo last = lastUserSayInfo.get();
                UserSayInfo mergeResult = merge(last, userSayInfo);
                log.info("用户输入符合断句补齐条件, 进行断句补齐, {\"last\":{}, \"current\":{}, \"mergeResult\":{} }",
                        JsonUtils.object2String(last), JsonUtils.object2String(userSayInfo), JsonUtils.object2String(mergeResult));
                userSayInfoStack.pop();
                userSayInfoStack.push(mergeResult);
                // 这里需要重置等待用户输入完成的状态
                waitUserSayFinishContext.reset();
                finalInfo = mergeResult;
            } else {
                userSayInfoStack.push(userSayInfo);
            }
            userSayFinish(finalInfo, aiProgress, null, lastAnswerPlayedContent);
            // 判断是否命中交互层快速挂断
        } else {
            // 用户输入中间结果, 发送userSay事件, 对话层对userSay进行处理, 主要判断是否需要暂停录音播放
            userSay(userSayInfo, aiProgress, aiSayTime, lastAnswerPlayedContent);
        }
    }

    private static String removeLastSymbol(String userSayText) {
        if (userSayText.endsWith("。")
                || userSayText.endsWith("？")
                || userSayText.endsWith("?")
                || userSayText.endsWith("！")
                || userSayText.endsWith("!")
                || userSayText.endsWith("，")
                || userSayText.endsWith(",")) {
            userSayText = userSayText.substring(0, userSayText.length() - 1);
        }
        return userSayText;
    }

    private void execFastHangup(double aiProgress, String reason) {
        // 目前一些异常情况会频繁触发快速挂断, 在交互层进行处理, 仅触发一次, 后续的快速挂断直接忽略
        if (!executedFastHangup) {
            executedFastHangup = true;
            FastHangupEvent event = new FastHangupEvent();
            event.setPlayProgress(aiProgress);
            event.setReason(reason);
            submitAsyncRequest(event);
        } else {
            log.info("已经执行过快速挂断, 本次快速挂断事件将被忽略, reason={}", reason);
        }
    }

    protected boolean isMergeInput(UserSayInfo last, UserSayInfo current) {
        log.info("判断是否符合断句补齐条件, enableInputMerge={} last={}, current={}", enableInputMerge, last, current);
        return enableInputMerge && (current.beginTime - last.endTime) < USER_INPUT_MERGE_INTERVAL_MS;
    }

    private UserSayInfo merge(UserSayInfo last, UserSayInfo current) {
        UserSayInfo merged = new UserSayInfo();
        merged.setOriginText(current.getText());
        merged.setBeginTime(last.beginTime);
        merged.setEndTime(current.endTime);
        merged.setMerged(true);
        List<UserSayInfo> originList = new ArrayList<>();
        if (last.merged) {
            originList.addAll(last.getOriginInfoList());
        } else {
            originList.add(last);
        }
        originList.add(current);
        merged.setOriginInfoList(originList);
        merged.setText(originList.stream().filter(info -> !info.isIgnore()).map(UserSayInfo::getOriginText).collect(Collectors.joining("")));
        return merged;
    }

    protected void createCantInterruptRecord(String userSayText,
                                             List<String> debugLogList,
                                             List<String> simpleDebugLogList,
                                             Integer sequence,
                                             Long startOffset,
                                             Long endOffset) {
        createUserSayRecord(SimpleUserSayResultInfo.builder()
                .userInput(userSayText)
                .beginTime(startOffset)
                .endTime(endOffset)
                .simpleDebugLogList(simpleDebugLogList)
                .debugLogList(debugLogList)
                .sequence(sequence)
                .build());
    }

    private void userSay(UserSayInfo userSayInfo, double aiProgress, Integer aiSayTime, String lastAnswerPlayedContent) {
        if (userSilenceConfig.waitUserSayFinish) {
            log.info("等待用户输入完成期间, 不处理userSay事件");
            return;
        }
        UserSayEvent event = new UserSayEvent();
        event.setInputText(userSayInfo.getText());
        event.setLastAnswerPlayedContent(lastAnswerPlayedContent);
        event.setOriginInputText(userSayInfo.getOriginText());
        // 这里面的beginTime和
        event.setStartOffset(userSayInfo.getBeginTime());
        event.setEndOffset(userSayInfo.getEndTime());
        event.setIsMergeInput(userSayInfo.merged);
        event.setPlayProgress(aiProgress);
        event.setOffset(getOffset());
        event.setLastAnswerId(audioPlayManager.getLastPlayAnswerId());
        userSayInfoMap.put(event.getOffset(), userSayInfo);
        submitAsyncRequest(event);
    }

    private void userSayFinish(UserSayInfo userSayInfo, double aiProgress, Boolean waitUserSayFinishDone, String lastAnswerPlayedContent) {
        userSayInfo = mergeWaitUserSayFinishContent(userSayInfo);
        SimpleChatHistory historyItem = new SimpleChatHistory();
        historyItem.setRole(SimpleChatHistory.ROLE_ASSISTANT);
        historyItem.setContent(audioPlayManager.getCurrentAnswerPlayedContent());
        historyItem.setIndex(audioPlayManager.getPlayedAnswerIndex());
        historyList.add(historyItem);

        SimpleChatHistory userItem = new SimpleChatHistory();
        userItem.setRole(ROLE_USER);
        userItem.setContent(userSayInfo.getText());
        userItem.setIndex(historyItem.getIndex());
        historyList.add(userItem);


        boolean merged = BooleanUtils.isNotTrue(waitUserSayFinishDone) && userSayInfo.merged;
        UserSayFinishEvent event = new UserSayFinishEvent();
        event.setInputText(userSayInfo.getText());
        event.setLastAnswerPlayedContent(lastAnswerPlayedContent);
        event.setOriginInputText(userSayInfo.getOriginText());
        // 这里面的beginTime和
        event.setStartOffset(userSayInfo.getBeginTime());
        event.setEndOffset(userSayInfo.getEndTime());
        event.setIsMergeInput(merged);
        event.setPlayProgress(aiProgress);
        event.setEnableInterrupt(BooleanUtils.isTrue(waitUserSayFinishDone) || lastUserSayFinishIsInterrupted && merged);
        event.setOffset(getOffset());
        event.setLastAnswerId(audioPlayManager.getLastPlayAnswerId());
        event.setWaitUserSayFinishDone(BooleanUtils.isTrue(waitUserSayFinishDone));
        event.setChatHistoryList(processHistoryList(historyList));
        userSayInfoMap.put(event.getOffset(), userSayInfo);
        requestChatHistoryListMap.put(event, processHistoryList(historyList));
        submitAsyncRequest(event);
    }

    /**
     * 合并等待用户输入完成的内容
     * @param userSayInfo 当前用户输入的内容
     * @return 合并后的内容
     */
    private UserSayInfo mergeWaitUserSayFinishContent(UserSayInfo userSayInfo) {
        // 未开启等待用户输入完成, 直接返回
        if (!userSilenceConfig.waitUserSayFinish) {
            return userSayInfo;
        }
        return waitUserSayFinishContext.merge(userSayInfo);
    }

    private void submitAsyncRequest(EventParam event) {
        lock.lock();
        try {
            if (finish) {
                log.info("当前会话已经结束了, 不再接收异步事件, event={}", event);
                return;
            }
            if (executedTransferToHuman) {
                log.info("当前会话已经执行了转人工操作, 不再接收异步事件, event={}", event);
                return;
            }
            if (event.getOffset() < 1) {
                event.setOffset(getOffset());
            }
            log.debug("chatHistory:{}", JsonUtils.object2String(historyList));
            log.info("提交事件到异步队列, seq={}, event={}", sequence.get(), JsonUtils.object2String(event));
            eventSender.submitEvent(event);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查是否需要忽略,
     * 在极端情况下, vad检测失败, 用户无应答时间很短, 会导致同时触发userSilence和userSayFinish事件, 但是这两种其实只需要响应一种就可以了
     * 响应userSayFinish更合理
     * 这里有两种情况, 1: 先发送了userSilence事件, 然后发送userSayFinish事件, 这种情况, 需要回退状态, 并重新发送userSayFinish事件
     * 2: 先发送了userSayFinish事件, 然后发送userSilence事件, 这种情况, 只需要忽略userSilence事件即可
     * 如何判断是两个事件同时触发了?
     * 这里也需要分情况考虑, 对于第一种情况的阈值, 尽量设置的比较小, 避免用户响应用户无应答的答案时, 可以用用户开始说话的时间加入判断
     * 对于第二种情况, 需要判断无应答的时候, 是否有userSayFinish的事件正在处理, 或者已经处理完了, 但是音频正在播放
     *
     * @param event 事件参数
     */
    private boolean checkUserSilenceNeedIgnore(EventParam event) {
        // 这里先判断用户无应答事件是否需要忽略
        if (!(event instanceof UserSilenceEvent)) {
            return false;
        }
        // 判断上一次事件是否是userSayFinish事件, 如果是, 判断下阈值
        // 判断下上一次录音播放结束事件和现在的间隔
        if (event.getOffset() < lastAiPlayBeginOffset) {
            log.debug("当前用户无应答事件的offset已经小于上一次ai播放开始的offset, 忽略当前用户无应答事件, offset={}, lastAiPlayBeginOffset={}", event.getOffset(), lastAiPlayBeginOffset);
            return true;
        }

        // 判断已经获取到新的答案了, 还没开始播放
        if (event.getOffset() < lastResponseAnswerOffset) {
            log.debug("当前用户无应答事件的offset已经小于上一次获取答案的偏移量, 忽略当前用户无应答事件, offset={}, lastAiPlayBeginOffset={}", event.getOffset(), lastAiPlayBeginOffset);
            return true;
        }

        return false;
    }

    private StreamRecord createAiSayRecord(int playIndex,
                                           String answerId,
                                           String answer,
                                           boolean finish,
                                           List<String> debugLogList,
                                           List<String> simpleDebugLogList) {
        return streamRecordAccumulator.createAiSayRecord(playIndex, answerId, answer, finish, debugLogList, simpleDebugLogList);
    }

    private StreamRecord createAiSayRecord(int playIndex,
                                           String answerId,
                                           String answer,
                                           boolean finish) {
        return createAiSayRecord(playIndex, answerId, answer, finish, Collections.emptyList(), Collections.emptyList());
    }

    private void createUserSayRecord(SimpleUserSayResultInfo userSayResultInfo) {
        streamRecordAccumulator.createUserSayRecord(userSayResultInfo);

//        if (CollectionUtils.isNotEmpty(callDetailListenerList)) {
//            for (CallDetailListener callDetailListener : callDetailListenerList) {
//                try {
//                    callDetailListener.createUserSayRecord(userSayResultInfo);
//                } catch (Exception e) {
//                    log.warn("创建用户侧联系历史失败", e);
//                }
//            }
//        }
    }

    /**
     * 处理普通节点/知识返回的答案, 或者大模型的引导话术
     */
    private void processAnswer(EventParam event, ChatResponse response) {
        AnswerResult answer = response.getAnswer();
        AnswerAudioPlayConfig answerAudioPlayConfig = response.getAnswerAudioPlayConfig();
        if (Objects.isNull(answer)) {
            // 如果没有命中任何意图, 且没有开启ai无法应答是会返回空答案的
            // todo 这种是挂机节点未设置答案, 后面应该在对话那边做统一处理, 不应该在这边做
            if (event instanceof UserSayFinishEvent) {
                UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) event;
                createUserSayRecord(SimpleUserSayResultInfo.builder()
                        .userInput(userSayFinishEvent.getInputText())
                        .simpleDebugLogList(response.getSimpleDebugLogList())
                        .debugLogList(response.getDebugLogList())
                        .sequence(response.getSequence())
                        .beginTime(userSayFinishEvent.getStartOffset())
                        .endTime(userSayFinishEvent.getEndOffset())
                        .build());

                // 如果是打断, 则恢复音频播放
                if (Objects.nonNull(response.getAnswerAudioPlayConfig())
                        && RepeatAnswerPlayStrategyEnum.RESUME.equals(response.getAnswerAudioPlayConfig().getRepeatPlayStrategy())) {
                    audioPlayManager.resume();
                }
            } else if (event instanceof FastHangupEvent) {
                FastHangupEvent fastHangupEvent = (FastHangupEvent) event;
                createUserSayRecord(SimpleUserSayResultInfo.builder()
                        .userInput(fastHangupEvent.getReason())
                        .simpleDebugLogList(response.getSimpleDebugLogList())
                        .debugLogList(response.getDebugLogList())
                        .sequence(response.getSequence())
                        .build());
            } else if (event instanceof UserSilenceEvent) {
                // 用户无应答直接命中挂机节点了
                createUserSayRecord(SimpleUserSayResultInfo.builder()
                        .userInput("用户无应答")
                        .simpleDebugLogList(response.getSimpleDebugLogList())
                        .debugLogList(response.getDebugLogList())
                        .sequence(response.getSequence())
                        .build());
            }
            return;
        }

        // UserSay事件通过EventResponseListener处理
        if (ChatEventTypeEnum.USER_SAY.equals(event.getEvent())) {
            return;
        }

        if (event instanceof UserSayFinishEvent) {
            UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) event;
            if (userSayFinishEvent.getPlayProgress() != null
                    && userSayFinishEvent.getPlayProgress() < 100.0
                    && (!answerIsEqual(realLastAnswer, answer) || lastUserSayFinishIsInterrupted)) {
                lastUserSayFinishIsInterrupted = true;
                log.info("lastUserSayFinishIsInterrupted:{}", lastUserSayFinishIsInterrupted);
            } else {
                lastUserSayFinishIsInterrupted = false;
            }
        }

        // 重置按键采集配置
        if (BooleanUtils.isNotTrue(response.getNoNeedResetKeyCaptureConfig())) {
            dtmfManager.reset(answer.getKeyCaptureConfig());
        }

        updateInterruptConfig(answer);

        clearWaitUserSayFinishStatus();

        lastResponseAnswerOffset = getOffset();

        // 前后两个答案是一致的, 且不是
        if (answerIsEqual(answer, realLastAnswer)
                && (!isNeedReplay(answerAudioPlayConfig) || isResumeByMergeInput(event))
                && !needWaitAnswerComplete(answer)) {
            // 只需要继续播放当前答案
            recordLastAnswer(answer);
            log.info("重复答案, 不需要重新播放, 直接继续播放当前录音, answer={}, playConfig={}", answer, answerAudioPlayConfig);
            resumeAnswerPlay(answer);
            // 创建用户侧联系历史, todo: 继续播放的联系历史应该是不同的
            createUserSayRecord(event, answer, response);
            // 创建ai侧联系历史
            if (isResumeByMergeInput(event)) {
                log.info("断句补齐,添加ai侧联系历史");
                createAiSayRecord(audioPlayManager.getCurrentIndex(), answer.getId(), answer.getRealAnswer(), BooleanUtils.isTrue(answer.getIsCompleted()));
            } else {
                log.info("答案重复且继续播放录音, 不需要添加ai侧的联系历史");
            }
        } else {
            // 重复的答案重新播放, 或者是新的答案
            recordLastAnswer(answer);
            // 开始播放录音
            playAnswerAudio(answer);

            // 创建用户侧联系历史
            createUserSayRecord(event, answer, response);

            // 创建ai侧联系历史
            createAiSayRecord(audioPlayManager.getCurrentIndex(), answer.getId(), answer.getRealAnswer(), BooleanUtils.isTrue(answer.getIsCompleted()));
        }
    }

    private void recordLastAnswer(AnswerResult currentAnswer) {
        preLastAnswer = lastAnswer == null ? currentAnswer : lastAnswer;
        lastAnswer = currentAnswer;
        realLastAnswer = currentAnswer;
    }

    private boolean needWaitAnswerComplete(AnswerResult answer) {
        return BooleanUtils.isNotTrue(answer.getIsCompleted());
    }

    /**
     * 清除掉等待用户输入完成的状态
     * 如果是在等待用户输入完成过程中, 接收到新的答案播报, 那么说明用户已经说完了(识别到意图了), 需要清除掉等待用户输入完成的状态
     */
    private void clearWaitUserSayFinishStatus() {
        if (userSilenceConfig.waitUserSayFinish) {
            log.info("清除等待用户输入完成的状态");
            updateUserSilence(false);
        }
    }

    private void createUserSayRecord(EventParam event,
                                     AnswerResult answer,
                                     ChatResponse chatResponse) {
        List<String> debugLogList = chatResponse.getDebugLogList();
        List<String> simpleDebugLogList = chatResponse.getSimpleDebugLogList();
        Integer sequence = chatResponse.getSequence();
        if (ChatEventTypeEnum.USER_SAY_FINISH.equals(event.getEvent())) {
            UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) event;
            String userInput = userSayFinishEvent.getInputText();
            // 这里使用response中的inputText, 因为在对话侧是会进行asr纠错的, 所以如果可能的话, 使用对话侧纠错后的结果
            if (StringUtils.isNotBlank(chatResponse.getInputText())) {
                userInput = chatResponse.getInputText();
            }
            createUserSayRecord(SimpleUserSayResultInfo.builder()
                    .beginTime(userSayFinishEvent.getStartOffset())
                    .endTime(userSayFinishEvent.getEndOffset())
                    .debugLogList(debugLogList)
                    .simpleDebugLogList(simpleDebugLogList)
                    .userInput(userInput)
                    .sequence(sequence)
                    .build()
            );
        } else if (ChatEventTypeEnum.USER_SILENCE.equals(event.getEvent())) {
            createUserSayRecord(SimpleUserSayResultInfo.builder()
                    .userInput("用户无应答")
                    .simpleDebugLogList(simpleDebugLogList)
                    .debugLogList(debugLogList)
                    .sequence(sequence)
                    .build());
        } else if (ChatEventTypeEnum.KEY_CAPTURE_SUCCESS.equals(event.getEvent())) {
            createUserSayRecord(SimpleUserSayResultInfo.builder()
                    .userInput(((KeyCaptureSuccessEvent) event).getResult())
                    .simpleDebugLogList(simpleDebugLogList)
                    .debugLogList(debugLogList)
                    .sequence(sequence)
                    .build());
        } else if (ChatEventTypeEnum.KEY_CAPTURE_FAILED.equals(event.getEvent())) {
            createUserSayRecord(SimpleUserSayResultInfo.builder()
                    .userInput("按键采集超时")
                    .simpleDebugLogList(simpleDebugLogList)
                    .debugLogList(debugLogList)
                    .sequence(sequence)
                    .build());
        } else {
            log.info("event={} 不需要创建用户侧联系历史", event.getEvent());
        }
    }

    private void resumeAnswerPlay(AnswerResult answer) {
        log.info("恢复播放答案, answer={}", answer);
        audioPlayManager.resume(answer.getId());
    }

    private void resumeAnswerPlay(ResumePlayAction action) {
        log.info("恢复播放答案, action={}", action);
        audioPlayManager.resume(action.getAnswerId());
    }

    /**
     * 判断是否是断句补齐
     */
    private boolean isResumeByMergeInput(EventParam event) {
        return event instanceof UserSayFinishEvent && BooleanUtils.isTrue(((UserSayFinishEvent) event).getIsMergeInput());
    }

    private boolean isRepeatAnswer(AnswerResult answer) {
        return answerIsEqual(answer, lastAnswer);
    }

    private boolean answerIsEqual(AnswerResult one, AnswerResult other) {
        if (Objects.isNull(one) || Objects.isNull(other)) {
            return false;
        }
        return one.getId().equals(other.getId());
    }

    private boolean isNeedReplay(AnswerAudioPlayConfig config) {
        return Objects.isNull(config)
                || Objects.isNull(config.getRepeatPlayStrategy())
                || RepeatAnswerPlayStrategyEnum.REPLAY.equals(config.getRepeatPlayStrategy());
    }

    private void playAnswerAudio(AnswerResult answer) {
        log.info("开始播放答案: answerId={}, template={}, realAnswer={}", answer.getId(), answer.getTemplate(), answer.getRealAnswer());
        cancelHangupDelay();
        // 从answerElementList获取要播放的录音信息
        if (CollectionUtils.isEmpty(answer.getAnswerElementList())) {
            // 异常了
            log.warn("答案数据异常, 答案元素内容为空");
            return;
        }
        try {
            audioPlayManager.playAudio(answer);
            // todo
            if (needWaitAnswerComplete(answer)) {
                audioPlayManager.appendPlaceholderFragment();
            }
        } catch (Exception e) {
            log.warn("调用播放录音接口异常", e);
        }
    }

    private long getOffset() {
        return System.currentTimeMillis() - startTimestamp;
    }

    @Override
    public void onAiHangup() {
        log.info("onAiHangup");
        this.finish();
    }

    long getAudioReadTime() {
        return callDetailCallbackAdaptor.readAudioTimeMs;
    }

    void setAudioReadTime(long time) {
        callDetailCallbackAdaptor.readAudioTimeMs = time;
        log.debug("setAudioReadTime:{}", time);
    }

    @Override
    public void finishByError() {
        log.info("finishByError");
        this.finish();
    }

    @Override
    public void finishByMatchPromptAudio() {
        log.info("finishByMatchPromptAudio");
        this.finish();
    }

    protected void finish() {
        this.finish = true;
        audioPlayManager.pause();
        // 触发联系历史回调
        callDetailCallbackAdaptor.submitCallback(true);
        // async
        botChatService.finish();
        // 发送finish请求给引擎端, 可以是引擎端释放engine实例
    }

    @Override
    public void onReadData() {
        // 读取播放数据
        if (checkNeedExecuteHangup()) {
            this.hangup();
            return;
        }
        // 延迟挂机时, 不触发用户无应答
        if (!waitHangup && checkUserSilence()) {
            userSilence();
        }
    }

    @Override
    public void onWaitingTimeout() {
        log.debug("onWaitingTimeout");
    }

    /**
     * 等待用户输入完成结束
     */
    private void waitUserSayFinishDone() {
        log.info("waitUserSayFinishDone");
        UserSayInfo userSayInfo = waitUserSayFinishContext.preUserSayInfo;
        updateUserSilence(false);
        lastUserSayFinishTime = System.currentTimeMillis();
        log.debug("lastUserSayFinishTime:{}", lastUserSayFinishTime);
        userSayFinish(userSayInfo, audioPlayManager.getAnswerPlayPercentByAnswerId(lastAnswer.getId()), true, audioPlayManager.getCurrentAnswerPlayedContent());
    }

    private boolean checkNeedExecuteHangup() {
        if (finish) {
            return false;
        }
        if (!waitHangup) {
            return false;
        }
        long now = System.currentTimeMillis();
        if (now > hangupTimestamp) {
            log.info("延迟挂机时间到达, 开始挂机");
            return true;
        }
        return false;
    }

    private void pausePlay() {
        log.info("暂停音频播放");
        audioPlayManager.pause();
    }

    @Override
    public void onAiPlayBegin(int index) {
        lastAiPlayBeginOffset = getOffset();
    }

    @Override
    public void onAiPlayEnd(int index) {
        // ai说完是会触发aiSayFinish事件的
        lastAiPlayEndOffset = getOffset();

        SimpleChatHistory historyItem = new SimpleChatHistory();
        historyItem.setRole(SimpleChatHistory.ROLE_ASSISTANT);
        historyItem.setContent(audioPlayManager.getCurrentAnswerPlayedContent());
        historyItem.setIndex(audioPlayManager.getPlayedAnswerIndex());
        historyList.add(historyItem);

        AiSayFinishEvent aiSayFinishEvent = new AiSayFinishEvent();
        aiSayFinishEvent.setLastAnswerId(audioPlayManager.getLastPlayAnswerId());
        aiSayFinishEvent.setLastAnswerPlayedContent(audioPlayManager.getCurrentAnswerPlayedContent());
        submitAsyncRequest(aiSayFinishEvent);
    }

    @Override
    public void onPause(int index) {

    }

    @Override
    public void onResume(int index) {

    }

    @Override
    public void onUserVoiceBegin() {
        // 通过vad检测到用户开始说话
        log.info("通过VAD检测到用户开始说话");
        userSaying = true;
        lastUserSayFinishTime = System.currentTimeMillis();
    }

    /**
     * 补偿用户开始说话状态
     */
    private void compensationUserVoiceBegin() {
        if (!userSaying) {
            userSaying = true;
            lastUserSayFinishTime = System.currentTimeMillis();
            log.info("补偿用户开始说话状态");
        }
    }

    private void compensationUserVoiceEnd() {
        if (userSaying) {
            userSaying = false;
            lastUserSayFinishTime = System.currentTimeMillis();
            log.info("补偿用户结束说话状态");
        }
    }

    @Override
    public void onUserVoiceEnd() {
        // 通过vad检测到
        log.info("通过VAD检测到用户说话结束");
        userSaying = false;
        lastUserSayFinishTime = System.currentTimeMillis();
        resumePlay();
    }

    private void resumePlay() {
        log.info("继续播放当前录音");
        audioPlayManager.resume();
    }

    /**
     * 每个答案都会返回这个答案是否允许打断标记和打断比例的配置
     *
     * @param answer 答案响应结果
     */
    private void updateInterruptConfig(AnswerResult answer) {
        recordInterruptConfigSnapshot();
        this.interruptConfig.setAnswerSource(answer.getAnswerSource());
        this.interruptConfig.setUninterrupted(BooleanUtils.isTrue(answer.getUninterrupted()));
        if (Objects.nonNull(answer.getCustomInterruptThreshold())) {
            this.interruptConfig.setThreshold(answer.getCustomInterruptThreshold());
        } else {
            this.interruptConfig.setThreshold(100);
        }
        this.interruptConfig.setNeedTryReplyOnUninterrupted(BooleanUtils.isTrue(answer.getNeedTryReplyOnUninterrupted()));
        log.info("更新打断设置, interruptConfig={}", this.interruptConfig);
    }

    private void recordInterruptConfigSnapshot() {
        BeanUtils.copyProperties(this.interruptConfig, this.preInterruptConfig);
    }

    /**
     * 处理响应结果
     */
    protected void processResponse(EventParam event, ChatResponse response) {
        String sessionContext = "";
        String preSessionContext = "";
        if (Objects.nonNull(response)) {
            sessionContext = response.getSessionContextJson();
            preSessionContext = response.getPreSessionContextJson();
            if (StringUtils.isNotBlank(response.getEventLogContent())) {
                eventLogContentList.add(response.getEventLogContent());
            }
            response.setEventLogContent(null);
            response.setSessionContextJson(null);
            response.setPreSessionContextJson(null);
        }

        log.info("处理响应信息, {\"event\":{}, \"response\":{} }", JsonUtils.object2String(event), JsonUtils.object2String(response));
        if (event instanceof UserSilenceEvent) {
            userSilenceEventStashContext.lastResponseUserSilenceOffset = getOffset();
        }

        validResponse(response);

        // 处理动态变量
        handleDynamic(response);

        // 更新sessionContext
        updateSessionContext(sessionContext, preSessionContext);

        // 处理指令
        executeAction(event, response.getActionList(), response);

        processAnswer(event, response);
    }

    /**
     * 处理大模型的响应结果
     */
    protected void processLLMResponse(LLMRequestEvent event, ChatResponse response) {
        String sessionContext = "";
        String preSessionContext = "";
        if (Objects.nonNull(response)) {
            sessionContext = response.getSessionContextJson();
            preSessionContext = response.getPreSessionContextJson();
            if (StringUtils.isNotBlank(response.getEventLogContent())) {
                eventLogContentList.add(response.getEventLogContent());
            }
            response.setEventLogContent(null);
            response.setSessionContextJson(null);
            response.setPreSessionContextJson(null);
        }
        log.info("处理响应信息, {\"event\":{}, \"response\":{} }", JsonUtils.object2String(event), JsonUtils.object2String(response));

        // 更新sessionContext
        updateSessionContext(sessionContext, preSessionContext);

        // 处理指令
        executeAction(event, response.getActionList(), response);

        // 追加答案
        appendAnswer(event, response.getAnswer(), response);
    }

    /**
     * 大模型生成的流式答案, 或者通过大模型跳转到新流程的答案
     */
    private void appendAnswer(EventParam event, AnswerResult answer, ChatResponse response) {
        log.debug("追加答案, answer={}", answer);
        if (answer == null) {
            return;
        }

        // 判断是否切换了答案, 如果切换, 则播放新答案
        if (!answerIsEqual(lastAnswer, answer)) {
            log.debug("生成新答案[{}], 不再播放之前的答案:[{}]", answer, lastAnswer);
            recordLastAnswer(answer);
            int playIndex = audioPlayManager.playAudio(answer);
            if (needWaitAnswerComplete(answer)) {
                audioPlayManager.appendPlaceholderFragment();
            }
            createAiSayRecord(audioPlayManager.getCurrentIndex(), answer.getId(), answer.getRealAnswer(), BooleanUtils.isTrue(answer.getIsCompleted()));
        } else {
            if (StringUtils.isNotBlank(answer.getRealAnswer())) {
                List<SimpleChatHistory> tmpList = processHistoryList(historyList, true);
                List<ChatHistoryItem> list = MyBeanUtils.copyList(tmpList, ChatHistoryItem.class);
                audioPlayManager.appendAnswer(answer, list);
            }
            if (!needWaitAnswerComplete(answer)) {
                // 在最后追加一个空的音频, 使占位符帧能够向下走
                audioPlayManager.appendCompleteFragment();
            }

            // 创建ai侧联系历史
            createAiSayRecord(audioPlayManager.getCurrentIndex(), answer.getId(), answer.getRealAnswer(), BooleanUtils.isTrue(answer.getIsCompleted()), response.getDebugLogList(), response.getSimpleDebugLogList());
        }
    }

    private void handleDynamic(ChatResponse response) {
        Map<String, String> playableVariableValueMap = response.getPlayableVariableValueMap();
        if (MapUtils.isEmpty(playableVariableValueMap)) {
            return;
        }
        Try.run(() -> audioPlayManager.onDynamicChanged(playableVariableValueMap))
                .onFailure(ex -> log.error(ex.getMessage(), ex));
    }

    private void executeAddWechat() {
        ApplicationExecutorHolder.execute("外呼中主动加微", new MDCDecoratorRunnable(oneTimeAddWechatExecutor::execute));
    }

    private void executeSwitchToHumanService() {
        log.info("开始执行转人工");
        executedTransferToHuman = true;
        if (Objects.nonNull(switchToHumanServiceExecutor)) {
            Try.run(() -> switchToHumanServiceExecutor.run()).onFailure(e -> log.error(e.getMessage(), e));
        }
        log.info("转人工结束");
    }

    private void executeHumanIntervention() {
        log.info("开始执行人工介入");
        if (Objects.nonNull(humanInterventionExecutor)) {
            Try.run(() -> humanInterventionExecutor.run()).onFailure(e -> log.error(e.getMessage(), e));
        }
        log.info("人工介入结束");
    }

    private void updateSessionContext(String responseSessionContextJson, String responsePreSessionContextJson) {
        String oldLogId = MDC.get("MDC_LOG_ID");
        try {
            if (StringUtils.length(oldLogId) > 20) {
                String newLogId = oldLogId.substring(6);
                MDC.put("MDC_LOG_ID", newLogId);
            }
            if (StringUtils.isNotBlank(responseSessionContextJson)) {
                sessionContextJson = responseSessionContextJson;
                if (DialogEngineConstant.enableDebug) {
                    log.debug("更新sessionContextJson={}", sessionContextJson);
                }
            }
            if (StringUtils.isNotBlank(responsePreSessionContextJson)) {
                preSessionContextJson = responsePreSessionContextJson;
                if (DialogEngineConstant.enableDebug) {
                    log.debug("更新preSessionContextJson={}", preSessionContextJson);
                }
            }
        } finally {
            MDC.put("MDC_LOG_ID", oldLogId);
        }
    }

    /**
     * 处理指令, 目前只有挂机需要处理
     */
    private void executeAction(EventParam event, List<ChatAction> actionList, ChatResponse response) {
        if (CollectionUtils.isEmpty(actionList)) {
            return;
        }
        actionList.stream()
                // 只需要执行交互层的action
                .filter(action -> ActionScopeEnum.INTERACTION.equals(action.getScope()))
                .forEach(action -> {
                    try {
                        switch (action.getType()) {
                            case WAIT:
                                // 等待事件理论上什么都不用处理
                                doWaitAction((WaitAction) action);
                                break;
                            case JUMP:
                                // jump只是engine那边的事件, 不会到这边来
                                log.warn("对话数据错误, 在交互层发现 jump 动作");
                                break;
                            case HANGUP:
                                doHangupAction((HangupAction) action);
                                break;
                            case PAUSE_PLAY:
                                pausePlay();
                                break;
                            case UNINTERRUPTED:
                                doUninterrupted(event, response);
                                break;
                            case IGNORE_INPUT:
                                executeIgnoreInput(event);
                                break;
                            case ADD_WECHAT:
                                executeAddWechat();
                                break;
                            case SWITCH_TO_HUMAN_SERVICE:
                                executeSwitchToHumanService();
                                break;
                            case WAIT_USER_SAY_FINISH:
                                executeWaitUserSayFinish(event, action);
                                break;
                            case HTTP_REQUEST:
                                executeHttpRequest((HttpRequestAction) action);
                                break;
                            case SEND_SMS:
                                executeSendSms((SendSmsAction) action);
                                break;
                            case LLM_REQUEST:
                                executeLLMRequest(event, response, (LLMRequestAction) action);
                                break;
                            case RESUME_PLAY:
                                resumeAnswerPlay((ResumePlayAction) action);
                                break;
                            case HUMAN_INTERVENTION:
                                executeHumanIntervention();
                                break;
                            default:
                                log.warn("对话数据错误, 在交互层发现未处理的指令{}", JsonUtils.object2String(action));
                                break;
                        }
                    } catch (Exception e) {
                        log.warn("执行{}失败", action.getClass().getSimpleName(), e);
                    }
                });
    }

    private void executeLLMRequest(EventParam userSayEvent, ChatResponse response, LLMRequestAction action) {
        // 已更新 sessionContext, 直接发送新请求
        log.debug("执行LLM请求");
        Integer preSequence = response.getSequence();
        LLMRequestEvent llmRequestEvent = new LLMRequestEvent();
        llmRequestEvent.setPreSequence(preSequence == null ? sequence.get() : preSequence);
        llmRequestEvent.setInputText(action.getInputText());
        llmRequestEvent.setAnswerId(action.getAnswerId());
        llmRequestEvent.setPlayProgress(action.getPlayProgress());
        if (requestChatHistoryListMap.containsKey(userSayEvent)) {
            llmRequestEvent.setChatHistoryList(requestChatHistoryListMap.get(userSayEvent));
        } else {
            llmRequestEvent.setChatHistoryList(processHistoryList(historyList));
        }
        submitAsyncRequest(llmRequestEvent);
    }

    private List<SimpleChatHistory> processHistoryList(List<SimpleChatHistory> historyList) {
        return processHistoryList(historyList, false);
    }

    private List<SimpleChatHistory> processHistoryList(List<SimpleChatHistory> historyList, boolean containsLastUserSayContent) {
        List<SimpleChatHistory> resultList = new ArrayList<>();

        log.debug("containsLastUserSayContent:{}, historyList:{}", containsLastUserSayContent, JsonUtils.object2String(historyList));

        if (CollectionUtils.isEmpty(historyList)) {
            return resultList;
        }

        // 将连续的n个取最后一个
        SimpleChatHistory pre = historyList.get(0);
        for (int i = 1; i < historyList.size(); i++) {
            SimpleChatHistory history = historyList.get(i);
            if (pre.getIndex() == history.getIndex() && StringUtils.equals(pre.getRole(), history.getRole())) {
                pre = history;
                continue;
            }
            resultList.add(pre);
            pre = history;
        }

        if (containsLastUserSayContent
                && !resultList.contains(pre)
                && ROLE_USER.equals(pre.getRole())) {
            resultList.add(pre);
        }

        log.debug("historyProcessResult:{}", JsonUtils.object2String(resultList));
        return resultList;
    }

    private void executeSendSms(SendSmsAction action) {
        log.info("发送短信, action={}", action);
        sendSmsExecutor.execute(action);
    }

    private void executeHttpRequest(HttpRequestAction httpRequestAction) {
        ApplicationExecutorHolder.execute("外呼中异步查询", new MDCDecoratorRunnable(
                () -> {
                    try {
                        QueryNodeApiTestReq httpRequestInfo = httpRequestAction.getHttpRequestInfo();
                        if (Objects.isNull(httpRequestInfo)) {
                            return;
                        }

                        log.info("调用接口={}", httpRequestInfo);
                        Map<String, String> varIdValueMap = botChatService.httpRequest(httpRequestInfo, session.getCallTaskId(), session.getUsageTarget());
                        log.info("接口响应={}", varIdValueMap);

                        // 同步接口响应信息
                        submitAsyncRequest(new DeliverHttpResponseEvent(varIdValueMap, httpRequestAction.getHttpRequestId()));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
        ));
    }

    private void executeWaitUserSayFinish(EventParam event, ChatAction action) {
        audioPlayManager.stop();
        WaitUserSayFinishAction waitUserSayFinishAction = (WaitUserSayFinishAction) action;
        updateUserSilence(waitUserSayFinishAction.getWaitUserSayFinishMs(), true);
        if (waitUserSayFinishContext.preUserSayInfo == null) {
            UserSayInfo userSayInfo = userSayInfoMap.get(event.getOffset());
            waitUserSayFinishContext.merge(userSayInfo);
        }
    }

    private void executeIgnoreInput(EventParam event) {
        // 忽略用户输入, 需要从输入栈中移除掉
        // 这种方式理论上是会出现: 输入内容还未移除时, 就已经开始下一轮对话的情况(这种一般是接口响应超时比较容易出现)
        UserSayInfo userSayInfo = userSayInfoMap.get(event.getOffset());
        if (Objects.nonNull(userSayInfo)) {
            userSayInfo.setIgnore(true);
            if (CollectionUtils.isNotEmpty(userSayInfo.getOriginInfoList())) {
                UserSayInfo last = userSayInfo.getOriginInfoList().get(userSayInfo.getOriginInfoList().size() - 1);
                last.setIgnore(true);
            }
            // 这里为和原逻辑保持一致(即先噪音过滤, 再断句补齐) 需要对用户输入内容进行替换
            // todo 这里应该要改成更好的实现
            if (event instanceof UserSayEvent) {
                UserSayEvent userSayEvent = (UserSayEvent) event;
                userSayEvent.setInputText(userSayInfo.getOriginText());
            }
            log.info("忽略用户输入, 从输入栈中移除掉当前文本内容:{}", userSayInfo.getOriginText());
        } else {
            log.warn("忽略用户输入, 但是没有找到对应的输入内容, event={}", event);
        }
        // 如果是断句补齐, 且不可打断, 此时对话状态已经回退了, 所以需要把已经回退了的状态再快进回去
        if (event instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) event;
            if (BooleanUtils.isTrue(userSayEvent.getIsMergeInput())) {
                forwardSessionContext();
            }
        }
    }

    private void doUninterrupted(EventParam event, ChatResponse response) {
        // 恢复音频播放
        resumePlay();

        if (preLastAnswer != lastAnswer) {
            preLastAnswer = lastAnswer;
            log.info("不可打断, 更新preLastAnswer=lastAnswer={}", lastAnswer);
        }

        if (ChatEventTypeEnum.USER_SAY.equals(event.getEvent())) {
            UserSayEvent userSayEvent = (UserSayEvent) event;
            String debugLog = "不可打断";
            if (CollectionUtils.isNotEmpty(response.getDebugLogList())) {
                response.getDebugLogList().add(debugLog);
            }
            if (CollectionUtils.isNotEmpty(response.getSimpleDebugLogList())) {
                response.getSimpleDebugLogList().add(debugLog);
            }
            createCantInterruptRecord(userSayEvent.getInputText(),
                    response.getDebugLogList(),
                    response.getSimpleDebugLogList(),
                    response.getSequence(),
                    userSayEvent.getStartOffset(),
                    userSayEvent.getEndOffset());
        }
    }

    /**
     * 快进对话状态
     */
    private void forwardSessionContext() {
        log.debug("快进对话状态");
        if (StringUtils.isNotBlank(beforeRollbackSessionContextJson)) {
            sessionContextJson = beforeRollbackSessionContextJson;
        } else {
            log.info("快进对话状态, 但是没有找到回退前的对话状态");
        }
        log.info("快进对话状态, sessionContext={}", sessionContextJson);
    }

    protected void doWaitAction(WaitAction waitAction) {
        updateUserSilence(waitAction.getUserSilenceMs());
    }

    private void updateUserSilence(Integer userSilenceMs) {
        updateUserSilence(userSilenceMs, false);
    }

    private void updateUserSilence(Boolean waitUserSayFinish) {
        updateUserSilence(null, waitUserSayFinish);
    }

    private void updateUserSilence(Integer userSilenceMs, Boolean waitUserSayFinish) {
        UserSilenceConfig preConfig = MyBeanUtils.copy(this.userSilenceConfig, UserSilenceConfig.class);
        boolean updated = false;
        if (Objects.nonNull(userSilenceMs)) {
            this.userSilenceConfig.userSilenceMs = userSilenceMs;
            updated = true;
        }
        if (Objects.nonNull(waitUserSayFinish)) {
            this.userSilenceConfig.waitUserSayFinish = BooleanUtils.isTrue(waitUserSayFinish);
            updated = true;
        }
        if (!this.userSilenceConfig.waitUserSayFinish) {
            waitUserSayFinishContext.reset();
        }
        if (updated) {
            log.info("更新用户无应答配置, preConfig={}, userSilenceConfig={}", preConfig, this.userSilenceConfig);
        }
    }

    private void doHangupAction(HangupAction action) {
        // 直接调用挂机操作, 也没有延迟挂机的逻辑
        if (BooleanUtils.isTrue(action.getHangupDelay()) && Objects.nonNull(action.getHangupDelayMs())) {
            log.info("接收到延迟挂机请求, 延迟:{}毫秒", action.getHangupDelayMs());
            long now = System.currentTimeMillis();
            this.hangupTimestamp = now + action.getHangupDelayMs();
            this.waitHangup = true;
        } else {
            hangup();
        }
    }

    /**
     * 取消延迟挂机状态
     * 在已经执行挂机了, 或者是播放答案了, 都需要取消
     */
    private void cancelHangupDelay() {
        if (this.hangupTimestamp > 0 || this.waitHangup) {
            log.info("取消挂机延迟状态");
        }
        this.hangupTimestamp = 0;
        this.waitHangup = false;
    }

    private void hangup() {
        log.info("hangup");
        this.finish = true;
        if (CollectionUtils.isNotEmpty(hangupEventListenerList)) {
            hangupEventListenerList.forEach(hangupEventListener -> {
                try {
                    hangupEventListener.onHangup();
                } catch (Exception e) {
                    log.warn("挂机事件处理失败, listener:{}", hangupEventListener.getClass().getSimpleName(), e);
                }
            });
        }
        audioPlayManager.onHangup();
        cancelHangupDelay();
    }

    // 对响应结果进行简单的校验
    private void validResponse(ChatResponse response) {

    }

    @Override
    public List<String> getAllUserSayContentList() {
        return userSayInfoStack.list.stream().map(UserSayInfo::getText).collect(Collectors.toList());
    }

    @Override
    public void onUserHangup() {
        finish();
        log.info("用户侧挂机");
    }

    @Override
    public void onFastHangup(String reason) {
        log.info("on fast hangup, reason={}", reason);
        double aiPlayProgress = audioPlayManager.getCurrentAnswerPlayPercent();
        execFastHangup(aiPlayProgress, reason);
    }

    @Override
    public String getLastAnswerId() {
        return Objects.nonNull(lastAnswer) ? lastAnswer.getId() : null;
    }

    @TraceCrossThread
    class EventSender {
        Deque<EventParam> requestQueue = new ArrayDeque<>();
        AtomicBoolean isWaiting = new AtomicBoolean(false);
        long startTimestamp = 0;

        private volatile ChatRequest lastLLMRequest;

        final Lock lock = new ReentrantLock();

        public void submitEvent(EventParam event) {
            if ((event instanceof KeyCaptureSuccessEvent || event instanceof KeyCaptureFailedEvent) && !dtmfManager.isWorking()) {
                log.warn("非按键采集期间，不触发采集事件");
                return;
            }
            if (event instanceof UserSilenceEvent && dtmfManager.isWorking()) {
                log.warn("按键采集期间，不触发无应答事件");
                return;
            }
            lock.lock();
            try {
                if (event instanceof LLMRequestEvent) {
                    // llmRequest插到对头
                    requestQueue.addFirst(event);
                } else {
                    // 先入队
                    requestQueue.addLast(event);
                }
                checkAndSubmitSendRequestTask();
            } finally {
                lock.unlock();
            }
        }

        private void checkAndSubmitSendRequestTask() {
            lock.lock();
            try {
                if (CollectionUtils.isEmpty(requestQueue)) {
                    return;
                }
                long now = System.currentTimeMillis();
                long processDuration = now - startTimestamp;
                if (isWaiting.compareAndSet(false, true)
                        || processDuration > 3000
                        || (lastLLMRequest != null && ChatEventTypeEnum.LLM_REQUEST.equals(lastLLMRequest.getParam().getEvent()))) {
                    // 提交到线程池
                    startTimestamp = System.currentTimeMillis();
                    EventParam firstEvent = requestQueue.poll();
                    while (isSkip(firstEvent)) {
                        firstEvent = requestQueue.poll();
                    }
                    if (firstEvent == null) {
                        isWaiting.set(false);
                        return;
                    }
                    if (ChatEventTypeEnum.LLM_REQUEST.equals(firstEvent.getEvent())) {
                        ChatRequest request = prepareRequest(firstEvent);
                        lastLLMRequest = request;
                        executor.execute("发送对话事件", new MDCDecoratorRunnable(createAsyncTask(firstEvent, request)));
                    } else {
                        ChatRequest request = prepareRequest(firstEvent);
                        if (!ChatEventTypeEnum.USER_SAY.equals(firstEvent.getEvent())
                                && !ChatEventTypeEnum.USER_SAY_FINISH.equals(firstEvent.getEvent()) ) {
                            lastLLMRequest = request;
                            log.debug("lastLLMRequest:{}", lastLLMRequest);
                        }
                        executor.execute("发送对话事件", new MDCDecoratorRunnable(createSyncTask(firstEvent, request)));
                    }
                } else {
                    log.info("isWaiting={}, processDuration={}", isWaiting.get(), processDuration);
                }
            } finally {
                lock.unlock();
            }
        }

        private Runnable createSyncTask(final EventParam event, ChatRequest request) {
            return () -> {
                MDC.put("MDC_LOG_ID", logId);
                if (finish) {
                    log.debug("对话已结束, 不再发送对话事件:{}", event);
                    return;
                }
                try {
                    String tmp = request.getSessionContextJson();
                    try {
                        request.setSessionContextJson(null);
                        log.info("发送对话事件, request={}", JsonUtils.object2StringNotNull(request));
                    } finally {
                        request.setSessionContextJson(tmp);
                    }
                    ChatResponse response = botChatService.sendEvent(request, session.getCallTaskId(), session.getUsageTarget());
                    if (!ChatEventTypeEnum.USER_SAY.equals(event.getEvent())) {
                        // 判断是否是语气词/噪音/不可打断
                        if (ChatEventTypeEnum.USER_SAY_FINISH.equals(event.getEvent())
                                && isUninterrupted(response)) {
                            log.debug("不可打断, 不更新lastLLMRequest");
                        } else {
                            lastLLMRequest = request;
                            log.debug("lastLLMRequest:{}", lastLLMRequest);
                        }
                    }
                    onResponse(request, response, null, true, false);
                } catch (Exception e) {
                    onResponse(request, null, e, true, false);
                } finally {
                    MDC.remove("MDC_LOG_ID");
                }
            };
        }

        private boolean isUninterrupted(ChatResponse response) {
            if (CollectionUtils.isNotEmpty(response.getActionList())) {
                return response.getActionList().stream().anyMatch(item -> ActionTypeEnum.UNINTERRUPTED.equals(item.getType()));
            }
            if (Objects.nonNull(response.getAnswerAudioPlayConfig())
                    && RepeatAnswerPlayStrategyEnum.RESUME.equals(response.getAnswerAudioPlayConfig().getRepeatPlayStrategy())) {
                return true;
            }
            return false;
        }

        private Runnable createAsyncTask(final EventParam event, ChatRequest request) {
            return () -> {
                MDC.put("MDC_LOG_ID", logId);
                if (finish) {
                    log.debug("对话已结束, 不再发送对话事件:{}", event);
                    return;
                }
                String tmp = request.getSessionContextJson();
                try {
                    request.setSessionContextJson(null);
                    log.info("发送对话事件, request={}", JsonUtils.object2StringNotNull(request));
                } finally {
                    request.setSessionContextJson(tmp);
                }
                try {
                    llmChatService.sendEvent(request, session.getCallTaskId(), session.getUsageTarget())
                            .doOnNext(response -> onResponse(request, response, null, false,false))
                            .doOnError(throwable -> {
                                onResponse(request, null, throwable, true, false);
                            })
                            .doOnComplete(() -> {
                                onResponse(request, null, null, true, false);
                            })
                            .subscribe();
                } catch (Exception e) {
                    onResponse(request, null, e, true, false);
                }
            };
        }

        private boolean isSkip(EventParam event) {
            if (event == null) {
                return false;
            }

            // 判断是否响应用户无应答事件的同时, 还有处理了userSayFinish事件
            if (checkUserSilenceNeedIgnore(event)) {
                return true;
            }

            // 判断是否是 usersay 时间, 且后续还有 usersayfinish 时间
            if (event.getEvent() == ChatEventTypeEnum.USER_SAY) {
                // 判断后续是否有 usersayfinish 事件
                 return hasUserSayFinishEvent();
            }

            return false;
        }

        private boolean hasUserSayFinishEvent() {
            if (requestQueue.isEmpty()) {
                return false;
            }
            for (EventParam event : requestQueue) {
                if (event.getEvent() == ChatEventTypeEnum.USER_SAY_FINISH) {
                    return true;
                }
            }
            return false;
        }

        private void onResponse(ChatRequest request,
                               ChatResponse response,
                               Throwable throwable,
                               boolean responseFinish,
                               boolean timeout) {
            MDC.put("MDC_LOG_ID", logId);
            try {
                if (throwable != null) {
                    log.error("发送对话事件异常", throwable);
                    log.info("发送对话事件异常, 先恢复录音播放(如果有暂停的话)");
                    resumePlay();
                }

                // 每次前一个发送的 request 响应时, 都在这里开始发送下一个 request
                try {
                    if (response != null) {
                        if (ChatEventTypeEnum.LLM_REQUEST.equals(request.getParam().getEvent())) {
                            if (lastLLMRequest == request) {
                                processLLMResponse((LLMRequestEvent) request.getParam(), response);
                            } else {
                                log.debug("已不是最新的请求, 忽略该响应");
                            }
                        } else {
                            processResponse(request.getParam(), response);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理响应结果异常", e);
                }

                try {
                    isWaiting.set(false);
                    checkAndSubmitSendRequestTask();
                } catch (Exception e) {
                    log.error("处理响应结果异常", e);
                }
            } finally {
                MDC.remove("MDC_LOG_ID");
            }
        }
    }

    class CallDetailCallbackAdaptor implements AudioPlayEventListener, CallDetailListener {

        volatile List<Object> recordList = new ArrayList<>();

        private volatile long startPlayTimestamp = 0;

        /**
         * 累计读取的音频时长, 单位毫秒, 读取一次累积 20mS
         */
        private volatile long readAudioTimeMs = 0L;

        private volatile int lastIndex;

        private AtomicBoolean callback = new AtomicBoolean();

        private Lock lock = new ReentrantLock();

        private final ConcurrentHashMap<Integer, Long> indexStartOffsetMap = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<Integer, Long> indexEndOffsetMap = new ConcurrentHashMap<>();

        @Override
        public void createAiSayRecord(SimpleAiSayResultInfo simpleAiSayResultInfo) {
            lock.lock();
            try {
                simpleAiSayResultInfo.setBeginTime(indexStartOffsetMap.getOrDefault(simpleAiSayResultInfo.getSequence(), 0L));
                simpleAiSayResultInfo.setEndTime(indexEndOffsetMap.getOrDefault(simpleAiSayResultInfo.getSequence(), 0L));
                log.debug("createAiSayRecord:{}", JsonUtils.object2String(simpleAiSayResultInfo));
                recordList.add(simpleAiSayResultInfo);
            } finally {
                lock.unlock();
            }
        }

        @Override
        public void createUserSayRecord(SimpleUserSayResultInfo simpleUserSayInfo) {
            lock.lock();
            try {
                recordList.add(simpleUserSayInfo);
            } finally {
                lock.unlock();
            }
        }

        @Override
        public void onReadData() {
            if (startPlayTimestamp == 0) {
                startPlayTimestamp = System.currentTimeMillis();
            }
            readAudioTimeMs += 20;
        }

        @Override
        public void updatePlayAudioTime(int index) {
            updateAiPlayEndOffset(index);
        }

        private void updateAiPlayBeginOffset(int index) {
            lock.lock();
            try {
                indexStartOffsetMap.put(index, getTimeOffset());
                if (CollectionUtils.isNotEmpty(recordList)) {
                    for (Object record : recordList) {
                        if (record instanceof SimpleAiSayResultInfo) {
                            SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                            if (index == simpleAiSayResultInfo.getSequence()) {
                                simpleAiSayResultInfo.setBeginTime(getTimeOffset());
                            }
                        }
                    }
                }
            } finally {
                lock.unlock();
            }
        }

        private void updateAiPlayEndOffset(int index) {
            lock.lock();
            try {
                indexEndOffsetMap.put(index, getTimeOffset());
                if (CollectionUtils.isNotEmpty(recordList)) {
                    for (Object record : recordList) {
                        if (record instanceof SimpleAiSayResultInfo) {
                            SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                            if (index == simpleAiSayResultInfo.getSequence()) {
                                simpleAiSayResultInfo.setEndTime(getTimeOffset());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理 updateAiPlayEndOffset 异常, index:{}", index, e);
            } finally {
                lock.unlock();
            }
        }

        @Override
        public void onAiPlayBegin(int index) {
            log.debug("onAiPlayBegin:{}", index);
            lastIndex = index;
            updateAiPlayBeginOffset(index);
            submitCallback(false);
        }

        @Override
        public void onAiPlayEnd(int index) {
            log.debug("onAiPlayEnd:{}", index);
            updateAiPlayEndOffset(index);
            submitCallback(true);
        }

        @Override
        public void onPause(int index) {
            log.debug("onPause:{}", index);
            updateAiPlayEndOffset(index);
        }

        @Override
        public void onResume(int index) {

        }

        private long getTimeOffset() {
            long start = 0 == startPlayTimestamp ? System.currentTimeMillis() : startPlayTimestamp;
            long delay = start - startTimestamp;
            return delay + readAudioTimeMs;
        }

        public void submitCallback(boolean callbackAll) {
            lock.lock();
            try {
                if (CollectionUtils.isNotEmpty(recordList)) {
                    log.debug("回调联系历史, recordList:{}", JsonUtils.object2String(recordList));
                    List<Object> tmpList = new ArrayList<>();
                    boolean stop = false;
                    for (Object record : recordList) {
                        if (stop) {
                            tmpList.add(record);
                        } else {
                            if (record instanceof SimpleAiSayResultInfo) {
                                SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                                if (!callbackAll && simpleAiSayResultInfo.getSequence() >= lastIndex) {
                                    stop = true;
                                    tmpList.add(record);
                                    continue;
                                }
                                if (CollectionUtils.isNotEmpty(realCallDetailListenerList)) {
                                    for (CallDetailListener callDetailListener : realCallDetailListenerList) {
                                        try {
                                            callDetailListener.createAiSayRecord(simpleAiSayResultInfo);
                                        } catch (Exception e) {
                                            log.warn("[LogHub_Warn] 回调联系历史处理失败", e);
                                        }
                                    }
                                }
                            } else if (record instanceof SimpleUserSayResultInfo) {
                                SimpleUserSayResultInfo simpleUserSayResultInfo = (SimpleUserSayResultInfo) record;
                                for (CallDetailListener callDetailListener : realCallDetailListenerList) {
                                    try {
                                        callDetailListener.createUserSayRecord(simpleUserSayResultInfo);
                                    } catch (Exception e) {
                                        log.warn("[LogHub_Warn] 回调联系历史处理失败", e);
                                    }
                                }
                            }
                        }
                    }
                    recordList = tmpList;
                }
            } finally {
                lock.unlock();
            }
        }
    }

    private void recordStatus() {
        // 记录对话状态
        preSessionContextJson = sessionContextJson;
        recordInterruptConfigSnapshot();
        preHistoryList.clear();
        preHistoryList.addAll(historyList);
    }

    private void rollbackStatus() {
        log.info("回退对话状态");
        lastAnswer = preLastAnswer;
        if (StringUtils.isNotBlank(preSessionContextJson)) {
            beforeRollbackSessionContextJson = sessionContextJson;
            sessionContextJson = preSessionContextJson;
            log.info("回退sessionContextJson, preSessionContextJson={}", preSessionContextJson);
        } else {
            log.warn("回退状态失败, preSessionContextJson为空");
        }
        historyList.clear();
        historyList.addAll(preHistoryList);
    }

    private ChatRequest prepareRequest(EventParam event) {
        if (event instanceof UserSayFinishEvent) {
            // 记录sessionContextJson或者回退
            UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) event;
            if (BooleanUtils.isTrue(userSayFinishEvent.getIsMergeInput())) {
                // 回退对话状态
                rollbackStatus();
            } else {
                recordStatus();
            }
        }

        ChatRequest request = new ChatRequest();
        request.setSessionContextJson(sessionContextJson);
        request.setBotId(session.getBotId());
        request.setVersion(session.getVersion());
        request.setUsageTarget(session.getUsageTarget());
        request.setSessionId(session.getSessionId());
        request.setSequence(sequence.getAndIncrement());
        request.setParam(event);

        if (event instanceof UserSilenceEvent) {
            userSilenceEventStashContext.sessionContext = sessionContextJson;
            userSilenceEventStashContext.preSessionContext = preSessionContextJson;
        }

        if (checkUserSayFinishNeedRollback(event) && StringUtils.isNotBlank(userSilenceEventStashContext.sessionContext)) {
            // 回退状态吧
            request.setSessionContextJson(userSilenceEventStashContext.sessionContext);
            request.setPreSessionContextJson(userSilenceEventStashContext.preSessionContext);
            if (event instanceof UserSayFinishEvent) {
                // 这里需要把音频播放进度设置为100%, 因为之前已经触发用户无应答了, 所以前面的音频肯定是播放完成了的
                UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) event;
                userSayFinishEvent.setPlayProgress(100.0d);
            }
            log.debug("回退对话状态只最近一次用户无应答时刻");
        }

        return request;
    }

    private boolean checkUserSayFinishNeedRollback(EventParam event) {
        if (!(event instanceof UserSayFinishEvent)) {
            return false;
        }
        // 这里只需要很简单的判断上一次用户无应答时间的偏移量, 如果小于xxx毫秒, 这个时候其实都是需要重新请求的
        long currentOffset = getOffset();

        if ((currentOffset - userSilenceEventStashContext.lastResponseUserSilenceOffset) < DialogEngineConstant.ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS
                && userSilenceEventStashContext.lastResponseUserSilenceOffset > 0) {
            log.debug("检测到userSayFinish和userSilence事件间隔小于{}ms, 应该需要回退到userSilence状态前重新请求, currentOffset={}, lastResponseUserSilenceOffset={}",
                    DialogEngineConstant.ROLLBACK_USER_SILENCE_EVENT_INTERVAL_MS, currentOffset, userSilenceEventStashContext.lastResponseUserSilenceOffset);
            return true;
        }
        return false;
    }

    @Data
    public static class UserSayInfo {
        String text;
        String originText;
        long beginTime;
        long endTime;
        boolean merged;

        boolean necessaryMerge;

        volatile boolean ignore;

        List<UserSayInfo> originInfoList;

        public UserSayInfo() {
            originInfoList = new ArrayList<>();
        }

        public UserSayInfo(String text, long begin, long end) {
            this();
            this.text = text;
            this.originText = text;
            this.beginTime = begin;
            this.endTime = end;
        }
    }

    static class UserSayInfoStack {
        LinkedList<UserSayInfo> list = new LinkedList<>();

        Optional<UserSayInfo> pop() {
            if (CollectionUtils.isEmpty(list)) {
                return Optional.empty();
            }
            return Optional.of(list.pop());
        }

        Optional<UserSayInfo> peek() {
            if (CollectionUtils.isEmpty(list)) {
                return Optional.empty();
            }
            return Optional.ofNullable(list.peek());
        }

        void push(UserSayInfo info) {
            if (Objects.nonNull(info)) {
                list.push(info);
            }
        }
    }

    /**
     * 等待用户说完栈, 如果userSilenceConfig.waitUserSayFinish为true, 则需要等待用户说完
     * 在用户输入完成后, 需要把前面的输入合并发送给服务端
     */
    static class WaitUserSayFinishContext {
        volatile UserSayInfo preUserSayInfo;
        /**
         * 将多个用户输入合并成一个
         * @return 合并后的用户输入
         */
        UserSayInfo merge(UserSayInfo userSayInfo) {
            log.info("WaitUserSayFinishContext.merge, preUserSayInfo={}, userSayInfo={}", preUserSayInfo, userSayInfo);
            if (preUserSayInfo == null) {
                preUserSayInfo = MyBeanUtils.copy(userSayInfo, UserSayInfo.class);
                return preUserSayInfo;
            }

            if (userSayInfo == preUserSayInfo) {
                return preUserSayInfo;
            }

            // 当断句补齐和等待用户输入同时触发时
            // preUserSayInfo已经被合并到 userSayInfo 中了, 那么后面又再次将 userSayInfo 合并到 preUserSayInfo 中
            // 导致出现循环引用
            // 所以在断句补齐时, 同时也需要请求 reset() 清空 preUserSayInfo
            preUserSayInfo.text = preUserSayInfo.text + ", " +  userSayInfo.originText;
            preUserSayInfo.originText = preUserSayInfo.originText + ", " +  userSayInfo.originText;
            preUserSayInfo.endTime = userSayInfo.endTime;
            preUserSayInfo.originInfoList.add(userSayInfo);
            log.info("合并用户输入, preUserSayInfo={}", preUserSayInfo);
            return preUserSayInfo;
        }

        void reset() {
            preUserSayInfo = null;
        }
    }

    /**
     * 用来实现对大模型生成的流式答案进行累加处理， 多个分段合并为一个结果
     * 因为底层时异步入库的， 所以在话术这边处理，等待大模型生成的分段数据完成/超时 才会通过回调传给外呼引擎
     */
    @Data
    private class StreamRecordAccumulator {
        Queue<StreamRecord> queue = new ArrayDeque<>();

        volatile StreamRecord lastAiRecord;

        void enqueueAndTryCallback(StreamRecord record) {
            queue.add(record);
            tryCallback();
        }

        void tryCallback() {
            if (queue.isEmpty()) {
                return;
            }

            while (true) {
                StreamRecord first = queue.peek();
                if (first == null) {
                    return;
                }
                if (first.wait && first.timeoutMs > System.currentTimeMillis()) {
                    return;
                }
                StreamRecord record = queue.poll();
                if (record == first) {
                    doCallback(record);
                }
            }
        }

        void callbackAll() {
            while (true) {
                StreamRecord first = queue.poll();
                if (first == null) {
                    return;
                }
                doCallback(first);
            }
        }

        private void doCallback(StreamRecord record) {
            if (record.isAiRecord) {
                if (CollectionUtils.isNotEmpty(callDetailListenerList)) {
                    String text = String.join("", record.getAnswerList());
                    List<String> debugLogList = record.getDebugLogList().stream().distinct().collect(Collectors.toList());
                    List<String> simpleDebugLogList = record.getSimpleDebugLogList().stream().distinct().collect(Collectors.toList());
                    if (StringUtils.isNotBlank(text)) {
                        for (CallDetailListener callDetailListener : callDetailListenerList) {
                            try {
                                SimpleAiSayResultInfo simpleUserSayResultInfo = new SimpleAiSayResultInfo();
                                simpleUserSayResultInfo.setSequence(record.getIndex());
                                simpleUserSayResultInfo.setAnswer(text);
                                simpleUserSayResultInfo.setDebugLogList(debugLogList);
                                simpleUserSayResultInfo.setSimpleDebugLogList(simpleDebugLogList);
                                callDetailListener.createAiSayRecord(simpleUserSayResultInfo);
                            } catch (Exception e) {
                                log.warn("创建ai侧联系历史失败", e);
                            }
                        }
                    }
                }
            } else {
                if (CollectionUtils.isNotEmpty(callDetailListenerList)) {
                    for (CallDetailListener callDetailListener : callDetailListenerList) {
                        try {
                            callDetailListener.createUserSayRecord(record.getSimpleUserSayResultInfo());
                        } catch (Exception e) {
                            log.warn("创建用户侧联系历史失败", e);
                        }
                    }
                }
            }
        }

        StreamRecord createAiSayRecord(int playIndex,
                                       String answerId,
                                       String answerText,
                                       boolean finish,
                                       List<String> debugLogList,
                                       List<String> simpleDebugLogList) {
            if (answerText == null) {
                answerText = "";
            }

            // 防止重复上一句拼接多余的答案
            if (lastAiRecord != null && !lastAiRecord.wait) {
                lastAiRecord = null;
            }

            StreamRecord lastRecord = lastAiRecord;

            if (lastRecord != null
                    && StringUtils.equals(lastRecord.getAnswerId(), answerId)
                    && isInQueue(lastRecord)) {
                lastRecord.getAnswerList().add(answerText);
                if (CollectionUtils.isNotEmpty(debugLogList)) {
                    lastRecord.getDebugLogList().addAll(debugLogList);
                }
                if (CollectionUtils.isNotEmpty(simpleDebugLogList)) {
                    lastRecord.getSimpleDebugLogList().addAll(simpleDebugLogList);
                }
                if (finish) {
                    lastRecord.setWait(false);
                    tryCallback();
                }
                return lastAiRecord;
            }

            if (lastRecord != null && isInQueue(lastRecord)) {
                // 已经切换新的文本了
                lastRecord.setWait(false);
            }

            StreamRecord streamRecord = new StreamRecord();
            streamRecord.setAnswerId(answerId);
            streamRecord.setIndex(playIndex);
            streamRecord.setAiRecord(true);
            streamRecord.setWait(!finish);
            streamRecord.getAnswerList().add(answerText);
            streamRecord.setTimeoutMs(System.currentTimeMillis() + 5000);
            if (CollectionUtils.isNotEmpty(debugLogList)) {
                streamRecord.getDebugLogList().addAll(debugLogList);
            }
            if (CollectionUtils.isNotEmpty(simpleDebugLogList)) {
                streamRecord.getSimpleDebugLogList().addAll(simpleDebugLogList);
            }
            this.lastAiRecord = streamRecord;
            enqueueAndTryCallback(streamRecord);
            return streamRecord;
        }

        boolean isInQueue(StreamRecord record) {
            return queue.contains(record);
        }

        void createUserSayRecord(SimpleUserSayResultInfo userSayResultInfo) {
            StreamRecord streamRecord = new StreamRecord();
            streamRecord.setSimpleUserSayResultInfo(userSayResultInfo);

            enqueueAndTryCallback(streamRecord);
        }
    }

    @Data
    private static class StreamRecord {
        int index;
        String answerId;
        boolean wait;
        long timeoutMs;
        boolean isAiRecord;
         // ai侧联系历史会追加到 answerList 里
        List<String> answerList = new ArrayList<>();
         // 用户侧
        SimpleUserSayResultInfo simpleUserSayResultInfo;

        List<String> debugLogList = new ArrayList<>();
        List<String> simpleDebugLogList = new ArrayList<>();
    }

}