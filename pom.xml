<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yiwise.dialogflow</groupId>
    <artifactId>aicc-platform-dialogflow-web</artifactId>
    <version>daily-2.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>dialogflow-api</module>
        <module>dialogflow-api-web</module>
        <module>dialogflow-common</module>
        <module>dialogflow-common-web-config</module>
        <module>dialogflow-common-webmvc-config</module>
        <module>dialogflow-common-webflux-config</module>
        <module>dialogflow-engine</module>
        <module>dialogflow-engine-share</module>
        <module>dialogflow-web</module>
        <module>dialogflow-chat-web</module>
        <module>dialogflow-chat-client</module>
        <module>service-api</module>
        <module>service-impl</module>
    </modules>

    <properties>
        <project.version>daily-2.1.0-SNAPSHOT</project.version>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <base.service.version>6.0.0-RELEASE</base.service.version>
        <yiwise.middleware.version>5.0.0-RELEASE</yiwise.middleware.version>
        <yiwise.middleware.redis.version>5.0.0-RELEASE</yiwise.middleware.redis.version>
        <yiwise.middleware.objectstorage.version>5.0.0-RELEASE</yiwise.middleware.objectstorage.version>

        <spring.boot.version>2.2.6.RELEASE</spring.boot.version>
        <spring.cloud.version>Hoxton.SR4</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.1</spring.cloud.alibaba.version>
        <slf4j.version>1.7.36</slf4j.version>
        <log4j.version>2.21.1</log4j.version>
        <druid.version>1.2.15</druid.version>
        <mysql.connector.j.version>8.0.32</mysql.connector.j.version>
        <lombok.version>1.18.30</lombok.version>
        <caffeine.version>2.9.3</caffeine.version>

        <maven.source.plugin.version>3.1.0</maven.source.plugin.version>
        <maven.compiler.plugin.version>2.3.2</maven.compiler.plugin.version>
        <maven.jar.plugin.version>3.2.0</maven.jar.plugin.version>
        <maven.resources.plugin.version>3.3.0</maven.resources.plugin.version>
        <maven.deploy.plugin.version>3.0.0</maven.deploy.plugin.version>
        <json.variable.filter.version>1.1-SNAPSHOT</json.variable.filter.version>
        <codehaus.janino.version>3.0.7</codehaus.janino.version>
        <aicc-platform-openfeign-api.version>2.0.0-RELEASE</aicc-platform-openfeign-api.version>
        <account-service-api.version>2.0.4-RELEASE</account-service-api.version>
        <lcs-api.version>2.0.0-RELEASE</lcs-api.version>
        <rcs-api.version>2.0.0-RELEASE</rcs-api.version>
        <callout-job-api.version>2.4.4-SNAPSHOT</callout-job-api.version>
        <base-common.version>6.0.0-RELEASE</base-common.version>
        <base-monitoring.version>6.0.0-RELEASE</base-monitoring.version>
        <quartz.version>2.2.3</quartz.version>
        <middleware.tts.version>5.2.5-SNAPSHOT</middleware.tts.version>
        <reactor-netty.version>0.9.6.RELEASE</reactor-netty.version>
        <call-activity-api.version>2.4.3-RELEASE</call-activity-api.version>
        <yiwise-agent-service-api.version>1.1.2-SNAPSHOT</yiwise-agent-service-api.version>
        <yiwise.aicc.common>1.0.4-SNAPSHOT</yiwise.aicc.common>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!--项目模块 start-->
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-api-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-common-web-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-common-webmvc-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-common-webflux-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-engine-share</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-chat-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-chat-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>service-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--项目模块 end-->

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-common</artifactId>
                <version>${base-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-monitoring</artifactId>
                <version>${base-monitoring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>account-service-api</artifactId>
                <version>${account-service-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.41</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.yiwise.cloud</groupId>
                <artifactId>aicc-platform-openfeign-api</artifactId>
                <version>${aicc-platform-openfeign-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-model</artifactId>
                <version>${base.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.agent</groupId>
                <artifactId>yiwise-agent-service-api</artifactId>
                <version>${yiwise-agent-service-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-monitoring</artifactId>
                <version>${base.service.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-MySQL</artifactId>
                <version>${yiwise.middleware.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-MongoDB</artifactId>
                <version>${yiwise.middleware.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-ObjectStorage</artifactId>
                <version>${yiwise.middleware.objectstorage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-Redis</artifactId>
                <version>${yiwise.middleware.redis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>call-activity-api</artifactId>
                <version>${call-activity-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>callout-job-api</artifactId>
                <version>${callout-job-api.version}</version>
            </dependency>


            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>1.7.1</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!--不使用, 覆盖其他依赖引入的有漏洞的旧版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${codehaus.janino.version}</version>
            </dependency>

            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-Tts</artifactId>
                <version>${middleware.tts.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.tencentcloudapi</groupId>
                        <artifactId>tencentcloud-sdk-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty</artifactId>
                <version>${reactor-netty.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>23.5-jre</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.3</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>2.2.1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>aicc-common</artifactId>
                <version>${yiwise.aicc.common}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven.source.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.resources.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven.deploy.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.yiwise</groupId>
                    <artifactId>json-variable-filter</artifactId>
                    <version>1.1-RELEASE</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>2.2.4</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgument>
                        -parameters
                    </compilerArgument>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>pl.project13.maven</groupId>-->
<!--                <artifactId>git-commit-id-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>get-the-git-infos</id>-->
<!--                        <goals>-->
<!--                            <goal>revision</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 使properties扩展到整个maven build 周期-->
<!--                    Ref: https://github.com/ktoso/maven-git-commit-id-plugin/issues/280 &ndash;&gt;-->
<!--                    <injectAllReactorProjects>true</injectAllReactorProjects>-->
<!--                    <dateFormat>yyyy.MM.dd HH:mm:ss</dateFormat>-->
<!--                    <verbose>true</verbose>-->
<!--                    &lt;!&ndash; 是否生 git.properties 属性文件 &ndash;&gt;-->
<!--                    <generateGitPropertiesFile>false</generateGitPropertiesFile>-->
<!--                    &lt;!&ndash;指定"git.properties"文件的存放路径(相对于${project.basedir}的一个路径);&ndash;&gt;-->
<!--                    &lt;!&ndash;<generateGitPropertiesFilename>git.properties</generateGitPropertiesFilename>&ndash;&gt;-->
<!--                    &lt;!&ndash;git描述配置,可选;由JGit提供实现;&ndash;&gt;-->
<!--                    <gitDescribe>-->
<!--                        &lt;!&ndash;是否生成描述属性&ndash;&gt;-->
<!--                        <skip>false</skip>-->
<!--                        &lt;!&ndash;提交操作未发现tag时,仅打印提交操作ID,&ndash;&gt;-->
<!--                        <always>false</always>-->
<!--                        &lt;!&ndash;提交操作ID显式字符长度,最大值为:40;默认值:7; 0代表特殊意义;后面有解释; &ndash;&gt;-->
<!--                        <abbrev>7</abbrev>-->
<!--                        &lt;!&ndash;构建触发时,代码有修改时(即"dirty state"),添加指定后缀;默认值:"";&ndash;&gt;-->
<!--                        <dirty>-dirty</dirty>-->
<!--                        &lt;!&ndash;always print using the "tag-commits_from_tag-g_commit_id-maybe_dirty" format, even if "on" a tag. The distance will always be 0 if you're "on" the tag. &ndash;&gt;-->
<!--                        <forceLongFormat>false</forceLongFormat>-->
<!--                    </gitDescribe>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- env用于子pom中指定filter文件 -->
                <env>local</env>
                <!-- buildEnv用于build_parameter.json中获取当前build的环境 -->
                <buildEnv>daily</buildEnv>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>daily</id>
            <properties>
                <env>daily</env>
                <buildEnv>daily</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>daily-huoshan</id>
            <properties>
                <env>daily-huoshan</env>
                <buildEnv>daily-huoshan</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
                <buildEnv>pre</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>pre-finance</id>
            <properties>
                <env>pre-finance</env>
                <buildEnv>pre-finance</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
                <buildEnv>prod</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-huoshan</id>
            <properties>
                <env>prod-huoshan</env>
                <buildEnv>prod-huoshan</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-huawei</id>
            <properties>
                <env>prod-huawei</env>
                <buildEnv>prod-huawei</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-finance</id>
            <properties>
                <env>prod-finance</env>
                <buildEnv>prod-finance</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>Windows</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.bat</script.extension>
            </properties>
        </profile>
        <profile>
            <id>unix</id>
            <activation>
                <os>
                    <family>unix</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.sh</script.extension>
            </properties>
        </profile>
        <profile>
            <id>mac</id>
            <activation>
                <os>
                    <family>mac</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.sh</script.extension>
            </properties>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>${repository.id}</id>
            <name>${repository.name}</name>
            <url>${repository.url}</url>
        </repository>
        <snapshotRepository>
            <id>${snapshotRepository.id}</id>
            <name>${snapshotRepository.name}</name>
            <url>${snapshotRepository.url}</url>
        </snapshotRepository>
    </distributionManagement>


</project>