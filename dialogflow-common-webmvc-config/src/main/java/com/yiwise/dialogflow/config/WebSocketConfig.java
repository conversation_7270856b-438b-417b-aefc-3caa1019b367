package com.yiwise.dialogflow.config;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.http.CookieUtils;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.model.websocket.UserIdPrincipal;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.BotResourceModifyLockService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import com.yiwise.dialogflow.utils.TokenUtils;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.SimpMessageType;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.scheduling.concurrent.DefaultManagedTaskScheduler;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import sun.net.util.IPAddressUtil;

import javax.annotation.Resource;
import java.net.HttpCookie;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yiwise.dialogflow.common.ApplicationConstant.WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL;

@Slf4j
@Order(value = Ordered.HIGHEST_PRECEDENCE)
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private BotResourceModifyLockService botResourceModifyLockService;

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // Message的destination如果是以/app开头，则会转发给响应的消息处理方法（如使用@MessageMapping注解的方法）
        //指服务端接收地址的前缀，意思就是说客户端给服务端发消息的地址的前缀
        registry.setApplicationDestinationPrefixes("/app");

        // 如果是以/topic,/queue开头则会被转发给消息代理（broker），由broker广播给连接的客户端，topic语义为1-n(广播机制),queue语义为1-1(单点机制)
        //客户端接收服务端消息的地址的前缀信息
        registry.enableSimpleBroker("/topic", "/queue").setHeartbeatValue(new long[]{10000, 10000}).setTaskScheduler(new DefaultManagedTaskScheduler());

        // 点对点使用的订阅前缀（客户端订阅路径上会体现出来），不设置的话，默认也是/user/
        registry.setUserDestinationPrefix("/user/");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 下面的配置中，注册了一个前缀为/webSocket的stomp终端，客户端可以使用该url来建立WebSocket连接(只用来建立连接使用)。
        registry.addEndpoint(ApplicationConstant.WEBSOCKET_TRAIN_CONNECT_URL,
                        ApplicationConstant.WEBSOCKET_TTS_JOB_CONNECT_URL,
                        ApplicationConstant.WEBSOCKET_BOT_GENERATE_CONNECT_URL,
                        ApplicationConstant.WEBSOCKET_BOT_REWRITE_CONNECT_URL,
                        WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL)
                .addInterceptors(new CustomHandshakeInterceptor())
                .setAllowedOrigins("*")
                .withSockJS();
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {

        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
                Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
                URI endPoint = (URI) sessionAttributes.get("endPoint");
                if (endPoint == null) {
                    throw new ComException(ComErrorCode.NOT_SUPPORT);
                }
                String path = endPoint.getPath();
                if (path == null) {
                    throw new ComException(ComErrorCode.NOT_SUPPORT);
                }
                Object msgType = accessor.getHeader("simpMessageType");
                if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                    login(message);
                    if (path.startsWith(ApplicationConstant.WEBSOCKET_BOT_GENERATE_CONNECT_URL)) {
                        Long userId = checkAndGetUserId();
                        UserIdPrincipal userIdPrincipal = new UserIdPrincipal(userId, accessor.getSessionId());
                        accessor.setUser(userIdPrincipal);
                    }
                    if (path.startsWith(WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL)) {
                        botResourceModifyLockService.onActive(accessor.getSessionId(), SecurityUtils.getUserId());
                    }
                } else if (StompCommand.DISCONNECT.equals(accessor.getCommand())) {
                    // 断开连接
                    if (path.startsWith(WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL)) {
                        botResourceModifyLockService.onInactive(accessor.getSessionId());
                    }
                } else {
                    if (path.startsWith(WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL)
                            && msgType instanceof SimpMessageType && SimpMessageType.HEARTBEAT.equals(msgType)) {
                        // 心跳
                        botResourceModifyLockService.onHeartbeat(accessor.getSessionId());
                    }
                }
                return message;
            }

            @Override
            public void postSend(Message<?> message, MessageChannel channel, boolean sent) {
                SecurityUtils.remove();
            }
        });
    }

    private void login(Message<?> message) {
        LinkedMultiValueMap nativeHeaderMap = message.getHeaders().get("nativeHeaders", LinkedMultiValueMap.class);
        String cookieStr = (String) nativeHeaderMap.getFirst("Cookie");

        List<HttpCookie> httpCookieList = CookieUtils.parseCookieStr(cookieStr);
        Map<String, HttpCookie> httpCookieMap = MyCollectionUtils.listToMap(httpCookieList, HttpCookie::getName);
        HttpCookie tokenCookie = httpCookieMap.get(TokenUtils.TOKEN);
        if (tokenCookie == null) {
            throw new ComException(ComErrorCode.FORBIDDEN);
        }
        String token = tokenCookie.getValue();
        String decodeToken = new String(Base64.decodeBase64(token.getBytes()));

        // todo
        SecurityUtils.flushUserInfo(getSystem(message), decodeToken, redisOpsService);
        UserPO user = SecurityUtils.getUserInfo();
        if (user == null) {
            throw new ComException(ComErrorCode.FORBIDDEN);
        }
    }

    private SystemEnum getSystem(Message<?> message) {
        Map map = message.getHeaders().get("simpSessionAttributes", Map.class);
        if (Objects.isNull(map)) {
            return SystemEnum.OPE;
        }
        Object hostObj = map.get("host");
        if (Objects.isNull(hostObj)) {
            return SystemEnum.OPE;
        }
        String host = hostObj.toString();
        String systemType = "OPE";
        // 兼容开发自测
        if (host.contains("ope") || host.contains("fi-ope") || host.startsWith("localhost") || IPAddressUtil.isIPv4LiteralAddress(StringUtils.substringBefore(host,":"))) {
            systemType = "OPE";
        } else if (host.contains("miniapp") || host.contains("fi-miniapp")){
            systemType = "MINIAPP";
        } else {
            systemType = "AICC";
        }
        return SystemEnum.valueOf(systemType);
    }


    private Long checkAndGetUserId() {
        // 异常处理，避免抛出空指针异常
        if (SecurityUtils.getUserInfo() == null) {
            throw new ComException(ComErrorCode.USER_NOT_LOGIN, "用户未登录");
        }
        return SecurityUtils.getUserId();
    }

}