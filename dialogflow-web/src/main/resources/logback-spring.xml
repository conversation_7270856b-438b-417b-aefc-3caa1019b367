<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <define name="ip" class="com.yiwise.base.common.log.IPLogPropertyDefiner" />
    <property name="baseDirectory" value="logs"/>
    <property name="pattern"
              value="%d{HH:mm:ss.SSS} [%boldYellow(%-15.15thread)] [%X{MDC_LOG_ID}] %highlight(%-5level) %boldGreen(%-40.40logger{39}){cyan} - %msg %n"/>
    <property name="HOSTNAME_IP" value="${HOSTNAME} - ${ip}"/>
    <property name="HOSTNAME_IP" value="${HOSTNAME} - ${ip}"/>
    <springProperty scope="context" name="logHub.endpoint" source="logHub.endpoint"/>
    <springProperty scope="context" name="logHub.accessKeyId" source="logHub.accessKeyId"/>
    <springProperty scope="context" name="logHub.accessKeySecret" source="logHub.accessKeySecret"/>
    <springProperty scope="context" name="logHub.projectName" source="logHub.projectName"/>
    <springProperty scope="context" name="logHub.logstore" source="logHub.logstore"/>

    <property name="logHub.endpoint" value="${logHub.endpoint}"/>
    <property name="logHub.accessKeyId" value="${logHub.accessKeyId}"/>
    <property name="logHub.accessKeySecret" value="${logHub.accessKeySecret}"/>
    <property name="logHub.projectName" value="${logHub.projectName}"/>
    <property name="logHub.logstore" value="${logHub.logstore}"/>

    <appender name="rollingFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${baseDirectory}/dialogflow-web.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${baseDirectory}/dialogflow-web.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>100</maxHistory>
            <totalSizeCap>50GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.yiwise.batch.log.BatchFilter">
            <OnMatch>DENY</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
        <filter class="com.yiwise.batch.log.ScheduleFilter">
            <OnMatch>DENY</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("[AliMessageQueue]");</expression>
            </evaluator>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
    </appender>

    <appender name="asyncRollingFileAppender" class="com.yiwise.dialogflow.logback.MyAsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="rollingFileAppender"/>
    </appender>

    <appender name="rollingScheduleFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${baseDirectory}/dialogflow-web_schedule.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${baseDirectory}/dialogflow-web_schedule.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>100</maxHistory>
            <totalSizeCap>50GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.yiwise.middleware.mysql.log.MybatisFilter"/>
        <filter class="com.yiwise.batch.log.ScheduleFilter">
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>

    <appender name="asyncRollingScheduleFileAppender" class="com.yiwise.dialogflow.logback.MyAsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="rollingScheduleFileAppender"/>
    </appender>

    <appender name="rollingBatchFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${baseDirectory}/dialogflow-web_batch.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${baseDirectory}/dialogflow-web_batch.log.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>100</maxHistory>
            <totalSizeCap>50GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.yiwise.batch.log.BatchFilter">
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>

    <appender name="asyncRollingBatchFileAppender" class="com.yiwise.dialogflow.logback.MyAsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="rollingBatchFileAppender"/>
    </appender>

    <appender name="logHubAppender" class="com.yiwise.base.common.log.AliyunLoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>${logHub.endpoint}</endpoint>
        <accessKeyId>${logHub.accessKeyId}</accessKeyId>
        <accessKeySecret>${logHub.accessKeySecret}</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>${logHub.projectName}</project>
        <logStore>${logHub.logstore}</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <topic>aicc-platform-dialogflow-web - ${spring.profiles.active}</topic>
        <source>${HOSTNAME_IP}</source>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>104857600</totalSizeInBytes>
        <maxBlockMs>60000</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>10</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>100</maxRetryBackoffMs>

        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd HH:mm:ss</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>

        <mdcFields>MDC_LOG_ID</mdcFields>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("[LogHub]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("[AliMessageQueue]");</expression>
            </evaluator>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("[LogHub_Warn]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <appender name="asyncLogHubAppender" class="com.yiwise.dialogflow.logback.MyAsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="logHubAppender"/>
    </appender>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
        <filter class="com.yiwise.middleware.mysql.log.MybatisFilter"/>
        <filter class="com.yiwise.batch.log.ScheduleFilter">
            <OnMatch>DENY</OnMatch>
            <OnMismatch>NEUTRAL</OnMismatch>
        </filter>
    </appender>

    <logger name="com.yiwise" level="DEBUG"/>
    <logger name="org.quartz" level="INFO"/>
    <logger name="net.sourceforge.peers" level="DEBUG"/>
    <logger name="org.apache.http" level="INFO"/>
    <logger name="org.mybatis.spring" level="INFO"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="druid.sql.Statement" level="INFO"/>
    <logger name="druid.sql.ResultSet" level="INFO"/>
    <logger name="druid.sql.Connection" level="INFO"/>
    <logger name="com.yiwise.base.monitoring.MDCDecoratedServlet" level="WARN" addtivity="false"/>
    <logger name="org.springframework.boot.web.servlet" level="INFO"/>
    <logger name="org.springframework.scheduling.quartz" level="INFO"/>
    <logger name="com.yiwise.core.config.YiwiseRestTemplate" level="INFO" />
    <logger name="org.apache.http.impl.conn.PoolingHttpClientConnectionManager" level="INFO"/>
    <logger name="org.springframework.jdbc.datasource.DataSourceTransactionManager" level="DEBUG" />
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="INFO"/>
    <logger name="com.yiwise.common.thread.DialogFlowDownloadExecutor" level="WARN"/>
    <logger name="org.springframework.data.elasticsearch.client.WIRE" level="trace"/>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="asyncRollingFileAppender"/>
            <appender-ref ref="asyncLogHubAppender"/>
        </root>
    </springProfile>
    <springProfile name="daily, daily-huoshan">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="asyncRollingFileAppender"/>
            <appender-ref ref="asyncLogHubAppender"/>
            <appender-ref ref="asyncRollingScheduleFileAppender" />
            <appender-ref ref="asyncRollingBatchFileAppender" />
        </root>
    </springProfile>
    <springProfile name="pre, pre-finance">
        <root level="INFO">
            <appender-ref ref="asyncRollingFileAppender"/>
            <appender-ref ref="asyncLogHubAppender"/>
            <appender-ref ref="asyncRollingScheduleFileAppender" />
            <appender-ref ref="asyncRollingBatchFileAppender" />
        </root>
    </springProfile>
    <springProfile name="prod, prod-huoshan, prod-huawei, prod-finance">
        <root level="INFO">
            <appender-ref ref="asyncRollingFileAppender"/>
            <appender-ref ref="asyncLogHubAppender"/>
            <appender-ref ref="asyncRollingScheduleFileAppender" />
            <appender-ref ref="asyncRollingBatchFileAppender" />
        </root>
    </springProfile>
</configuration>
