package com.yiwise.dialogflow.schedule;

import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.enums.BotGenerateRecordStatusEnum;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateRecordPO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/5/12
 */
@Slf4j
@Component
public class BotGenerateTimeoutScheduler {

    @Resource
    private BotGenerateRecordService botGenerateRecordService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private MongoTemplate mongoTemplate;

    private static final String BOT_GENERATE_TIMEOUT_LOCK = RedisKeyCenter.getBotGenerateTimeoutLockKey();

    private boolean isTimeout(BotGenerateRecordPO record) {
        // todo 通过配置文件设置超时时间
        return record.getCreateTime().plusSeconds(ApplicationConstant.BOT_GENERATE_CREATE_TIMEOUT_SECOND).isBefore(LocalDateTime.now());
    }

    /**
     * 每5分钟运行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void checkForTimeout() {
        MDC.put("MDC_LOG_ID", MyRandomStringUtils.getRandomStringByLength(8));
        if (redisOpsService.setIfAbsent(BOT_GENERATE_TIMEOUT_LOCK, "", TimeUnit.MINUTES.toSeconds(1))) {
            log.info("【话术自动生成超时检测】开始执行");
            try {
                doCheckForTimeout();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                redisOpsService.delete(BOT_GENERATE_TIMEOUT_LOCK);
            }
            log.info("【话术自动生成超时检测】执行结束");
        }
    }

    private void doCheckForTimeout() {
        List<BotGenerateRecordPO> runningRecordList = botGenerateRecordService.queryRunningRecordList();
        if (CollectionUtils.isEmpty(runningRecordList)) {
            log.info("没有进行中的话术自动生成任务");
            return;
        }

        List<BotGenerateRecordPO> timeoutRecordList = new ArrayList<>();
        for (BotGenerateRecordPO record : runningRecordList) {
            if (isTimeout(record)) {
                log.warn("[LogHub_Warn]话术自动生成超时,title={},recordId={}", record.getTitle(), record.getId());
                timeoutRecordList.add(record);
            }
        }

        for (BotGenerateRecordPO record : timeoutRecordList) {
            // BOT自动生成记录修改为超时状态
            record.setStatus(BotGenerateRecordStatusEnum.TIMEOUT);
            record.setUpdateTime(LocalDateTime.now());
            record.setFailReason("生成超时");
            mongoTemplate.save(record, BotGenerateRecordPO.COLLECTION_NAME);

            botGenerateRecordService.updateTemplateAndSendMsgIfRecordIsLatest(record);
        }
    }
}
