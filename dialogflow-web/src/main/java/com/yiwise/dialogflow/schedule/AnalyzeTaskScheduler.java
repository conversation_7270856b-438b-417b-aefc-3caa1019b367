package com.yiwise.dialogflow.schedule;

import com.yiwise.dialogflow.service.analyze.AnalyzeTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AnalyzeTaskScheduler {

    @Resource
    private AnalyzeTaskService analyzeTaskService;

    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void schedule() {
        analyzeTaskService.asyncDispatch();
    }
}
