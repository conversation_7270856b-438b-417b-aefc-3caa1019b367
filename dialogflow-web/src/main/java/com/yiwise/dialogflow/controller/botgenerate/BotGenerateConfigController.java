package com.yiwise.dialogflow.controller.botgenerate;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateConfigPO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;


/**
 * bot生成/配置信息
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/botGenerate/config")
public class BotGenerateConfigController {

    @Resource
    private BotGenerateConfigService botGenerateConfigService;

    /**
     * 获取最新的schema配置信息
     * @return 配置信息
     */
    @NoLogin
    @GetMapping("last")
    public ResultObject<BotGenerateConfigPO> getNewestConfig() {
        return ResultObject.success(botGenerateConfigService.getLastSchemaConfig().orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "没有配置")));
    }

    /**
     * 获取最新的template配置信息
     * @return 配置信息
     */
    @NoLogin
    @GetMapping("lastTemplate")
    public ResultObject<BotGenerateConfigPO> lastTemplate() {
        return ResultObject.success(botGenerateConfigService.getLastTemplateConfig().orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "没有配置")));
    }

    /**
     * 算法侧上传schema配置文件
     * @param file schema文件
     * @return 上传后的文件key
     */
    @NoLogin
    @PostMapping("upload")
    public ResultObject<String> upload(@RequestParam(value = "file")
                                      @NotNull(message = "file不能为空") MultipartFile file) {
        return ResultObject.success(botGenerateConfigService.uploadSchema(file));
    }

    /**
     * 算法侧上传语法模板
     * @param file syntaxTemplate文件
     * @return 上传后的文件key
     */
    @NoLogin
    @PostMapping("uploadTemplate")
    public ResultObject<String> uploadTemplate(@RequestParam(value = "file")
                                              @NotNull(message = "file不能为空") MultipartFile file) {
        return ResultObject.success(botGenerateConfigService.uploadTemplate(file));
    }

}
