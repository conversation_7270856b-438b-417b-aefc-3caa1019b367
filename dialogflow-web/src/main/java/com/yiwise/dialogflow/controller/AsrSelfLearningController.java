package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.query.AsrSelfLearningDetailQuery;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrSelfLearningDetailVO;
import com.yiwise.dialogflow.service.asrmodel.AsrSelfLearningService;
import com.yiwise.dialogflow.service.file.FileService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19 14:51:21
 */
@RestController
@RequestMapping("/apiBot/v3/asrSelfLearning")
public class AsrSelfLearningController {
    @Resource
    private AsrSelfLearningService asrSelfLearningService;

    @Resource
    private FileService fileService;

    @PostMapping("list")
    public ResultObject list(@RequestBody AsrSelfLearningDetailQuery query) {
        return ResultObject.success(asrSelfLearningService.list(query));
    }

    @PostMapping("update")
    public ResultObject update(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        asrSelfLearningDetailVO.setCurrentUserId(userId);
        asrSelfLearningDetailVO.setTenantId(tenantId);
        asrSelfLearningService.update(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("stop")
    public ResultObject stop(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        asrSelfLearningDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrSelfLearningService.stop(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("start")
    public ResultObject start(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        asrSelfLearningDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrSelfLearningService.start(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("delete")
    public ResultObject delete(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        asrSelfLearningService.delete(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @GetMapping("getBotList")
    public ResultObject getBotList(@RequestParam("asrSelfLearningDetailId") Long asrSelfLearningDetailId, @RequestParam(value = "botInfo", required = false) String botInfo,
                                  @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum) {
        return ResultObject.success(asrSelfLearningService.getBotList(asrSelfLearningDetailId, botInfo, pageSize, pageNum));
    }

    @GetMapping("getById")
    public ResultObject getById(@RequestParam("asrSelfLearningDetailId") Long asrSelfLearningDetailId) {
        return ResultObject.success(asrSelfLearningService.getById(asrSelfLearningDetailId));
    }

    @PostMapping("unbindBot")
    public ResultObject unbindBot(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        asrSelfLearningService.unbindBot(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("bindBot")
    public ResultObject bindBot(@RequestBody AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        asrSelfLearningDetailVO.setCurrentUserId(userId);
        asrSelfLearningDetailVO.setTenantId(tenantId);
        asrSelfLearningService.bindBot(asrSelfLearningDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping(value = "/uploadCorpusTxt")
    public ResultObject<Map<String, String>> uploadCorpusTxt(@RequestParam MultipartFile file) {
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        Map<String, String> urls = fileService.upload(tenantId, userId, file);
        return ResultObject.success(urls, "上传成功");
    }

    /**
     * 删除厂商侧所有的自学习模型及其数据集
     *
     * @return
     */
    @PostMapping("deleteAllProviderData")
    public ResultObject deleteAllProviderData(@RequestParam("pageNumber") Integer pageNumber, @RequestParam("pageSize") Integer pageSize) {
        asrSelfLearningService.deleteAllProviderData(pageNumber, pageSize);
        return ResultObject.success("请求成功");
    }

    /**
     * 列举阿里侧数据
     *
     * @return
     */
    @GetMapping("listAliData")
    public ResultObject listAliData(@RequestParam("pageNumber") Integer pageNumber, @RequestParam("pageSize") Integer pageSize) {
        return ResultObject.success(asrSelfLearningService.listAliData(pageNumber, pageSize));
    }

    /**
     * 列举腾讯侧数据
     *
     * @return
     */
    @GetMapping("listTencentData")
    public ResultObject listTencentData(@RequestParam("pageSize") Integer pageSize) {
        return ResultObject.success(asrSelfLearningService.listTencentData(pageSize));
    }
}