package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.po.GroupPO;
import com.yiwise.dialogflow.entity.vo.group.*;
import com.yiwise.dialogflow.service.GroupService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 分组(知识分组、意图分组)
 *
 * <AUTHOR>
 * @date 2022/7/21
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/group")
public class GroupController {

    @Resource
    private GroupService groupService;

    /**
     * 创建分组
     */
    @PostMapping("/create")
    @TenantIsolation("#createVO.botId")
    public ResultObject<GroupPO> create(@RequestBody GroupCreateVO createVO) {
        createVO.setUserId(SecurityUtils.getUserId());
        return ResultObject.success(groupService.create(createVO));
    }

    /**
     * 修改分组
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody GroupUpdateVO updateVO) {
        updateVO.setUserId(SecurityUtils.getUserId());
        groupService.update(updateVO);
        return ResultObject.success(null);
    }

    /**
     * 分组查询
     *
     * @param botId botId
     * @param type  分组类型
     */
    @GetMapping("/list")
    public ResultObject<List<GroupVO>> list(GroupTypeEnum type, Long botId) {
        return ResultObject.success(groupService.list(type, botId));
    }

    /**
     * 删除分组
     */
    @PostMapping("/delete")
    public ResultObject<GroupDeleteResultVO> delete(@RequestBody GroupDeleteVO deleteVO) {
        return ResultObject.success(groupService.delete(deleteVO, SecurityUtils.getUserId()));
    }
}
