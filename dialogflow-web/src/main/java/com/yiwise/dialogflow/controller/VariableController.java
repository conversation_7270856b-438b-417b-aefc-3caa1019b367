package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.entity.query.VariableDelQuery;
import com.yiwise.dialogflow.entity.query.VariableQuery;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.VariableBindInfoVO;
import com.yiwise.dialogflow.entity.vo.VariableVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncResultVO;
import com.yiwise.dialogflow.service.VariableService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/variable")
public class VariableController {

    @Resource
    private VariableService variableService;
    
    @GetMapping("getListByBotId")
    public ResultObject<PageResultObject<VariableVO>> getListByBotId(VariableQuery query) {
        return ResultObject.success(variableService.getPageByBotId(query));
    }

    @PostMapping("recoverySystemVariable")
    public ResultObject<List<VariablePO>> recoverySystemVariable(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(variableService.recoverySystemVariable(botId));
    }

    @PostMapping("add")
    @TenantIsolation("#variable.botId")
    public ResultObject addVariable(@RequestBody VariablePO variable) {
        variable.setCreateUserId(SecurityUtils.getUserId());
        variable.setUpdateUserId(SecurityUtils.getUserId());
        variableService.create(variable);
        return ResultObject.success("请求成功");
    }

    @GetMapping("getById")
    public ResultObject<VariablePO> getVariable(Long botId, @NotNull(message = "变量id不能为空") String id) {
        return ResultObject.success(variableService.getById(botId, id));
    }

    @PostMapping("update")
    @TenantIsolation("#variablePO.botId")
    public ResultObject updateVariable(@RequestBody VariablePO variablePO) {
        variableService.update(variablePO, SecurityUtils.getUserId());
        return ResultObject.success("请求成功");
    }

    @PostMapping("delete")
    @TenantIsolation("#botId")
    public ResultObject deleteVariable(Long botId, @NotNull(message = "变量id不能为空") String id) {
        variableService.deleteById(botId, id, SecurityUtils.getUserId());
        return ResultObject.success("请求成功");
    }
    
    @PostMapping("batchDelete")
    @TenantIsolation("#delQuery.botId")
    public ResultObject batchDeleteVariable(@RequestBody VariableDelQuery delQuery) {
        variableService.deleteByIdList(delQuery.getBotId(), delQuery.getIdList(), delQuery.getSelectAll(), SecurityUtils.getUserId());
        return ResultObject.success("请求成功");
    }

    @NoLogin
    @GetMapping("getUsedVariableNameSetByDialogFlowId")
    public ResultObject<Set<String>> getUsedVariableNameSetByDialogFlowId(Long dialogFlowId) {
        return ResultObject.success(variableService.getUsedVariableNameSetByDialogFlowId(dialogFlowId, null));
    }

    /**
     * 查询 bot 下已使用的变量绑定客户属性信息列表
     * @param botId botId
     * @param tenantId 租户 id
     * @return 绑定列表
     */
    @GetMapping("getUsedVariableBindList")
    public ResultObject<List<VariableBindInfoVO>> getUsedVariableBindList(@NotNull Long botId, @NotNull Long tenantId) {
        return ResultObject.success(variableService.getUsedVariableBindInfoList(botId, tenantId));
    }

    /**
     * 根据botId查询所有动态变量id-name键值对
     * @param botId
     * @return
     */
    @GetMapping("getDynamicVariableIdNamePairList")
    public ResultObject<List<IdNamePair<String, String>>> getDynamicVariableIdNamePairList(@NotNull Long botId) {
        return ResultObject.success(variableService.getDynamicVariableIdNamePairList(botId));
    }

    /**
     * 根据老话术id查询动态变量名称集合
     *
     * @param dialogFlowId 老话术id
     * @return 动态变量名称集合
     */
    @NoLogin
    @GetMapping("/listAllDynamicVariableNameByDialogFlowId")
    public ResultObject<List<String>> listAllDynamicVariableNameByDialogFlowId(Long dialogFlowId) {
        return ResultObject.success(variableService.listAllDynamicVariableNameByDialogFlowId(dialogFlowId));
    }

    /**
     * 同步变量
     */
    @PostMapping("sync")
    public ResultObject<VariableSyncResultVO> sync(@RequestBody VariableSyncRequestVO syncRequest) {
        syncRequest.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(variableService.sync(syncRequest));
    }

}
