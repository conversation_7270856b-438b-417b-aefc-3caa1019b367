package com.yiwise.dialogflow.controller;


import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.vo.QueryCallOutUsedSmsTemplateIdRequestVO;
import com.yiwise.dialogflow.service.OutsideResourceService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("apiBot/v3/outside/resource/")
public class OutsideResourceController {

    @Resource
    private OutsideResourceService outsideResourceService;

    @InnerOnly
    @NoLogin
    @PostMapping("queryCallOutSmsTemplateIdSetByDialogFlowIdList")
    public ResultObject<Map<Long, Set<Long>>> queryCallOutSmsTemplateIdSetByDialogFlowIdList(@RequestBody QueryCallOutUsedSmsTemplateIdRequestVO request) {
        return ResultObject.success(outsideResourceService.queryCallOutSmsTemplateIdSetByDialogFlowIdList(request.getDialogFlowIdList()));
    }

}
