package com.yiwise.dialogflow.controller.test;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.service.remote.MaFlowService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;

@RestController
@RequestMapping("apiBot/v3/test/maFlow")
public class MaFlowController {

    @Resource
    private MaFlowService maFlowService;

    @NoLogin
    @GetMapping("list")
    public ResultObject list(Long id) {
        return ResultObject.success(maFlowService.getMaFlowNameList(Collections.singletonList(id)));
    }
}
