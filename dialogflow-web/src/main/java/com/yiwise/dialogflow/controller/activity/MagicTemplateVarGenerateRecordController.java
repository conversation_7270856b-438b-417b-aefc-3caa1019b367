package com.yiwise.dialogflow.controller.activity;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordDetailPO;
import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordPO;
import com.yiwise.dialogflow.entity.vo.magic.MagicTemplateVarGenerateRecordQueryVO;
import com.yiwise.dialogflow.entity.vo.magic.TemplateVarValueRegenerateRequestVO;
import com.yiwise.dialogflow.entity.vo.magic.TemplateVariableValueGenerateRequestVO;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateRecordDetailService;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateRecordService;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateService;
import com.yiwise.dialogflow.utils.SecurityUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 轻量化模板变量测试生成记录
 */
@Slf4j
@Validated
@RestController
@RequestMapping("apiBot/v3/magicVarGenerateRecord")
public class MagicTemplateVarGenerateRecordController {

    @Resource
    private MagicTemplateVarGenerateRecordDetailService magicTemplateVarGenerateRecordDetailService;

    @Resource
    private MagicTemplateVarGenerateRecordService magicTemplateVarGenerateRecordService;

    @Resource
    private MagicTemplateVarGenerateService magicTemplateVarGenerateService;

    /**
     * 根据详情ID查询
     */
    @GetMapping("getDetailById")
    public ResultObject<MagicTemplateVarGenerateRecordDetailPO> getDetailById(
            @NotNull(message = "botId不能为空") Long botId,
            @NotBlank(message = "recordId不能为空") String recordId,
            @NotBlank(message = "detailId不能为空") String detailId) {
        return ResultObject.success(magicTemplateVarGenerateRecordDetailService.findById(botId, recordId, detailId));
    }

    /**
     * 根据记录ID查询详情列表
     */
    @GetMapping("getDetailListByRecordId")
    public ResultObject<List<MagicTemplateVarGenerateRecordDetailPO>> getDetailListByRecordId(
            @NotNull(message = "botId不能为空") Long botId,
            @NotBlank(message = "recordId不能为空") String recordId) {
        return ResultObject.success(magicTemplateVarGenerateRecordDetailService.findByRecordId(botId, recordId));
    }

    /**
     * 根据条件查询最新一条记录的详情列表
     */
    @PostMapping("queryLastDetailList")
    public ResultObject<PageResultObject<MagicTemplateVarGenerateRecordDetailPO>> queryLastDetailList(
            @RequestBody MagicTemplateVarGenerateRecordQueryVO query) {
        return ResultObject.success(magicTemplateVarGenerateRecordDetailService.queryLastListByCondition(query));
    }

    /**
     * 根据记录ID查询记录
     */
    @GetMapping("getRecordById")
    public ResultObject<MagicTemplateVarGenerateRecordPO> getRecordById(
            @NotNull(message = "botId不能为空") Long botId,
            @NotBlank(message = "recordId不能为空") String recordId) {
        return ResultObject.success(magicTemplateVarGenerateRecordService.findById(botId, recordId));
    }

    /**
     * 根据记录ID重新生成
     */
    @PostMapping("regenerateByRecordId")
    public ResultObject<Void> regenerateByRecordId(
            @NotNull(message = "botId不能为空") Long botId,
            @NotBlank(message = "recordId不能为空") String recordId) {
        magicTemplateVarGenerateService.regenerateByRecordId(botId, recordId);
        return ResultObject.success(null);
    }

    /**
     * 根据详情ID重新生成
     */
    @PostMapping("regenerate")
    public ResultObject<Void> regenerate(@RequestBody @Validated TemplateVarValueRegenerateRequestVO request) {
        request.setUserId(getUserId());
        magicTemplateVarGenerateService.regenerate(request);
        return ResultObject.success(null);
    }

    /**
     * 生成模板变量值
     */
    @PostMapping("generate")
    public ResultObject<Void> generate(@RequestBody @Validated TemplateVariableValueGenerateRequestVO request) {
        // 调用生成服务
        request.setUserId(getUserId());
        magicTemplateVarGenerateService.generate(request);
        return ResultObject.success(null);
    }

    /**
     * 获取当前用户ID
     */
    private Long getUserId() {
        return SecurityUtils.getUserId();
    }
}
