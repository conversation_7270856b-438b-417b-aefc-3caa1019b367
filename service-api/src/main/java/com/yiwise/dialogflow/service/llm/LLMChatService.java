package com.yiwise.dialogflow.service.llm;

import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatRequest;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatResponse;
import com.yiwise.dialogflow.reactor.Accumulator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;


public interface LLMChatService {

    Flux<LLMChatResponse> llmStepChat(LLMChatRequest request);

    <T> Flux<T> llmStepChat(LLMChatRequest request, Accumulator<String, T> accumulator);

    Flux<LLMChatResponse> chat(LLMChatRequest request);

    void asyncCache(LLMChatRequest request);

    Mono<Boolean> validateKnowledgeAnswer(Long botId, String userInput, String answerText, List<SimpleChatHistory> history);
}