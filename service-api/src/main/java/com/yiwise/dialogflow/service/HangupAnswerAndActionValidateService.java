package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.po.StepPO;

import java.util.List;

/**
 * 验证答案是挂机话术, 且是否配置的挂机动作
 */
public interface HangupAnswerAndActionValidateService {

    List<SnapshotInvalidFailItemMsg> validateNode(StepPO step, List<DialogBaseNodePO> nodeList);


    List<SnapshotInvalidFailItemMsg> validateKnowledge(List<KnowledgePO> knowledgeList);

    List<SnapshotInvalidFailItemMsg> validateSpecialAnswer(List<SpecialAnswerConfigPO> specialAnswerList);


}
