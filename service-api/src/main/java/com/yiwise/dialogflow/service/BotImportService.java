package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.vo.BotAutoImportVO;
import com.yiwise.dialogflow.entity.vo.BotZipImportRequestVO;
import com.yiwise.dialogflow.entity.vo.CommonFileUploadResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 机器人导入服务
 */
public interface BotImportService {

    void importFromJsonFile(MultipartFile file);

    void importFromJson(BotAutoImportVO botAutoImportVO);

    /**
     * 上传zip文件
     * @param file zip文件
     * @return zip文件路径
     */
    CommonFileUploadResult uploadZipFile(MultipartFile file);

    /**
     * 从zip文件导入
     * @return 导入后的botId
     */
    Long importFromZipFile(BotZipImportRequestVO request);
}
