package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.TtsJobPO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;


/**
 * <AUTHOR>
 */
public interface TtsJobExecuteService {
    void submit(TtsJobPO job);

//    void redoAllAnswer(Long botId);

//    boolean cancel(Long botId);

    TtsComposeResultVO composeTextByBotConfig(Long botId, String text);

//    TtsComposeProgressVO getComposeProgress(Long botId);

    boolean jobIsRunning(TtsJobPO job);

    /**
     * 查询bot是否正在合成
     * @param botId botId
     * @return 是否正在合成
     */
    boolean botIsComposing(Long botId);

}
