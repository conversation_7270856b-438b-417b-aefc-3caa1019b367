package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.DomainIntentPO;
import com.yiwise.dialogflow.entity.query.DomainIntentQuery;
import com.yiwise.dialogflow.entity.vo.DomainIntentVO;
import com.yiwise.dialogflow.entity.vo.SyncResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/18
 */
public interface DomainIntentService {

    PageResultObject<DomainIntentVO> list(DomainIntentQuery intentQuery);

    List<DomainIntentPO> listAll();

    SyncResultVO sync(DomainIntentQuery intentQuery);

    void importFromFile(String domainName, MultipartFile multipartFile, Boolean isCover);
}
