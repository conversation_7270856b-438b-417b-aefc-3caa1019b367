package com.yiwise.dialogflow.service.train;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import com.yiwise.dialogflow.entity.vo.BatchTrainRequestVO;
import com.yiwise.dialogflow.entity.vo.BatchTrainResultVO;
import com.yiwise.dialogflow.entity.vo.TrainStatusVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
public interface TrainService {

    void train(Long botId, Long userId, String desc, SystemEnum systemType);

    void message(TrainResultPO trainResultPO);

    void create(TrainResultPO trainResultPO);

    void callback(TrainStatusVO trainResultPO);

    List<TrainStatusVO> history(Long tenantId, AlgorithmTrainTypeEnum trainType);

    TrainResultPO lastSuccess(ModelTrainKey modelTrainKey);

    TrainResultPO earliestUntrained(ModelTrainKey modelTrainKey);

    TrainStatusVO enableTrain(ModelTrainKey modelTrainKey);

    void updateTrainingStatus(ModelTrainKey modelTrainKey);

    void deleteTrainingStatus(ModelTrainKey modelTrainKey);

    Boolean queryIsTraining(ModelTrainKey modelTrainKey);

    BatchTrainResultVO batchTrain(BatchTrainRequestVO request, Long tenantId);

    BatchTrainResultVO preBatchTrain(BatchTrainRequestVO request, Long tenantId);
}
