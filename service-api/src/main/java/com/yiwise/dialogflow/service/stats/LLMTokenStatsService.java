package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.LLMTokenStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;

import java.util.List;

public interface LLMTokenStatsService {

    void saveStats(BotStatsAnalysisResult analysisResult);

    List<LLMTokenStatsPO> queryAllStepStatsList(Long botId, BaseStatsQuery condition);
}