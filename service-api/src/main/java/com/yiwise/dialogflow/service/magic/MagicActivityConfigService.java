package com.yiwise.dialogflow.service.magic;

import com.yiwise.dialogflow.api.dto.request.MagicActivityConfigCreateRequest;
import com.yiwise.dialogflow.api.dto.response.activity.MagicActivityConfigDTO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.entity.po.magic.MagicActivityConfigPO;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MagicActivityConfigService {

    /**
     * 根据活动 id 列表获取
     */
    List<MagicActivityConfigDTO> getByActivityIdList(List<Long> activityIdList);

    /**
     * 根据任务 id 和 botid 获取配置, 可能会获取不到
     */
    Optional<MagicActivityConfigPO> getByBotIdAndCallJobId(Long botId, Long callJobId);

    /**
     * 根据任务 id 列表获取配置
     */
    List<MagicActivityConfigPO> getByCallJobIdList(List<Long> callJobIdList);

    String create(MagicActivityConfigCreateRequest request);

    Boolean preCreate(MagicActivityConfigCreateRequest request);

    Mono<MagicActivityConfigPO> asyncGetByBotIdAndCallJobId(Long botId, Long callJobId);

    Mono<Map<String, String>> asyncGetVarValueFromCache(Long botId, Long callJobId, Map<String, VariablePO> variableMap);

    Optional<MagicActivityConfigPO> getBotIdAndConfigId(Long botId, String configId);

    Optional<MagicActivityConfigPO> getBotIdAndActivityId(Long botId, Long activityId);

    void modify(MagicActivityConfigPO config, Long userId);
}
