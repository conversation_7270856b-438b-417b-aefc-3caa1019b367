package com.yiwise.dialogflow.service.analyze;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplateIntentPO;
import com.yiwise.dialogflow.entity.vo.analyze.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AnalyzeTemplateIntentService {

    /**
     * 判断意图是否存在
     *
     * @param templateId 模板id
     * @param name 意图名称
     * @return T/F
     */
    boolean exists(String templateId, String name);

    /**
     * 根据模板id删除意图
     *
     * @param templateId 模板id
     */
    void deleteByTemplateId(String templateId);

    /**
     * 根据id查询意图
     *
     * @param id id
     * @return 意图实体
     */
    AnalyzeTemplateIntentPO getById(String id);

    /**
     * 根据id列表查询意图列表
     *
     * @param ids id列表
     * @return 意图列表
     */
    List<AnalyzeTemplateIntentPO> listByIds(List<String> ids);

    /**
     * 添加意图
     *
     * @param request form
     */
    void add(AnalyzeTemplateIntentAddVO request);

    /**
     * 创建意图
     *
     * @param templateId 模板id
     * @param name       名称
     * @return 意图实体
     */
    String createTaskIntent(String templateId, String name);

    /**
     * 修改意图
     *
     * @param request form
     */
    void update(AnalyzeTemplateIntentUpdateVO request);

    /**
     * 分页查询意图
     *
     * @param request form
     * @return 模板意图列表
     */
    PageResultObject<AnalyzeTemplateIntentVO> list(AnalyzeTemplateIntentQueryVO request);

    /**
     * 删除意图
     *
     * @param request form
     */
    void delete(AnalyzeTemplateIntentQueryVO request);

    /**
     * 黑白切换
     *
     * @param request form
     */
    void toggleCategory(AnalyzeTemplateIntentToggleVO request);

    /**
     * 查询全部意图
     *
     * @param templateId 模板id
     * @return 意图列表
     */
    List<AnalyzeTemplateIntentPO> listByTemplateId(String templateId);

    /**
     * 意图合并
     *
     * @param request form
     * @param userId 操作人id
     */
    void merge(AnalyzeTemplateIntentMergeVO request, Long userId);

    /**
     * 批量保存意图列表
     *
     * @param list 意图列表
     */
    void batchSave(List<AnalyzeTemplateIntentPO> list);

    /**
     * 拆分意图
     *
     * @param request form
     */
    void revert(AnalyzeTemplateIntentRevertVO request);

    /**
     * 增加意图中的语料数量
     *
     * @param intentId 意图id
     * @param corpusSize 语料数量
     */
    void incrementCorpusSize(String intentId, Long corpusSize);

    /**
     * 查询所有子意图的id列表
     *
     * @param intentId 父意图id
     * @return 意图id列表
     */
    List<String> findAllDescendantIntentIdList(String intentId);
}
