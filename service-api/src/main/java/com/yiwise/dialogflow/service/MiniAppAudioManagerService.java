package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.vo.BotAudioProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.AudioTotalInfoVO;
import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.TreeifyAnswerAudioVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MiniAppAudioManagerService {

    List<AudioTotalInfoVO> getTreeifyTotalInfoByBotId(Long botId, Long recordUserId);

    List<TreeifyAnswerAudioVO> searchAnswerAudioInfo(Long botId, Long recordUserId, String search);

    PageResultObject<BotAudioProgressVO> queryBotAudioProgress(Long recordUserId, String search, Integer pageNum, Integer pageSize);

    void batchAdjustVolume(Long botId, Long recordUserId, Integer volume, List<String> audioOssKeyList, Long userId);

    AudioUploadResultVO uploadAudio(Long botId, String answerText, MultipartFile file, Long userId);

    AudioTotalInfoVO getTreeifyInfoBySourceType(Long botId, Long recordUserId, AnswerSourceEnum originType, String originId);
}
