package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;

@Service
public class DialogFlowFastHangupWordsService {

    @Resource(name = "remoteServiceWebClient")
    private WebClient remoteServiceWebClient;

    public Set<String> queryAll() {
        return asyncQueryAll().block();
    }

    public Mono<HashSet<String>> asyncQueryAll() {
        String url = "http://ai-call-service/invoke/dialogFlowFastHangupWordsServiceImpl/queryAll";
        return remoteServiceWebClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(Collections.emptySet()))
                .retrieve()
                .bodyToMono(String.class)
                .map(body -> {
                    HashSet<String> result = new HashSet<>();
                    ResultObject<String> ResultObject = JsonUtils.string2Object(body, new TypeReference<ResultObject<String>>(){});
                    if (ResultObject.isSuccess()) {
                        String data = ResultObject.getData();
                        if (StringUtils.isBlank(data)) {
                            return result;
                        }
                        HashSet<String> dataInfo = JsonUtils.string2Object(data, new TypeReference<HashSet<String>>(){});
                        if (CollectionUtils.isNotEmpty(dataInfo)) {
                            result.addAll(dataInfo);
                        }
                    }
                    return result;
                }).switchIfEmpty(Mono.just(new HashSet<>()))
                .onErrorResume(throwable -> Mono.just(new HashSet<>()));
    }

}
