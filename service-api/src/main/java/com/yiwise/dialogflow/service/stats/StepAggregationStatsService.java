package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.api.dto.request.StepAggregationStatsRequest;
import com.yiwise.dialogflow.api.dto.response.stats.DataReportStepDataPO;

import java.util.List;

/**
 * 流程聚合分析, 按行业场景对其下面的所有话术的流程数据进行聚合分析
 */
public interface StepAggregationStatsService {


    List<DataReportStepDataPO> generateDialogFlowDataReportData(StepAggregationStatsRequest condition);


}
