package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum RuleActionTypeEnum implements CodeDescEnum {
    INTENT_LEVEL(0, "意向等级"),
    INTENT_ACTION(1,"意向动作"),

    ;

    private final Integer code;
    private final String desc;

    RuleActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
