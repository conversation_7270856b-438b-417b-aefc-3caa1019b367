package com.yiwise.dialogflow.entity.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PredictTestRequestVO {
    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    private Long botId;
    /**
     * 节点标签
     */
    private String nodeLabel;
    /**
     * 测试文本
     */
    @NotNull(message = "测试文本不能为空")
    private String text;
    /**
     * 意图问法置信度上限
     */
    private Double upperThreshold;
    /**
     * 意图问法置信度下限
     */
    private Double lowerThreshold;
}
