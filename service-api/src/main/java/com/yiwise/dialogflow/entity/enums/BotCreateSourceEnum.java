package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum BotCreateSourceEnum implements CodeDescEnum {
    MANUAL(0, "手动创建"),
    COPY(1, "复制"),
    IMPORT(2, "导入"),
    GENERATE(3, "自动生成"),
    GENERATE_REWRITE(4, "自动生成改写模型"),
    OPEN_API(5, "开放平台接口"),
    MAGIC_AICC(6, "轻量化 AICC"),
    ;

    BotCreateSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    final Integer code;
    final String desc;
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
