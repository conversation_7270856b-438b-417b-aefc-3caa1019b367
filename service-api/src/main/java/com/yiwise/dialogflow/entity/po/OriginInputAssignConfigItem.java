package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.OriginInputCollectTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OriginInputAssignConfigItem implements Serializable {

    /**
     * 原话采集方式
     */
    private OriginInputCollectTypeEnum originInputCollectType;

    /**
     * 过滤意图列表
     */
    private List<String> filteredIntentIdList;

    /**
     * 过滤关键词列表
     */
    private List<String> filteredRegexList;

    /**
     * 变量 id
     */
    private String variableId;

    public String getEntityId() {
        return SystemEntityCategoryEnum.convertToId(SystemEntityCategoryEnum.ORIGIN_INPUT);
    }
}
