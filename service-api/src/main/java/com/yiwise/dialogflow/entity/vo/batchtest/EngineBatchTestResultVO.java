package com.yiwise.dialogflow.entity.vo.batchtest;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.bo.batchtest.CompareResult;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EngineBatchTestResultVO {
    String description;
    Long botId;
    Long version;
    String sessionId;
    RobotSnapshotUsageTargetEnum usageTarget;
    Boolean startFail;
    String startFailMsg;
    List<CompareResult> compareList;
    Boolean success;
}
