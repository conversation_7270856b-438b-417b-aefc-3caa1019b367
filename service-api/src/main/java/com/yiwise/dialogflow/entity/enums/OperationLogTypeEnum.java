package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 日志类型枚举
 *
 * <AUTHOR>
 * @date 2022/9/13
 */
public enum OperationLogTypeEnum implements CodeDescEnum {
    STEP(1, "对话流"),
    KNOWLEDGE(2, "知识库"),
    INTENT(3, "意图库"),
    VARIABLE(4, "变量库"),
    AUDIO(5, "音频库"),
    BOT_CONFIG(6, "Bot设置"),
    RULE_CONFIG(7, "规则配置"),
    TRAIN_TEST(8, "训练测试"),
    SEARCH_REPLACE(9, "查找替换"),
    BOT_COPY(10, "Bot复制"),
    PUBLISH_AUDIT(11, "发布审核"),
    GROUP(12, "分组"),
    AUTO_CREATE(13, "Bot自动生成"),
    BOT_MIGRATION(14, "Bot迁移"),
    BOT_SYNC(15, "Bot同步"),
    ENTITY(16, "实体库"),
    AI_GENERATE(17, "AI生成"),
    AI_REWRITE(18, "AI改写"),
    SMS_GENERATE(19, "短信生成"),
    BOT_BIND(20, "Bot绑定"),
    BOT_EXPORT(21, "话术导出"),
    PAGE_STAY(22, "页面停留"),
    LLM(23, "大模型"),
    MAGIC_FORM_TEMPLATE(24, "轻量化表单模板"),
    ;

    private final int code;
    private final String desc;

    OperationLogTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
