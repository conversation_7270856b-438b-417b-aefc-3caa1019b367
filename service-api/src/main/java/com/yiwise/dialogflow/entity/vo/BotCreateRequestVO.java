package com.yiwise.dialogflow.entity.vo;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.BotCreateSourceEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.enums.EasyCallVersionEnum;
import lombok.Data;

/**
 * 创建bot的请求参数, 不在使用BotVO, 因为BotVO中有很多字段是不需要的
 */
@Data
public class BotCreateRequestVO {

    /**
     * bot名称
     */
    private String name;

    /*
     * 来源BotId
     * 这个字段有值表示是复制话术
     */
    Long srcBotId;

    /**
     * 文件夹ID
     */
    Long folderId;

    /**
     * 场景id
     */
    private Integer customerSceneId;


    /**
     * 赛道ID
     */
    private Integer customerTrackType;

    /**
     * 二级赛道ID
     */
    private Integer customerTrackSubType;

    /**
     * BOT描述
     */
    private String description;

    /**
     * 意向标签组ID
     */
    private Long intentLevelTagId;

    private Long tenantId;

    private Long userId;

    private SystemEnum systemType;

    /**
     * 是否自动化生成
     */
    Boolean isAutoCreate = false;

    BotCreateSourceEnum createSource;

    /**
     * BOT类型,0-常规BOT 1-轻量化BOT模板 2-轻量化BOT
     */
    private V3BotTypeEnum type;

    /**
     * 轻量化模板易呼版本
     */
    private EasyCallVersionEnum easyCallVersion;
}
