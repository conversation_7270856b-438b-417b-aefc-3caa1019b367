package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum RewriteStatusEnum implements CodeDescEnum {
    PROCESSING(0, "处理中"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    QUEUING(3, "排队中"),
    CANCEL(4, "取消")
    ;

    final Integer code;
    final String desc;
    RewriteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
