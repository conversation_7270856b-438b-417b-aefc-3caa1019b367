package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.TrainButtonStatusEnum;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2020/7/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TrainStatusVO extends TrainResultPO {

    private static final long serialVersionUID = -8763438129541795626L;

    /**
     * 兼容算法接口
     */
    String robotId;

    @Deprecated
    Boolean isTraining;

    @Deprecated
    Boolean trainButtonEnabled;

    ModelTrainKey modelTrainKey;

    /**
     * 训练按钮状态
     */
    TrainButtonStatusEnum trainButtonStatus;

    String createUserName;

    /**
     * 算法返回的失败原因
     */
    String operate;
}
