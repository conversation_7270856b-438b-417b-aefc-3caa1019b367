package com.yiwise.dialogflow.entity.po;

import com.yiwise.middleware.objectstorage.serializer.AddOssPrefixSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BackgroundAudioConfigPO {
    String source;

    String filename;

    String url;

    String originUrl;

    Boolean enable;

    Integer volume;

    Boolean isBuiltIn;

    public Boolean getIsBuiltIn() {
        if (Objects.isNull(isBuiltIn)) {
            isBuiltIn = "办公室谈话声".equals(source);
        }
        return isBuiltIn;
    }

    public String getFullUrl() {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        return AddOssPrefixSerializer.getAddOssPrefixUrl(url);
    }

    public String getOriginUrl() {
        if (StringUtils.isBlank(originUrl)) {
            originUrl = url;
        }
        return originUrl;
    }
}
