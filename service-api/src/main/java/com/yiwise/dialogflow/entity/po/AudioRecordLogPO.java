package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@EqualsAndHashCode(callSuper=false)
@Document(collection = AudioRecordLogPO.COLLECTION_NAME)
public class AudioRecordLogPO  extends BaseTimePO {

    public static final String COLLECTION_NAME = "audioRecordLog";

    @Id
    String id;

    Long botId;

    Long recordUserId;

    String textHash;

    String text;

    String url;
}
