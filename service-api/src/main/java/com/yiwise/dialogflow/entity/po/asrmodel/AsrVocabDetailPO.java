package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.middleware.mysql.handler.StringListToJsonTypeHandler;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * asr热词组信息表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_vocab_detail")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrVocabDetailPO extends BaseTimeUserIdPO implements Serializable {

    @Id
    @GeneratedValue(generator = "JDBC")
    Long asrVocabId;

    /**
     * 热词组名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * 热词组内容
     */
    @ColumnType(typeHandler = StringListToJsonTypeHandler.class)
    List<String> content;

    /**
     * 开启状态（0-停用；1-开启）
     */
    Integer status;

}
