package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.SyncScopeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BasicSyncVO implements Serializable {

    /**
     * 源BotId
     */
    Long srcBotId;

    /**
     * 需要同步的目标流程/知识/意图ID列表
     */
    List<String> targetIdList;

    /**
     * 目标BotId列表
     */
    List<Long> targetBotIdList;

    /**
     * 同步内容
     */
    Set<SyncScopeEnum> syncScopeEnumSet;

    /**
     * 操作者
     */
    Long currentUserId;

    /**
     * 系统环境
     */
    SystemEnum systemType;
}
