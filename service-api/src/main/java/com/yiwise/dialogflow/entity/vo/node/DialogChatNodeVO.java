package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.entity.enums.ExceedThresholdReplayStrategyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DialogChatNodeVO extends DialogBaseNodeVO {
    private List<String> selectIntentIdList;

    private Map<String, String> intentRelatedNodeMap;

    private Boolean enableCustomReplay;

    private Integer customReplayThreshold;

    /**
     * 是否开启用户无应答时长
     */
    private Boolean enableCustomUserSilence;

    /**
     * 针对当前这个答案设置的用户无应答时长
     */
    private Double customUserSilenceSecond;

    /**
     * 是否启用节点意图优先
     */
    private boolean enableNodeIntentFirst;

    /**
     * 是否开启等待用户说完(ai应答时长)
     */
    private Boolean enableWaitUserSayFinish;

    /**
     * 等待时长, 单位毫秒
     */
    private Integer waitUserSayFinishMs;

    /**
     * 触发意图id列表, 仅触发此意图时, 才会执行等待用户说完逻辑
     * 此字段应该是当前节点关联意图子集
     */
    private List<String> triggerWaitIntentIdList;

    /**
     * 在回到原主流程时, 如果超过重播阈值, 重播策略
     */
    private ExceedThresholdReplayStrategyEnum exceedThresholdReplayStrategy = ExceedThresholdReplayStrategyEnum.DEFAULT_BRANCH;

    private Boolean enablePullback;

}
