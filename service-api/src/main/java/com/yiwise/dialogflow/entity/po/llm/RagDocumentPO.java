package com.yiwise.dialogflow.entity.po.llm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.RagDocumentStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = RagDocumentPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_docName", def = "{botId: 1, docName: 1}")
)
public class RagDocumentPO extends BaseTimeUserIdPO implements Serializable {

    public static final String COLLECTION_NAME = "ragDocument";

    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 文档类型
     */
    private DocumentTypeEnum documentType;

    /**
     * 文档名称
     */
    private String docName;

    /**
     * 网站 url 地址
     */
    private String url;

    /**
     * 文件上传后的 oss key
     */
    private String fileOssKey;

    /**
     * 文档状态
     */
    private RagDocumentStatusEnum status;

    /**
     * 状态(启用, 禁用, 删除)
     */
    private EnabledStatusEnum enabledStatus;

    /**
     * 文档拆分配置
     */
    private DocumentSplitConfigPO splitConfig;

    /**
     * 文档解析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastParseTime;
}