package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LlmStepTypeEnum implements CodeDescEnum {

    /**
     * 采集任务型
     */
    COLLECT_TASK(1, "任务配置型"),

    /**
     * 自由配置型
     */
    FREE_CONFIG(2, "自由配置型"),
    ;

    private final Integer code;
    private final String desc;

    public static Boolean isCollectTask(LlmStepTypeEnum type) {
        return COLLECT_TASK.equals(type);
    }

    public static Boolean isFreeConfig(LlmStepTypeEnum type) {
        return FREE_CONFIG.equals(type);
    }
}