package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 分组类型
 *
 * <AUTHOR>
 * @date 2022/7/21
 */
public enum GroupTypeEnum implements CodeDescEnum {

    /**
     * 知识
     */
    KNOWLEDGE(1, "知识"),

    /**
     * 单个意图
     */
    SINGLE_INTENT(2, "单个意图"),

    /**
     * 公共音频库
     */
    PUBLIC_AUDIO(3, "公共音频库"),
    ;

    private final int code;
    private final String desc;

    GroupTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
