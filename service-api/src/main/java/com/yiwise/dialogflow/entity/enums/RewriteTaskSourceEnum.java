package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum RewriteTaskSourceEnum implements CodeDescEnum {
    AI_GENERATE(0, "AI生成"),
    AI_REWRITE(1, "AI改写"),
    ;
    final Integer code;
    final String desc;

    RewriteTaskSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
