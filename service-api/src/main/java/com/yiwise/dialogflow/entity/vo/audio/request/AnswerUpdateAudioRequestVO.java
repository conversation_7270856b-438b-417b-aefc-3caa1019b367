package com.yiwise.dialogflow.entity.vo.audio.request;

import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AnswerUpdateAudioRequestVO {
    @NotNull(message = "机器人id不能为空")
    Long botId;
    AnswerLocateBO locate;
    @NotBlank(message = "答案文本不能为空")
    String answerText;
    @NotBlank(message = "答案音频地址不能为空")
    String url;

    String fullUrl;
    Integer volume;
    Integer duration;
}
