package com.yiwise.dialogflow.entity.bo.algorithm;

import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.SimpleIntentInfo;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/1/26
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PredictResult implements Serializable {

    private static final long serialVersionUID = -1483753955589662920L;

    /**
     * 关键词or问法
     */
    PredictTypeEnum predictType;

    /**
     * 正则字符串
     */
    Pattern pattern;

    /**
     * 关键词，如果是问法则同意图名称
     */
    String keyword;

    /**
     * 相似句/匹配问法
     */
    String matchText;

    /**
     * 置信度
     */
    Double confidence;

    /**
     * 匹配到的目标意图
     */
    SimpleIntentInfo simpleIntentInfo;

    /**
     * 意图关联类型
     */
    IntentRefTypeEnum intentRefType;

    /**
     * 命中的意图ID
     */
    String intentId;

    /**
     * 命中的意图名称
     */
    String intentName;

    /**
     * 组合意图条件组依赖的意图结果, 用来定位组合意图条件成立的原因
     */
    List<PredictResult> compositeIntentConditionResults;

    /**
     * 意图优先级
     */
    IntentPriorityLevelEnum intentPriorityLevel;

    public static PredictResult of(IntentBO intentBO) {
        PredictResult predictResult = new PredictResult();
        predictResult.setIntentId(intentBO.getId());
        predictResult.setIntentName(intentBO.getName());
        predictResult.setConfidence(Double.valueOf(intentBO.getConfidence()));
        predictResult.setMatchText(intentBO.getSim_sentence());
        if (BooleanUtils.isTrue(intentBO.getIsNlu())) {
            predictResult.setIntentPriorityLevel(IntentPriorityLevelEnum.NLU_INTENT);
        }
        return predictResult;
    }

    public String toPrintLogString() {
        return "{\"predictType\":\"" + predictType +
                "\",\"keyword\":\"" + keyword +
                "\",\"matchText\":\"" + matchText +
                "\",\"confidence\":" + confidence +
                ",\"intentId\":\"" + intentId +
                "\",\"intentName\":\"" + intentName +
                "\",\"intentPriorityLevel\":\"" + intentPriorityLevel +
                "\"}";
    }
}
