package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
public enum ConditionVarTypeEnum implements CodeDescEnum {

    /**
     * 自定义变量
     */
    CUSTOM(1,"自定义变量"),

    /**
     * AI识别
     */
    AI(2,"AI识别"),

    /**
     * 常量
     */
    CONSTANT(3,"常量"),

    /**
     * 动态变量
     */
    DYNAMIC(4, "动态变量"),

    /**
     * 意图
     */
    INTENT(5, "意图"),
    ;

    private final int code;
    private final String desc;

    ConditionVarTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    public static boolean isVar(ConditionVarTypeEnum type) {
        return CUSTOM.equals(type) || DYNAMIC.equals(type);
    }

    public static boolean isIntent(ConditionVarTypeEnum type) {
        return INTENT.equals(type);
    }
}
