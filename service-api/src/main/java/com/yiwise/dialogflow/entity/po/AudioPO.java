package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = AudioPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_recordUserId", def = "{recordUserId: 1}")
)
public class AudioPO implements Serializable {

    public static final String COLLECTION_NAME = "audio";

    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 录音师id
     */
    private Long recordUserId;

    /**
     * 话术文案
     */
    private String text;

    /**
     * 音频地址
     */
    private String url;

    /**
     * 采样率
     */
    private Integer samplesPerSec;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}