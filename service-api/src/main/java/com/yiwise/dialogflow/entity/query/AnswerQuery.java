package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.po.Labelled;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class AnswerQuery extends AbstractQueryVO {

    public static final int SEARCH_TYPE_ANSWER = 0;

    public static final int SEARCH_TYPE_CUSTOM_VARIABLE = 1;

    public static final int SEARCH_TYPE_DYNAMIC_VARIABLE = 2;

    Long botId;

    Set<AnswerSourceEnum> answerSourceSet;

    /**
     * 搜索流程类型, 如果为空默认搜索所有流行
     */
    List<StepTypeEnum> stepTypeList;

    /**
     * 查询类型, 0: 答案, 1: 自定义变量, 2: 动态变量
     */
    Integer searchType;

    String search;

    Boolean mergeWholeVariableSentence;

    EnabledStatusEnum specialAnswerConfigEnabledStatus;

    /**
     * 流程、特殊语境、知识集合
     */
    List<? extends Labelled> labelledList;
}
