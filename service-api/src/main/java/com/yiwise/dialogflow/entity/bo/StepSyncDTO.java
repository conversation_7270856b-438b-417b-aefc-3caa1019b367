package com.yiwise.dialogflow.entity.bo;

import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StepSyncDTO implements Serializable {

    /**
     * 目标BotId
     */
    Long targetBotId;

    /**
     * 意向标签组ID
     */
    Long intentLevelTagId;

    /**
     * 同步参数
     */
    StepSyncVO syncVO;

    /**
     * 目标流程
     */
    StepPO stepPO;

    /**
     * 目标节点列表
     */
    List<DialogBaseNodePO> nodeList;

    /**
     * 被引用的变量列表
     */
    List<VariablePO> variablePOList;

    /**
     * 触发意图列表
     */
    List<? extends IntentPO> triggerIntentPOList;

    /**
     * 触发意图语料列表
     */
    List<IntentCorpusPO> triggerIntentCorpusPOList;

    /**
     * 节点意图列表
     */
    List<? extends IntentPO> nodeIntentPOList;

    /**
     * 节点意图语料列表
     */
    List<IntentCorpusPO> nodeIntentCorpusPOList;

    /**
     * 流程名称-ID映射
     */
    Map<String, String> stepNameIdMapping;

    /**
     * 意图名称-ID映射
     */
    Map<String, String> intentNameIdMapping;

    /**
     * 知识名称-ID映射
     */
    Map<String, String> knowledgeNameIdMapping;

    /**
     * 特殊语境名称-ID映射
     */
    Map<String, String> specialAnswerConfigNameIdMapping;

    /**
     * 实体列表
     */
    List<BaseEntityPO> entityList;

    /**
     * 算法标签列表
     */
    List<AlgorithmLabelPO> algorithmLabelList;

    /**
     * 大模型流程配置
     */
    LlmStepConfigPO llmStepConfig;

    /**
     * 源BOT
     */
    BotPO srcBot;

    public List<DialogBaseNodePO> getNodeList() {
        return DeepCopyUtils.copyList(nodeList);
    }

    public StepPO getStepPO() {
        return DeepCopyUtils.copyObject(stepPO);
    }

    public List<VariablePO> getVariablePOList() {
        return DeepCopyUtils.copyList(variablePOList);
    }

    public List<? extends IntentPO> getTriggerIntentPOList() {
        return DeepCopyUtils.copyList(triggerIntentPOList);
    }

    public List<IntentCorpusPO> getTriggerIntentCorpusPOList() {
        return DeepCopyUtils.copyList(triggerIntentCorpusPOList);
    }

    public List<? extends IntentPO> getNodeIntentPOList() {
        return DeepCopyUtils.copyList(nodeIntentPOList);
    }

    public List<IntentCorpusPO> getNodeIntentCorpusPOList() {
        return DeepCopyUtils.copyList(nodeIntentCorpusPOList);
    }
}
