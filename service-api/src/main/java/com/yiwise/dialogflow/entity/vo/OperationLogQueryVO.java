package com.yiwise.dialogflow.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationLogQueryVO extends AbstractQueryVO {

    /**
     * botId
     */
    private Long botId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime endTime;

    /**
     * 日志详情模糊查询
     */
    private String detailKeyword;

    /**
     * 操作对象列表
     */
    private List<OperationLogResourceTypeEnum> resourceTypeList;

    /**
     * 操作人id列表
     */
    private List<Long> operatorIdList;
}
