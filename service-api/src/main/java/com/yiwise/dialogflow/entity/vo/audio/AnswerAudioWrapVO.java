package com.yiwise.dialogflow.entity.vo.audio;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnswerAudioWrapVO extends BaseAnswerContentVO {
    Set<String> completedSentenceSet;
    Set<String> manMadeCompletedSentenceSet;
    Set<String> ttsCompletedSentenceSet;
    List<AnswerAudioMappingVO> audioSourceList;
    Boolean completed;
    List<AnswerPlaceholderElementVO> answerElementList;
    Integer volume;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public LocalDateTime getRecordingTime() {
        List<AnswerPlaceholderElementVO> elementList = getAnswerElementList();
        if (CollectionUtils.isEmpty(elementList)) {
            return null;
        }
        return elementList.stream().map(AnswerPlaceholderElementVO::getRecordingTime)
                .filter(Objects::nonNull).max(LocalDateTime::compareTo).orElse(null);
    }
}
