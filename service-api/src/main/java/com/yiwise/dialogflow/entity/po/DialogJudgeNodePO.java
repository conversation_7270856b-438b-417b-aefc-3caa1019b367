package com.yiwise.dialogflow.entity.po;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class DialogJudgeNodePO extends DialogBaseNodePO implements NonLeafNode{

    /**
     * 分支列表
     */
    List<NodeConditionBranchPO> branchList;

    /**
     * 这里需要和前端讨论下这个结构, 向下一个节点的引用是维护在分支里还是在外面维护
     */
    private Map<String, String> branchRelatedNodeMap;


    @Override
    @JsonIgnore
    public Map<String, String> getRelatedNodeMap() {
        if (branchRelatedNodeMap == null) {
            branchRelatedNodeMap = new HashMap<>();
        }
        return branchRelatedNodeMap;
    }

    @Override
    public void setRelatedNodeMap(Map<String, String> relatedNodeMap) {
        this.branchRelatedNodeMap = relatedNodeMap;
    }

    @Override
    @JsonIgnore
    public List<String> getNextNodeIdList() {
        if (MapUtils.isEmpty(branchRelatedNodeMap)) {
            return new ArrayList<>();
        }
        return branchRelatedNodeMap.values().stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    public Set<String> calBranchDependIntentIdSet() {
        if (CollectionUtil.isEmpty(branchList)) {
            return Collections.emptySet();
        }
        Set<String> intentIdSet = new HashSet<>();
        for (NodeConditionBranchPO branch : branchList) {
            intentIdSet.addAll(branch.acquireBranchDependIntentIdSet());
        }
        return intentIdSet;
    }

    @Override
    public Set<String> calDependVariableIdSet(DependentResourceBO resource) {
        Set<String> result = super.calDependVariableIdSet(resource);
        if (CollectionUtils.isNotEmpty(getBranchList())) {
            for (NodeConditionBranchPO branch : getBranchList()) {
                result.addAll(branch.acquireSwithConditionDependsVariableIdSet());
            }
        }
        return result;
    }
}
