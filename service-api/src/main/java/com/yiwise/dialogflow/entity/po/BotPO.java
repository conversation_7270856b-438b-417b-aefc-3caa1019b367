package com.yiwise.dialogflow.entity.po;


import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.enums.handler.EasyCallVersionEnumHandler;
import com.yiwise.middleware.mysql.handler.EnabledStatusEnumHandler;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "bot")
public class BotPO extends BaseTimeUserIdPO implements AsrConfigPO {

    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long botId;

    /**
     * BOT名称
     */
    private String name;

    /**
     * BOT描述
     */
    private String description;

    /**
     * 意向标签组ID
     */
    private Long intentLevelTagId;

    /**
     * 行业
     */
    @Deprecated
    private IndustryTypeEnum industry;

    /**
     * 子行业
     */
    @Deprecated
    private IndustrySubTypeEnum subIndustry;

    /**
     * 赛道ID
     */
    private Integer customerTrackType;

    /**
     * 二级赛道ID
     */
    private Integer customerTrackSubType;

    /**
     * TTS语音
     */
    @Column
    @Deprecated
    private TtsVoiceEnum ttsVoice;

    /**
     * 语音类型
     */
    @Column
    @Deprecated
    private VoiceTypeEnum voiceType;

    /**
     * 审核状态
     */
    @Column
    private AuditStatusEnum auditStatus;

    /**
     * 删除状态
     */
    @ColumnType(typeHandler = EnabledStatusEnumHandler.class)
    private EnabledStatusEnum enableStatus;

    /**
     * 失败原因
     */
    @Column
    private String failReason;

    /**
     * 体验二维码id
     */
    private String qrCodeId;

    /**
     * 体验二维码名称
     */
    private String qrCodeName;

    /**
     * 海报类型
     */
    @Column
    private PosterTypeEnum posterType;

    /**
     * 内置意图领域名称
     */
    private String domainName;

    /**
     * ASR语言类型ID
     */
    private Long asrLanguageId;

    /**
     * asr基础模型id
     */
    private Long asrProviderId;

    /**
     * asr自学习模型id
     */
    private Long asrSelfLearningDetailId;

    /**
     * 热词组id
     */
    private Long asrVocabId;

    /**
     * 纠错模型id
     */
    private String asrErrorCorrectionDetailId;

    /**
     * 反应灵敏度
     */
    private Integer maxSentenceSilence;

    /**
     * 场景id
     */
    private Integer customerSceneId;

    /**
     * 创建人所在的租户id, ope创建时, 该字段为0, 否则为userId所在的租户id
     */
    private Long createTenantId;

    /**
     * 创建来源
     */
    @Column
    private BotCreateSourceEnum createSource;

    /**
     * bot生成状态, 如果来源是自动生成, 则通过这个状态判断是否生成完成
     * 在bot改写模型下, 如果未生成完成的, 则不允许查看/编辑等操作
     */
    @Column
    private BotGenerateStatusEnum generateStatus;

    /**
     * 是否已经发布审核过, 一旦发布审核通过, 则一直为true, 外呼查询话术列表时, 需要根据该字段过滤
     */
    private Boolean published;

    /**
     * BOT类型,0-常规BOT 1-轻量化BOT模板 2-轻量化BOT
     */
    @Column
    private V3BotTypeEnum type;

    /**
     * 易呼版本, 针对轻量化场景
     */
    @ColumnType(typeHandler = EasyCallVersionEnumHandler.class)
    private EasyCallVersionEnum easyCallVersion;

    /**
     * 轻量版bot, 模板id
     */
    private Long magicTemplateId;


    /**
     * 可见状态
     */
    @ColumnType(typeHandler = EnabledStatusEnumHandler.class)
    private EnabledStatusEnum visibleStatus;

    /**
     * 开启 asr 优化
     */
    private Boolean enableAsrOptimization;

    /**
     * 录音师id
     */
    private Long recordUserId;
}
