package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 25/07/2018
 */
public enum DialStatusEnum implements CodeDescEnum {
    ANSWERED(0, "已接听", null, 2),
    NO_ANSWER(1, "无应答", "呼叫号码未接听", 1),
    BUSY(2, "忙线中", "呼叫号码占线", 1),
    POWER_OFF(3, "关机", "呼叫号码关机", 1),
    OUT_OF_SERVICE(4, "停机", "呼叫号码停机", 1),
    REFUSED(5, "拒接", "呼叫号码拒接", 1),
    VACANT_NUMBER(6, "空号", "呼叫的号码是空号", 1),
    /**
     * 无法接通，或没拨通，或没能获取到EarlyMedia
     */
    CAN_NOT_CONNECT(7, "无法接通", "呼叫的号码无法接通", 1),
    /**
     * 主叫号码不可用，主叫欠费，停机
     */
    FROM_PHONE_ERROR(8, "主叫停机", "主叫号码不可用", 0),
    /**
     * 外呼失败，系统错误
     */
    SYSTEM_ERROR(9, "系统错误", "外呼失败，系统错误", 0),
    CALL_LOSS(10, "多并发呼损", "等待服务中用户挂断", 1),
    TRANSFER_ARTIFICIAL(11, "转人工呼损", "等待转人工服务中用户挂断", 2),
    IVR_GIVE_UP(12, "IVR中放弃", "IVR中放弃", 2),
    QUEUE_GIVE_UP(13, "排队放弃", "排队放弃", 2),
    QUEUE_OVER_TIME(14, "排队超时", "排队超时", 2),
    NO_CS(15, "无坐席在线", "无坐席在线", 2),
    RING_GIVE_UP(16, "振铃放弃", "振铃放弃", 2),
    CALLER_HANG_UP(17, "主叫挂断", "主叫挂断", 2),
    EXTENSION_NUMBER(18, "分机号错误", "分机号错误", 1),
    CALL_LIMIT(19, "呼叫受限", "呼叫受限", 0),
    LINE_INTERCEPT(20, "线路拦截", "线路拦截", 0),
    ;

    private Integer code;
    private String desc;
    private String webSocketMsg;
    /**
     * 0:线路问题 1:被叫问题 2:其他
     */
    private Integer caused;

    DialStatusEnum(Integer code, String desc, String webSocketMsg, Integer caused) {
        this.code = code;
        this.desc = desc;
        this.webSocketMsg = webSocketMsg;
        this.caused = caused;
    }

    public static boolean isAnswered(DialStatusEnum status) {
        return ANSWERED.equals(status) || TRANSFER_ARTIFICIAL.equals(status);
    }

    public static boolean isCallLoss(DialStatusEnum status) {
        return CALL_LOSS.equals(status) || TRANSFER_ARTIFICIAL.equals(status);
    }

    /**
     * 线路问题
     *
     * @param status
     * @return
     */
    public static boolean isLineCaused(DialStatusEnum status) {
        return 0 == status.caused;
    }

    /**
     * 被叫问题
     *
     * @param status
     * @return
     */
    public static boolean isCalledCaused(DialStatusEnum status) {
        return 1 == status.caused;
    }

    public String getWebSocketAlterMsg() {
        return webSocketMsg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}