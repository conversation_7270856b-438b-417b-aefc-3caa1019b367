<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yiwise.dialogflow</groupId>
        <artifactId>aicc-platform-dialogflow-web</artifactId>
        <version>daily-2.1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>service-api</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-engine-share</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.common</groupId>
            <artifactId>base-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.common</groupId>
            <artifactId>base-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>account-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>cloud-feign-helper</artifactId>
            <version>2.0.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>call-activity-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>callout-job-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>ma-center-api</artifactId>
            <version>2.0.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yiwise.common</groupId>
                    <artifactId>base-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>customer-data-platform-rpc-api</artifactId>
            <version>2.0.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yiwise.common</groupId>
                    <artifactId>base-service</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yiwise.common</groupId>
                    <artifactId>base-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>customer-data-platform-common</artifactId>
            <version>2.0.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yiwise.common</groupId>
                    <artifactId>base-service</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yiwise.common</groupId>
                    <artifactId>base-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.9.0</version>
        </dependency>

        <!-- middleware start-->
        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-ObjectStorage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-MongoDB</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-Redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-Elasticsearch</artifactId>
            <version>5.0.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise.batch</groupId>
            <artifactId>aicc-platform-batch-job-api</artifactId>
            <version>2.0.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise.cloud</groupId>
            <artifactId>aicc-platform-openfeign-api</artifactId>
        </dependency>


        <!-- middleware end-->

        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-utils</artifactId>
            <version>3.4.2</version>
        </dependency>

        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-circuitbreaker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-asr</artifactId>
            <version>3.1.620</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>3.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.28</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise.agent</groupId>
            <artifactId>yiwise-agent-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>aicc-common</artifactId>
        </dependency>
    </dependencies>
</project>