package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.SnapshotApi;
import com.yiwise.dialogflow.api.dto.request.IdListDTO;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
public class SnapshotApiController implements SnapshotApi {


    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Override
    public Map<Long, Object> getLastPublishedSnapshotByBotIdList(IdListDTO idList) {
        return robotSnapshotService.getLastPublishedSnapshotByBotIdList(idList.getIdList());
    }
}
