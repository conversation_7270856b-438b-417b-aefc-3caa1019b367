package com.yiwise.dialogflow.helper;

import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.enums.SmsTemplateSourceEnum;
import com.yiwise.dialogflow.entity.po.IntentActionSource;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class IntentActionConfigCompareHelper {

    private static String generateActionInfo(IntentActionSource source, ActionNameResourceBO actionNameResource) {
        if (BooleanUtils.isNotTrue(source.getIsEnableAction())) {
            return "未启用";
        }
        List<RuleActionParam> actionList = source.getActionList();
        if (CollectionUtils.isEmpty(actionList)) {
            return "已启用";
        }
        StringJoiner sj = new StringJoiner("，");
        for (RuleActionParam actionParam : actionList) {
            switch (actionParam.getActionType()) {
                case WHITE_LIST: sj.add(generateActionInfo(actionParam, actionNameResource.getGroupId2NameMap())); break;
                case SEND_SMS: sj.add(generateActionInfo(actionParam, actionNameResource.getSmsTempId2NameMap())); break;
                case ADD_TAG: sj.add(generateActionInfo(actionParam, actionNameResource.getTagId2NameMap())); break;
                case ADD_WECHAT: sj.add(generateActionInfo(actionParam, Collections.emptyMap())); break;
                default: break;
            }
        }
        return sj.toString();
    }

    public static String generateActionInfo(RuleActionParam actionParam, Map<Long, String> resourceIdNameMap) {
        String resourceNames = "";
        ActionCategoryEnum actionType = actionParam.getActionType();
        List<IdNamePair<Long, String>> sourceIdList = actionParam.getSourceIdList();
        if (CollectionUtils.isNotEmpty(sourceIdList)) {
            resourceNames = sourceIdList.stream()
                    .map(IdNamePair::getId)
                    .map(resourceIdNameMap::get)
                    .filter(Objects::nonNull)
                    .map(item -> String.format("【%s】", item))
                    .collect(Collectors.joining("、"));
        }

        // 判断短信模板来源
        if (ActionCategoryEnum.SEND_SMS.equals(actionType)) {
            if (SmsTemplateSourceEnum.BOT.equals(actionParam.getSmsTemplateSource())) {
                resourceNames = "自定义短信模板:" + resourceNames;
            } else if (SmsTemplateSourceEnum.CALL_JOB.equals(actionParam.getSmsTemplateSource())) {
                resourceNames = "随外呼任务配置";
            }
        }

        String immeExec = "";
        if (ActionCategoryEnum.SEND_SMS.equals(actionType) && BooleanUtils.isTrue(actionParam.getImmediateExecute())) {
            immeExec = "立即";
        }
        return immeExec + actionType.getDesc() + (ActionCategoryEnum.ADD_WECHAT.equals(actionType) || StringUtils.isBlank(resourceNames) ? "" : ":" + resourceNames);
    }

    public static List<String> compareIntentActionConfigChangeLog(IntentActionSource oldConfig,
                                                          IntentActionSource newConfig,
                                                          ActionNameResourceBO actionNameResource,
                                                          String prefix) {
        String oldVal = generateActionInfo(oldConfig, actionNameResource);
        String newVal = generateActionInfo(newConfig, actionNameResource);
        List<String> logDetailList = new ArrayList<>();
        if (!StringUtils.equals(oldVal, newVal)) {
            logDetailList.add(String.format("%s触发动作设置【%s】修改为【%s】", prefix, oldVal, newVal));
        }
        return logDetailList;
    }

}
