package com.yiwise.dialogflow.thread;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/6/28
 */
public class SemanticAnalysisDetailExportThreadExecutorHelper {

    private static final ScheduledExecutorService monitorThread = Executors.newSingleThreadScheduledExecutor(new BasicThreadFactory.Builder().namingPattern("SemanticAnalysisDetailExport").daemon(true).build());

    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 100, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1024), new BasicThreadFactory.Builder().namingPattern("spring_batch_%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final DynamicDataSourceApplicationExecutor APPLICATION_EXECUTOR = new DynamicDataSourceApplicationExecutor(monitorThread, threadPool);

    public static void startThreadPoolStatusMonitor() {
        APPLICATION_EXECUTOR.startThreadPoolStatusMonitor();
    }

    static {
        startThreadPoolStatusMonitor();
    }

    public static Executor getExecutor() {
        return threadPool;
    }

    public static void execute(String taskTitle, Runnable runnable) {
        APPLICATION_EXECUTOR.execute(taskTitle, runnable);
    }

    public static <T> Future<T> submit(String taskTitle, Callable<T> callable) {
        return APPLICATION_EXECUTOR.submit(taskTitle, callable);
    }

}
