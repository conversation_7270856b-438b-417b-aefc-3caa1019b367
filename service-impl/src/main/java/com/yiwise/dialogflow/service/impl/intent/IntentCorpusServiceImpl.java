package com.yiwise.dialogflow.service.impl.intent;

import com.google.common.collect.Lists;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.ResourceCopyReferenceMappingBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentSearchScopeEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.DomainIntentPO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.DomainIntentQuery;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;
import com.yiwise.dialogflow.repository.IntentCorpusRepository;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DomainIntentService;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.dialogflow.utils.ParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.util.CloseableIterator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Slf4j
@Service
public class IntentCorpusServiceImpl implements IntentCorpusService, RobotResourceService {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private IntentCorpusRepository intentCorpusRepository;

    @Resource
    private BotService botService;

    @Resource
    private DomainIntentService domainIntentService;

    @Lazy
    @Resource
    private IntentService intentService;

    @Override
    public void save(IntentCorpusPO intentCorpusPO) {
        intentCorpusRepository.save(intentCorpusPO);
    }

    @Override
    public void saveAll(Collection<IntentCorpusPO> intentCorpusPOList) {
        if (CollectionUtils.isEmpty(intentCorpusPOList)) {
            return;
        }
        intentCorpusRepository.saveAll(intentCorpusPOList);
    }

    @Override
    public void delete(String intentCorpusId) {
        intentCorpusRepository.deleteById(intentCorpusId);
    }

    @Override
    public void deleteByIntentIdList(Collection<String> intentIdList) {
        intentCorpusRepository.deleteByIntentIdIn(intentIdList);
    }

    @Override
    public IntentCorpusPO findByIntentId(String intentId) {
        if (StringUtils.isEmpty(intentId)) {
            return null;
        }
        return intentCorpusRepository.findByIntentId(intentId);
    }

    @Override
    public List<IntentCorpusPO> findByIntentIdIn(Collection<String> intentIdList) {
        if (intentIdList == null) {
            return Lists.newArrayList();
        }
        intentIdList.removeIf(StringUtils::isEmpty);
        if (CollectionUtils.isEmpty(intentIdList)) {
            return Lists.newArrayList();
        }
        return intentCorpusRepository.findByIntentIdIn(intentIdList);
    }

    @Override
    public List<IntentCorpusPO> findByBotId(Long botId) {
        return intentCorpusRepository.findByBotId(botId);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<IntentCorpusPO> intentCorpusPOList = findByBotId(context.getSrcBotId());
        context.getSnapshot().setIntentCorpusList(intentCorpusPOList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        RobotSnapshotPO snapshot = context.getSnapshot();
        Map<String, String> intentIdNameMap = MyCollectionUtils.listToConvertMap(snapshot.getIntentList(), IntentPO::getId, IntentPO::getName);
        checkInvalidRegex(snapshot, intentIdNameMap, context);
        checkEmptyRegexOrCorpus(snapshot, intentIdNameMap, context);
    }

    /**
     * 检查空的语料或正则, 仅提示
     */
    private void checkEmptyRegexOrCorpus(RobotSnapshotPO snapshot, Map<String, String> intentIdNameMap, RobotResourceContext context) {
        Set<String> singleIntentIdSet = snapshot.getIntentList().stream()
                .filter(intent -> IntentTypeEnum.SINGLE.equals(intent.getIntentType()) && CorpusTypeEnum.CUSTOMIZED.equals(intent.getCorpusType()))
                .map(IntentPO::getId)
                .collect(Collectors.toSet());
        List<String> emptyCorpusIntentNameList = snapshot.getIntentCorpusList().stream()
                .filter(corpus -> singleIntentIdSet.contains(corpus.getIntentId()))
                .filter(corpus -> CollectionUtils.isEmpty(corpus.getCorpusList()) && CollectionUtils.isEmpty(corpus.getRegexList()) && CollectionUtils.isEmpty(corpus.getDescList()))
                .map(corpus -> intentIdNameMap.getOrDefault(corpus.getIntentId(), ""))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyCorpusIntentNameList)) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.INTENT)
                    .isWarning(true)
                    .failMsg(String.format("下列意图的问法或关键字均为空: %s", String.join(", ", emptyCorpusIntentNameList)))
                    .build();
            context.getInvalidMsgList().add(msg);
        }
    }

    /**
     * 检查无效的正则
     */
    private void checkInvalidRegex(RobotSnapshotPO snapshot, Map<String, String> intentIdNameMap, RobotResourceContext context) {
        snapshot.getIntentCorpusList().forEach(corpus -> {
            List<String> invalidRegexList = ParseUtil.getInvalidRegexExp(corpus.getRegexList());
            if (CollectionUtils.isNotEmpty(invalidRegexList)) {
                String intentName = intentIdNameMap.getOrDefault(corpus.getIntentId(), "未知意图");
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.INTENT)
                        .resourceId(corpus.getIntentId())
                        .resourceName(intentName)
                        .failMsg(String.format("意图:%s, 下列正则表达式错误: %s", intentName, String.join(", ", invalidRegexList)))
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        });
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long targetBotId = context.getTargetBotId();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> intentIdMapping = mapping.getIntentIdMapping();
        List<IntentCorpusPO> intentCorpusList = context.getSnapshot().getIntentCorpusList();
        if (CollectionUtils.isNotEmpty(intentCorpusList)) {
            intentCorpusList.forEach(intentCorpusPO -> {
                intentCorpusPO.setId(null);
                intentCorpusPO.setBotId(targetBotId);
                intentCorpusPO.setIntentId(intentIdMapping.get(intentCorpusPO.getIntentId()));
            });
            saveAll(intentCorpusList);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(IntentServiceImpl.class);
    }

    /**
     * 用于内部获取大量数据时的查询
     */
    @Override
    public List<IntentCorpusPO> scroll(IntentCorpusQuery intentCorpusQuery) {
        List<IntentCorpusPO> result = Lists.newArrayList();

        NativeSearchQueryBuilder queryBuilder = getIntentCorpusQuery(intentCorpusQuery);
        // 取消排序
        queryBuilder.withSort(SortBuilders.fieldSort("_doc"));
        // scroll的size
        queryBuilder.withPageable(PageRequest.of(0, 3000));
        NativeSearchQuery searchQuery = queryBuilder.build();

        try (CloseableIterator<IntentCorpusPO> closeableIterator = elasticsearchRestTemplate.stream(searchQuery, IntentCorpusPO.class)) {
            while (closeableIterator.hasNext()) {
                result.add(closeableIterator.next());
            }
        } catch (Exception e) {
            log.error("IntentCorpus查询Elasticsearch错误", e);
        }

        return result;
    }

    @Override
    public void fixIntentBuildInCorpus(Long botId) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }

        String domainName = bot.getDomainName();
        if (StringUtils.isBlank(domainName)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "领域不存在");
        }

        List<DomainIntentPO> domainIntentList = domainIntentService.listAll()
                .stream()
                .filter(domainIntentPO -> domainName.equals(domainIntentPO.getDomainName()))
                .collect(Collectors.toList());

        List<IntentCorpusPO> corpusList = findByBotId(botId);

        Map<String, DomainIntentPO> domainIntentMap = MyCollectionUtils.listToMap(domainIntentList, DomainIntentPO::getName);

        List<IntentCorpusPO> needSaveList = Lists.newArrayList();
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(intentList, IntentPO::getId);
        for (IntentCorpusPO corpus : corpusList) {
            IntentPO intent = intentMap.get(corpus.getIntentId());
            if (Objects.nonNull(intent) && CorpusTypeEnum.BUILD_IN.equals(intent.getCorpusType())) {
                DomainIntentPO domainIntent = domainIntentMap.get(intent.getName());
                if (Objects.nonNull(domainIntent)) {
                    corpus.setBuildInCorpusList(domainIntent.getBuiltInCorpusList());
                    needSaveList.add(corpus);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(needSaveList)) {
            saveAll(needSaveList);
            log.info("修复机器人{}的内置问法成功, 语料:[{}]", botId, JsonUtils.object2String(needSaveList));
        }
    }

    public NativeSearchQueryBuilder getIntentCorpusQuery(IntentCorpusQuery intentCorpusQuery) {
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (CollectionUtils.isNotEmpty(intentCorpusQuery.getIntentIdList())) {
            BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
            for (String intentId : intentCorpusQuery.getIntentIdList()) {
                shouldQueryBuilder.should(QueryBuilders.termQuery("intentId", intentId));
            }
            boolQueryBuilder.must(shouldQueryBuilder);
        } else {
            if (Objects.nonNull(intentCorpusQuery.getBotId())) {
                boolQueryBuilder.must(QueryBuilders.termQuery("botId", intentCorpusQuery.getBotId()));
            }

            if (StringUtils.isNotEmpty(intentCorpusQuery.getKeyword()) &&
                    CollectionUtils.isNotEmpty(intentCorpusQuery.getIntentSearchScopeList())) {
                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                if (intentCorpusQuery.getIntentSearchScopeList().contains(IntentSearchScopeEnum.INTENT_NAME)) {
                    //意图名搜索
                    shouldQueryBuilder.should(QueryBuilders.regexpQuery("name.keyword", ParseUtil.esRegex(intentCorpusQuery.getKeyword())));
                    shouldQueryBuilder.should(QueryBuilders.termQuery("name.keyword", intentCorpusQuery.getKeyword()));
                }
                if (intentCorpusQuery.getIntentSearchScopeList().contains(IntentSearchScopeEnum.KEYWORD)) {
                    //关键字搜索
                    shouldQueryBuilder.should(QueryBuilders.regexpQuery("regexList.keyword", ParseUtil.esRegex(intentCorpusQuery.getKeyword())));
                    shouldQueryBuilder.should(QueryBuilders.termQuery("regexList.keyword", intentCorpusQuery.getKeyword()));
                }
                if (intentCorpusQuery.getIntentSearchScopeList().contains(IntentSearchScopeEnum.CORPUS)) {
                    //问法语料搜索
                    shouldQueryBuilder.should(QueryBuilders.regexpQuery("corpusList.keyword", ParseUtil.esRegex(intentCorpusQuery.getKeyword())));
                    shouldQueryBuilder.should(QueryBuilders.termQuery("corpusList.keyword", intentCorpusQuery.getKeyword()));
                }
                if (intentCorpusQuery.getIntentSearchScopeList().contains(IntentSearchScopeEnum.DESC)) {
                    //意图描述搜索
                    shouldQueryBuilder.should(QueryBuilders.regexpQuery("descList.keyword", ParseUtil.esRegex(intentCorpusQuery.getKeyword())));
                    shouldQueryBuilder.should(QueryBuilders.termQuery("descList.keyword", intentCorpusQuery.getKeyword()));
                }
                boolQueryBuilder.must(shouldQueryBuilder);
            }
        }

        nativeSearchQueryBuilder.withFilter(boolQueryBuilder);

        return nativeSearchQueryBuilder;
    }

    @Override
    public List<IntentCorpusPO> findByBotIdAndKeywordList(Long botId, List<String> keywordList) {
        if (Objects.isNull(botId) || CollectionUtils.isEmpty(keywordList)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("botId", botId));
        boolQuery.filter(QueryBuilders.termsQuery("regexList.keyword", keywordList));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withFilter(boolQuery).withSort(SortBuilders.fieldSort("_id")).build();
        return elasticsearchRestTemplate.queryForList(nativeSearchQuery, IntentCorpusPO.class);
    }

    @Override
    public List<IntentCorpusPO> findByBotIdAndDescList(Long botId, List<String> descList) {
        if (Objects.isNull(botId) || CollectionUtils.isEmpty(descList)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("botId", botId));
        boolQuery.filter(QueryBuilders.termsQuery("descList.keyword", descList));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withFilter(boolQuery).withSort(SortBuilders.fieldSort("_id")).build();
        return elasticsearchRestTemplate.queryForList(nativeSearchQuery, IntentCorpusPO.class);
    }


    @Override
    public void updateIntentOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        List<IntentCorpusPO> corpusList = findByBotId(botId);
        if (CollectionUtils.isEmpty(corpusList)) {
            return;
        }
        List<IntentCorpusPO> updateList = new ArrayList<>();
        for (IntentCorpusPO corpus : corpusList) {
            AtomicBoolean update = new AtomicBoolean();
            if (CollectionUtils.isNotEmpty(corpus.getDescList())) {
                List<String> newDescList = corpus.getDescList().stream().map(desc -> {
                    String newDesc = AnswerTextUtils.convertTemplateOnVariableRename(desc, oldVariableName, newVariableName);
                    if (!StringUtils.equals(newDesc, desc)) {
                        update.set(true);
                    }
                    return newDesc;
                }).collect(Collectors.toList());
                corpus.setDescList(newDescList);
            }
            if (update.get()) {
                updateList.add(corpus);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            saveAll(updateList);
        }
    }

}
