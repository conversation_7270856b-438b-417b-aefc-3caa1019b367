package com.yiwise.dialogflow.service.impl.magic;

import javax.annotation.Resource;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordPO;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateRecordService;

import java.time.LocalDateTime;

@Service
public class MagicTemplateVarGenerateRecordServiceImpl implements MagicTemplateVarGenerateRecordService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void create(MagicTemplateVarGenerateRecordPO record) {
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setId(new ObjectId().toHexString());
        mongoTemplate.save(record, MagicTemplateVarGenerateRecordPO.COLLECTION_NAME);
    }

    @Override
    public MagicTemplateVarGenerateRecordPO findById(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("_id").is(id));
        return mongoTemplate.findOne(query, MagicTemplateVarGenerateRecordPO.class, MagicTemplateVarGenerateRecordPO.COLLECTION_NAME);
    }

    @Override
    public MagicTemplateVarGenerateRecordPO getLastRecordByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.limit(1);
        return mongoTemplate.findOne(query, MagicTemplateVarGenerateRecordPO.class, MagicTemplateVarGenerateRecordPO.COLLECTION_NAME);
    }
} 