package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioDetail;
import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioElement;
import com.yiwise.dialogflow.api.dto.response.step.NodeLinkInfo;
import com.yiwise.dialogflow.api.dto.response.step.SimpleNode;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.query.StepNodeQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.node.*;
import com.yiwise.dialogflow.entity.vo.stats.NodeIntentBranchStatsVO;
import com.yiwise.dialogflow.entity.vo.sync.NodeSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.NodeSyncVO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.impl.entitycollect.EntityServiceImpl;
import com.yiwise.dialogflow.service.impl.intent.IntentServiceImpl;
import com.yiwise.dialogflow.service.intent.*;
import com.yiwise.dialogflow.service.operationlog.StepNodeOperationLogService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.service.remote.ResourceService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.utils.*;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StepNodeServiceImpl implements StepNodeService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private StepService stepService;

    @Resource
    private IntentService intentService;

    @Lazy
    @Resource
    private IntentRuleService intentRuleService;

    @Resource
    private LabelGenerateService labelGenerateService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Lazy
    @Resource
    private BotService botService;

    @Autowired
    private KnowledgeService knowledgeService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private ResourceService resourceService;

    @Resource
    private StepNodeOperationLogService stepNodeOperationLogService;

    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @Resource
    private VariableService variableService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private HangupAnswerAndActionValidateService hangupAnswerAndActionValidateService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private TtsJobService ttsJobService;

    @Resource
    private AlgorithmLabelService algorithmLabelService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private EntityService entityService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Override
    public StepNodeSaveResultVO validAndSave(Long tenantId,
                                             Long botId,
                                             String stepId,
                                             List<DialogBaseNodePO> nodeList,
                                             SnapshotValidateConfigBO validateConfig, Long userId) {

        ActionNameResourceBO actionNameResource = new ActionNameResourceBO();
        if (Objects.nonNull(tenantId) && tenantId > 0 && BooleanUtils.isTrue(validateConfig.getRequireValidOutsideResource())) {
            actionNameResource = resourceService.getSourceId2NameMap(tenantId);
        }
        return doSave(botId, stepId, nodeList, true, validateConfig, actionNameResource, userId);
    }

    @Override
    public StepNodeSaveResultVO autoSave(Long botId, String stepId, List<DialogBaseNodePO> nodeList, Long userId) {
        if (CollectionUtils.isEmpty(nodeList)) {
            List<DialogBaseNodePO> oldNodeList = deleteByStepId(botId, stepId);
            compareAndCreateOperateLog(botId, stepId, oldNodeList, Collections.emptyList(), userId);
            StepNodeSaveResultVO result = new StepNodeSaveResultVO();
            result.setBotId(botId);
            result.setSuccess(true);
            result.setNodeList(Collections.emptyList());
            result.setFailList(Collections.emptyList());
            return result;
        }
        return doSave(botId, stepId, nodeList, false, new SnapshotValidateConfigBO(), new ActionNameResourceBO(), userId);
    }

    private StepNodeSaveResultVO doSave(Long botId,
                                        String stepId,
                                        List<DialogBaseNodePO> nodeList,
                                        boolean validateBusiConfig,
                                        SnapshotValidateConfigBO validateConfig,
                                        ActionNameResourceBO actionNameResource,
                                        Long userId) {
        if (CollectionUtils.isEmpty(nodeList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不能为空");
        }
        if (StringUtils.isBlank(stepId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程id不能为空");
        }
        StepPO step = stepService.getPOById(botId, stepId);
        if (Objects.isNull(step)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程不存在");
        }

        // 校验节点id是否重复
        checkNodeIdIsNullOrDuplicate(nodeList);
        validNodeLabelDuplicate(nodeList, step);

        StepNodeSaveResultVO result = new StepNodeSaveResultVO();
        result.setBotId(botId);
        result.setSuccess(true);

        // 为跳转节点设置默认值, 这个是前端实现不了, 让后端来做的
        jumpNodeDefaultJumpAction(step, nodeList);

        nodeList.forEach(node -> {
            node.setBotId(botId);
            node.setStepId(stepId);
            // 对空白答案进行清理
            cleanBlankAnswer(node);

            cleanNextNodeMapping(node);

            // 处理一下等待用户应答设置中的触发意图列表
            if (node instanceof DialogChatNodePO) {
                DialogChatNodePO chatNode = (DialogChatNodePO) node;
                chatNode.resetWaitUserSayTriggerIntentIdList();
            }

            // 处理时间
            if (Objects.isNull(node.getCreateTime())) {
                node.setCreateTime(LocalDateTime.now());
            }
            node.setUpdateTime(LocalDateTime.now());
        });
        // 自动创建变量
        result.setHasVarCreated(autoCreateVarIfNotExists(nodeList, botId, userId));

        List<SnapshotInvalidFailItemMsg> validateInfoList = new ArrayList<>();

        if (validateBusiConfig) {
            validNodeBusiConfig(botId, nodeList, validateConfig, actionNameResource, step, validateInfoList);
        }

        if (CollectionUtils.isNotEmpty(validateInfoList)) {
            result.setFailList(validateInfoList);
            result.setSuccess(false);
            result.setNodeList(nodeList);
            return result;
        }

        List<DialogBaseNodePO> oldNodeList = deleteByStepId(botId, stepId);
        Set<String> oldNodeIdSet = oldNodeList.stream().map(DialogBaseNodePO::getId).collect(Collectors.toSet());
        Integer maxNodeLabelSeq = obtainMaxNodeLabelSeq(nodeList, step);
        labelGenerateService.updateNodeLabelSeqIfNeed(botId, maxNodeLabelSeq, step.getLabel());
        generateLabel(botId, step, nodeList);

        Exception exception = null;
        try {
            mongoTemplate.insert(nodeList, DialogBaseNodePO.COLLECTION_NAME);
            compareAndCreateOperateLog(botId, stepId, oldNodeList, nodeList, userId);
        } catch (Exception e) {
            log.error("[LogHub_Warn]更新节点数据失败", e);
            exception = e;
            if (CollectionUtils.isNotEmpty(oldNodeList)) {
                oldNodeList.forEach(node -> {
                    mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
                });
            }
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "更新节点数据失败", exception);
        }
        //节点被规则依赖处理
        Set<String> newNodeIdSet = nodeList.stream().map(DialogBaseNodePO::getId).collect(Collectors.toSet());
        oldNodeIdSet.removeAll(newNodeIdSet);
        if (CollectionUtils.isNotEmpty(oldNodeIdSet)) {
            intentRuleActionService.clearInvalidNodeId(oldNodeIdSet, botId);
            intentRuleService.clearInvalidNodeId(oldNodeIdSet, botId);
        }

        // 更新引用的意图
        sourceRefService.deleteSourceByParentRefId(botId, stepId, IntentRefTypeEnum.STEP);
        DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable());
        addSourceRef(nodeList, dependentResource);
        updateBotToDraft(botId);
        result.setNodeList(nodeList);
        return result;
    }

    private void validNodeBusiConfig(Long botId, List<DialogBaseNodePO> nodeList,
                                     SnapshotValidateConfigBO validateConfig,
                                     ActionNameResourceBO actionNameResource,
                                     StepPO step,
                                     List<SnapshotInvalidFailItemMsg> validateInfoList) {
        try {
            Map<String, List<String>> errMsgMap = validWholeStep(step, false, nodeList, prepareResource(botId), actionNameResource, validateConfig);
            if (MapUtils.isNotEmpty(errMsgMap)) {
                Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);
                errMsgMap.forEach((nodeId, msgList) -> {
                    DialogBaseNodePO node = nodeMap.get(nodeId);
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.NODE)
                            .failMsg(String.format("%s", String.join("; ", msgList)))
                            .resourceId(step.getId())
                            .resourceName(step.getName())
                            .resourceLabel(step.getLabel())
                            .nodeId(nodeId)
                            .nodeLabel(node.getLabel())
                            .nodeName(node.getName())
                            .build();
                    validateInfoList.add(msg);
                });

            }
        } catch (ComException e) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.NODE)
                    .failMsg(e.getDetailMsg())
                    .resourceId(step.getId())
                    .resourceName(step.getName())
                    .resourceLabel(step.getLabel())
                    .build();
            if (e.getData() instanceof DialogBaseNodePO) {
                DialogBaseNodePO node = (DialogBaseNodePO) e.getData();
                msg.setNodeId(node.getId());
                msg.setNodeName(node.getName());
                msg.setNodeLabel(node.getLabel());
            } else {
                throw e;
            }
            validateInfoList.add(msg);
        }
    }

    private void checkNodeIdIsNullOrDuplicate(List<DialogBaseNodePO> nodeList) {
        nodeList.forEach(node -> {
            if (StringUtils.isBlank(node.getId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点id不能为空");
            }
        });
        Map<String, List<DialogBaseNodePO>> nodeId2ListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getId);
        nodeId2ListMap.forEach((nodeId, list) -> {
            if (list.size() > 1) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("节点id:%s 存在多次定义", nodeId));
            }
        });
    }

    private void validNodeLabelDuplicate(List<DialogBaseNodePO> nodeList, StepPO step) {
        Map<String, List<DialogBaseNodePO>> nodeLabel2ListMap = MyCollectionUtils.listToMapList(
                nodeList.stream().filter(n -> StringUtils.isNotBlank(n.getLabel())).collect(Collectors.toList()),
                DialogBaseNodePO::getLabel);
        Pattern stepLabelPattern = Pattern.compile(step.getLabel() + "-([1-9]\\d*)");
        nodeLabel2ListMap.forEach((nodeLabel, list) -> {
            if (!stepLabelPattern.matcher(nodeLabel).matches()) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("节点ID【%s】格式错误", nodeLabel));
            }
            if (list.size() > 1) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("节点ID【%s】已存在", nodeLabel));
            }
        });
    }

    private Integer obtainMaxNodeLabelSeq(List<DialogBaseNodePO> nodeList, StepPO step) {
        List<String> labelList = nodeList.stream().map(DialogBaseNodePO::getLabel).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Pattern stepNodeLabelPattern = Pattern.compile(step.getLabel() + "-([1-9]\\d*)");
        int max = 0;
        for (String label : labelList) {
            Matcher matcher = stepNodeLabelPattern.matcher(label);
            if (matcher.find() && matcher.groupCount() == 1) {
                String seqStr = matcher.group(1);
                if (StringUtils.isNotBlank(seqStr)) {
                    int seq = Integer.parseInt(seqStr);
                    if (seq > max) {
                        max = seq;
                    }
                }
            }
        }
        return max;
    }

    private Boolean autoCreateVarIfNotExists(List<DialogBaseNodePO> nodeList, Long botId, Long userId) {
        boolean hasVarCreated = false;
        if (CollectionUtils.isNotEmpty(nodeList)) {
            for (DialogBaseNodePO node : nodeList) {
                // 自动新增变量
                if(CollectionUtils.isNotEmpty(node.getAnswerList())) {
                    for (NodeAnswer answer : node.getAnswerList()) {
                        if (variableService.autoCreateIfNotExists(botId, answer.getText(), userId)) {
                            hasVarCreated = true;
                        }
                    }
                }
                // 查询节点http body中变量自动创建
                if (node instanceof DialogQueryNodePO) {
                    String body = ((DialogQueryNodePO) node).getBody();
                    if (StringUtils.isNotBlank(body)) {
                        if (variableService.autoCreateIfNotExists(botId, body, userId)) {
                            hasVarCreated = true;
                        }
                    }
                }
            }
        }
        return hasVarCreated;
    }

    /**
     * 比较节点变化, 并创建操作日志
     */
    private void compareAndCreateOperateLog(Long botId, String stepId,
                                            List<DialogBaseNodePO> oldNodeList,
                                            List<DialogBaseNodePO> nodeList,
                                            Long userId) {
        stepNodeOperationLogService.compareAndCreateOperationLog(botId, stepId, oldNodeList, nodeList, userId);
    }

    /**
     * 处理节点后续连线, 如果没有连线, 则删掉对应的key, 这个是前端要求的
     */
    private void cleanNextNodeMapping(DialogBaseNodePO node) {
        if (node instanceof NonLeafNode) {
            NonLeafNode chatNode = (NonLeafNode) node;
            if (MapUtils.isNotEmpty(chatNode.getRelatedNodeMap())) {
                Map<String, String> newMapping = new HashMap<>();
                chatNode.getRelatedNodeMap().forEach((k, v) -> {
                    if (StringUtils.isNotBlank(k) && StringUtils.isNotBlank(v)) {
                        newMapping.put(k, v);
                    }
                });
                chatNode.setRelatedNodeMap(newMapping);
            }
        }
    }

    /**
     * 在节点复制的时候, 如果前端没有清理掉label字段, 需要后端重新遍历一遍, 清理掉旧的标签
     */
    private void cleanOldNodeLabel(Set<String> oldNodeIdSet, List<DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        for (DialogBaseNodePO node : nodeList) {
            if (!oldNodeIdSet.contains(node.getId())) {
                node.setLabel(null);
                if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                    for (NodeAnswer nodeAnswer : node.getAnswerList()) {
                        nodeAnswer.setLabel(null);
                    }
                }
            }
        }
    }

    private void addSourceRef(List<DialogBaseNodePO> nodeList, DependentResourceBO dependentResource) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        nodeList.forEach(node -> {
            //变量依赖
            Set<String> variableIdSet = node.calDependVariableIdSet(dependentResource);
            if (CollectionUtils.isNotEmpty(variableIdSet)) {
                sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.VARIABLE, variableIdSet));
            }
            //意图依赖
            if (NodeTypeEnum.CHAT.equals(node.getType()) && node instanceof DialogChatNodePO) {
                DialogChatNodePO chatNodePO = (DialogChatNodePO) node;
                if (CollectionUtils.isNotEmpty(chatNodePO.getSelectIntentIdList())) {
                    Set<String> intentIdSet = new HashSet<>(chatNodePO.getSelectIntentIdList());
                    intentIdSet.remove(ApplicationConstant.DEFAULT_INTENT_ID);
                    intentIdSet.remove(ApplicationConstant.USER_SILENCE_INTENT_ID);
                    if (CollectionUtils.isNotEmpty(intentIdSet)) {
                        sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.INTENT, intentIdSet));
                    }
                }
            }

            // 实体的依赖
            if (Objects.nonNull(node.getEntityAssign())) {
                Set<String> entityIdSet = node.getEntityAssign().getDependentEntityIdSet();
                sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.ENTITY, entityIdSet));
            }

            // 判断节点分支依赖意图
            if (NodeTypeEnum.JUDGE.equals(node.getType()) && node instanceof DialogJudgeNodePO) {
                DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) node;
                Set<String> intentIdSet = judgeNode.calBranchDependIntentIdSet();
                if (CollectionUtils.isNotEmpty(intentIdSet)) {
                    sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.INTENT, intentIdSet, IntentRefTypeEnum.JUDGE_NODE_BRANCH));
                }
            }

            // 原话采集过滤意图
            if (BooleanUtils.isTrue(node.getEnableAssign())
                    && Objects.nonNull(node.getOriginInputAssign())
                    && CollectionUtils.isNotEmpty(node.getOriginInputAssign().getDependIntentIdList())) {
                sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.INTENT, new HashSet<>(node.getOriginInputAssign().getDependIntentIdList()), IntentRefTypeEnum.ORIGIN_INPUT));
            }

            // 信息采集节点
            if (node instanceof DialogCollectNodePO) {
                DialogCollectNodePO collectNode = (DialogCollectNodePO) node;

                // 意图
                if (BooleanUtils.isTrue(collectNode.getEnableSkipCondition()) && CollectionUtils.isNotEmpty(collectNode.getSkipConditionList())) {
                    Set<String> intentIdList = collectNode.getSkipConditionList().stream()
                            .map(CollectNodeSkipCondition::getIntentIdList)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(intentIdList)) {
                        sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.INTENT, intentIdList, IntentRefTypeEnum.SKIP_CONDITION));
                    }
                }

                // 实体
                if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                    Set<String> entityIdSet = collectNode.getEntityCollectList().stream()
                            .map(CollectNodeEntityItemPO::getEntityId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(entityIdSet)) {
                        sourceRefService.saveSourceRef(buildParam(node, SourceTypeEnum.ENTITY, entityIdSet));
                    }
                }
            }
        });
    }

    private void updateNodeVariableRef(Long botId, List<DialogBaseNodePO> nodeList, DependentResourceBO dependentResource) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        sourceRefService.deleteSourceByRefIds(botId, nodeList.stream().map(DialogBaseNodePO::getId).collect(Collectors.toList()), SourceTypeEnum.VARIABLE);
        List<SourceRefPO> sourceRefList = new ArrayList<>();
        nodeList.forEach(node -> {
            //变量依赖
            Set<String> variableIdSet = node.calDependVariableIdSet(dependentResource);
            if (CollectionUtils.isNotEmpty(variableIdSet)) {
                variableIdSet.forEach(variableId -> {
                    SourceRefPO variableRef = new SourceRefPO();
                    variableRef.setBotId(node.getBotId());
                    variableRef.setSourceId(variableId);
                    variableRef.setRefId(node.getId());
                    variableRef.setParentRefId(node.getStepId());
                    variableRef.setParentRefType(IntentRefTypeEnum.STEP);
                    variableRef.setSourceType(SourceTypeEnum.VARIABLE);
                    variableRef.setRefType(IntentRefTypeEnum.NODE);
                    variableRef.setRefLabel(node.getLabel());
                    sourceRefList.add(variableRef);
                });
            }
        });

        sourceRefService.batchAddSourceRef(sourceRefList);
    }

    private SourceRefBO buildParam(DialogBaseNodePO node, SourceTypeEnum sourceType, Set<String> sourceIdSet) {
        return buildParam(node, sourceType, sourceIdSet, IntentRefTypeEnum.NODE);
    }

    private SourceRefBO buildParam(DialogBaseNodePO node, SourceTypeEnum sourceType, Set<String> sourceIdSet, IntentRefTypeEnum refType) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setSourceIdSet(sourceIdSet);
        sourceRefBO.setBotId(node.getBotId());
        sourceRefBO.setParentRefId(node.getStepId());
        sourceRefBO.setParentRefType(IntentRefTypeEnum.STEP);
        sourceRefBO.setRefLabel(node.getLabel());
        sourceRefBO.setSourceType(sourceType);
        sourceRefBO.setRefId(node.getId());
        sourceRefBO.setRefType(refType);
        return sourceRefBO;
    }

    private void jumpNodeDefaultJumpAction(StepPO step, List<DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }

        JumpTypeEnum jumpType = StepTypeEnum.MAIN.equals(step.getType()) ? JumpTypeEnum.NEXT_STEP : JumpTypeEnum.HANG_UP;
        nodeList.forEach(node -> {
            if (node instanceof DialogJumpNodePO) {
                DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
                if (Objects.isNull(jumpNode.getJumpType())) {
                    jumpNode.setJumpType(jumpType);
                }
            }
        });
    }

    private void updateBotToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    private void cleanBlankAnswer(DialogBaseNodePO node) {
        Function<List<NodeAnswer>, List<NodeAnswer>> removeBlankAnswer = (list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return list;
            }
            return list.stream()
                    .filter(answer -> StringUtils.isNotBlank(answer.getText()))
                    .collect(Collectors.toList());
        };
        if (node instanceof DialogCollectNodePO) {
            DialogCollectNodePO collectNode = (DialogCollectNodePO) node;
            if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                collectNode.getEntityCollectList().forEach(item -> {
                    item.setAnswerList(removeBlankAnswer.apply(item.getAnswerList()));
                });
            }
        } else {
            node.setAnswerList(removeBlankAnswer.apply(node.getAnswerList()));
        }
    }

    private DependentResourceBO prepareResource(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        condition.intent().step().variable().knowledge().specialAnswerConfig().entity();
        return dependResourceService.generateByCondition(condition);
    }

    private Map<String, String> getIntentIdNameMap(Long botId) {
        return MyCollectionUtils.listToConvertMap(intentService.getAllByBotId(botId), IntentPO::getId, IntentPO::getName);
    }

    private void validAndThrow(StepPO step,
                               Map<String, List<String>> nodeErrMsgMap,
                               DialogBaseNodePO node,
                               DependentResourceBO resource,
                               ActionNameResourceBO actionResource,
                               SnapshotValidateConfigBO validateConfig) {
        if (StringUtils.isBlank(node.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点id不能为空");
        }
        if (Objects.isNull(node.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (StringUtils.isBlank(node.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点名称不能为空");
        }
        if (StringUtils.isBlank(node.getStepId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程id不能为空");
        }
        if (Objects.isNull(node.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点类型不能为空");
        }

        if (NodeTypeEnum.CHAT.equals(node.getType())) {
            validateChatNode(nodeErrMsgMap, (DialogChatNodePO) node, resource);
        } else if (NodeTypeEnum.JUMP.equals(node.getType())) {
            validateJumpNode(nodeErrMsgMap, (DialogJumpNodePO) node, resource);
        } else if (NodeTypeEnum.JUDGE.equals(node.getType())) {
            validateJudegeNode(nodeErrMsgMap, (DialogJudgeNodePO) node, resource);
        } else if (NodeTypeEnum.QUERY.equals(node.getType())) {
            validateQueryNode(nodeErrMsgMap, (DialogQueryNodePO) node, resource);
        } else if (NodeTypeEnum.KEY_CAPTURE.equals(node.getType())) {
            validateKeyCaptureNode(nodeErrMsgMap, (DialogKeyCaptureNodePO) node, resource);
        } else if (NodeTypeEnum.COLLECT.equals(node.getType())) {
            validateCollectNode(nodeErrMsgMap, (DialogCollectNodePO) node, resource);
        }

        validAnswer(nodeErrMsgMap, node, resource);

        if (BooleanUtils.isTrue(node.getIsEnableAction())
                && Objects.nonNull(validateConfig)
                && BooleanUtils.isTrue(validateConfig.getRequireValidOutsideResource())) {
            validAction(nodeErrMsgMap, node, actionResource);
        }

        // 校验动态变量赋值配置
        validateVariableAssign(step, node, resource);
    }

    private void validateVariableAssign(StepPO step,
                                        DialogBaseNodePO node,
                                        DependentResourceBO resource) {
        if (BooleanUtils.isNotTrue(node.getEnableAssign())) {
            return;
        }
        if (Objects.nonNull(node.getEntityAssign())
                && node instanceof DialogJumpNodePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: 节点动态变量赋值类型不能为[%s]",
                    getNodeDisplayInfo(node), VariableAssignTypeEnum.ENTITY_COLLECT.getDesc())).setData(node);
        }

        int actionCount = 0;
        if (Objects.nonNull(node.getConstantAssign()) && CollectionUtils.isNotEmpty(node.getConstantAssign().getAssignActionList())) {
            actionCount = node.getConstantAssign().getAssignActionList().size();
        }
        if (Objects.nonNull(node.getEntityAssign()) && CollectionUtils.isNotEmpty(node.getEntityAssign().getAssignActionList())) {
            actionCount += node.getEntityAssign().getAssignActionList().size();
        }
        if (Objects.nonNull(node.getOriginInputAssign()) && CollectionUtils.isNotEmpty(node.getOriginInputAssign().getAssignActionList())) {
            actionCount += node.getOriginInputAssign().getAssignActionList().size();
        }

        if (actionCount < 1) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: 动态变量赋值配置不完整", getNodeDisplayInfo(node))).setData(node);
        }
        if (Objects.nonNull(node.getConstantAssign())) {
            try {
                node.getConstantAssign().validateConfig(step, resource, node);
            } catch (Exception e) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: %s", getNodeDisplayInfo(node), e.getMessage())).setData(node);
            }
        }
        if (Objects.nonNull(node.getEntityAssign())) {
            try {
                node.getEntityAssign().validateConfig(step, resource, node);
            } catch (Exception e) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: %s", getNodeDisplayInfo(node), e.getMessage())).setData(node);
            }
        }
        if (Objects.nonNull(node.getOriginInputAssign())) {
            try {
                node.getOriginInputAssign().validateConfig(step, resource, node);
            } catch (Exception e) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: %s", getNodeDisplayInfo(node), e.getMessage())).setData(node);
            }
        }
    }

    private void validAction(Map<String, List<String>> nodeErrMsgMap,
                             DialogBaseNodePO node, ActionNameResourceBO actionResource) {
        if (Objects.isNull(actionResource)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(node.getActionList())) {
            node.getActionList().forEach(actionParam -> {
                if (Objects.isNull(actionParam.getActionType())) {
                    appendErrorMsg(nodeErrMsgMap, node, String.format("%s: 动作类型不能为空", getNodeDisplayInfo(node)));
                }
                if (!ActionCategoryEnum.ADD_WECHAT.equals(actionParam.getActionType()) && CollectionUtils.isEmpty(actionParam.getSourceIdList())) {
                    appendErrorMsg(nodeErrMsgMap, node, String.format("%s: [%s]内容不能为空", getNodeDisplayInfo(node), actionParam.getActionType().getDesc()));
                    return;
                }
                switch (actionParam.getActionType()) {
                    case WHITE_LIST:
                        checkParamValidity(nodeErrMsgMap, node, actionParam, actionResource.getGroupId2NameMap());
                        break;
                    case SEND_SMS:
                        checkParamValidity(nodeErrMsgMap, node, actionParam, actionResource.getSmsTempId2NameMap());
                        break;
                    case ADD_TAG:
                        checkParamValidity(nodeErrMsgMap, node, actionParam, actionResource.getTagId2NameMap());
                        break;
                    default:
                        break;
                }
            });
        }
    }

    private void checkParamValidity(Map<String, List<String>> nodeErrMsgMap,
                                    DialogBaseNodePO node,
                                    RuleActionParam actionParam,
                                    Map<Long, String> id2NameMap) {
        if (Objects.isNull(id2NameMap)) {
            return;
        }
        for (IdNamePair<Long, String> idNamePair : actionParam.getSourceIdList()) {
            if (id2NameMap.containsKey(idNamePair.getId())) {
                break;
            }
            if (actionParam.getSourceIdList().indexOf(idNamePair) == actionParam.getSourceIdList().size() - 1) {
                appendErrorMsg(nodeErrMsgMap, node, String.format("%s: [%s]内容不能为空", getNodeDisplayInfo(node), actionParam.getActionType().getDesc()));
            }
        }
    }

    private void validAnswer(Map<String, List<String>> nodeErrorInfoMap, DialogBaseNodePO node, DependentResourceBO resource) {
        // 答案合法校验
        if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
            node.getAnswerList().forEach(answer -> {
                try {
                    answer.validWithResource(resource);
                } catch (ComException e) {
                    appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 答案[%s]校验错误, %s", getNodeDisplayInfo(node), answer.getText(), e.getDetailMsg()));
                }
            });
        }
    }

    private void validBaseParam(Map<String, List<String>> nodeErrorInfoMap,
                                DialogBaseNodePO node,
                                DependentResourceBO resource) {

        if (BooleanUtils.isTrue(node.getEnableIntentLevel()) && Objects.isNull(node.getIntentLevelDetailCode())) {
            appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 未选择意向等级", getNodeDisplayInfo(node)));
        }

        if (node instanceof DialogChatNodePO) {
            DialogChatNodePO chatNode = (DialogChatNodePO) node;
            List<String> selectIntentIdList = chatNode.getSelectIntentIdList();
            List<String> uninterruptedReplyBranchIntentIdList = node.getUninterruptedReplyBranchIntentIdList();
            if (CollectionUtils.isNotEmpty(uninterruptedReplyBranchIntentIdList)) {
                if (Objects.isNull(selectIntentIdList)) {
                    selectIntentIdList = Collections.emptyList();
                }
                if (uninterruptedReplyBranchIntentIdList.contains(ApplicationConstant.USER_SILENCE_INTENT_ID)
                        || uninterruptedReplyBranchIntentIdList.contains(ApplicationConstant.DEFAULT_INTENT_ID)
                        || uninterruptedReplyBranchIntentIdList.contains(ApplicationConstant.COLLECT_FAILED_INTENT_ID)) {
                    appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 不可打断仍允许跳转到节点分支不支持选择客户无应答、兜底意图、采集失败", getNodeDisplayInfo(node)));
                }
                if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(uninterruptedReplyBranchIntentIdList, selectIntentIdList))) {
                    appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 不可打断仍允许跳转到节点分支仅支持选择当前节点分支", getNodeDisplayInfo(node)));
                }
            }
        }
        node.validMismatchAndInterruptConflict(getNodeDisplayInfo(node), resource).forEach(errMsg -> appendErrorMsg(nodeErrorInfoMap, node, errMsg));
    }

    private void validateChatNode(Map<String, List<String>> nodeErrorInfoMap,
                            DialogChatNodePO chatNode,
                            DependentResourceBO resource) {
        validBaseParam(nodeErrorInfoMap, chatNode, resource);
        if (CollectionUtils.isEmpty(chatNode.getAnswerList())) {
            if (!NodeTypeEnum.COLLECT.equals(chatNode.getType())) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 答案列表不能为空", getNodeDisplayInfo(chatNode)));
            }
        }
        if (CollectionUtils.isEmpty(chatNode.getSelectIntentIdList())) {
            appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 关联意图不能为空", getNodeDisplayInfo(chatNode)));
        }

        // 关联意图校验
        chatNode.getSelectIntentIdList().forEach(intentId -> {
            String nodeId = chatNode.getIntentRelatedNodeMap().get(intentId);
            String intentName = resource.getIntentIdNameMap().getOrDefault(intentId, "");
            if (StringUtils.isBlank(nodeId)) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: %s分支未连线", getNodeDisplayInfo(chatNode), intentName));
            }
        });

        // 自定义重复阈值校验
        if (BooleanUtils.isTrue(chatNode.getEnableCustomReplay())) {
            if (Objects.isNull(chatNode.getCustomReplayThreshold())) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 自定义重复播放阈值不能为空", getNodeDisplayInfo(chatNode)));
            }
            if (chatNode.getCustomReplayThreshold() < 0 || chatNode.getCustomReplayThreshold() > 100) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 自定义重复播放阈值取值错误, 合法范围为[0, 100]", getNodeDisplayInfo(chatNode)));
            }
        }

        // 自定义客户无应答时长校验
        if (BooleanUtils.isTrue(chatNode.getEnableCustomUserSilence())) {
            if (Objects.isNull(chatNode.getCustomUserSilenceSecond())) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 客户无应答时长不能为空", getNodeDisplayInfo(chatNode)));
            }
            if (chatNode.getCustomUserSilenceSecond() < 0.1d || chatNode.getCustomUserSilenceSecond() > 10d) {
                appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 客户无应答时长取值错误, 合法范围为[0.1, 10]", getNodeDisplayInfo(chatNode)));
            }
        }

        // 判断未开启动态变量赋值但是开启了采集成功分支
        if (chatNode.getRelatedNodeMap().containsKey(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID) && !NodeTypeEnum.KEY_CAPTURE.equals(chatNode.getType())) {
            if (!NodeTypeEnum.COLLECT.equals(chatNode.getType())) {
                // todo
                if ((Objects.isNull(chatNode.getEntityAssign()) && Objects.isNull(chatNode.getOriginInputAssign()))
                        || BooleanUtils.isFalse(chatNode.getEnableAssign())) {
                    appendErrorMsg(nodeErrorInfoMap, chatNode, String.format("%s: 仅[实体/原话采集]类型的动态变量赋值, 才可选择采集成功分支", getNodeDisplayInfo(chatNode)));
                }
            }
        }
    }

    private void validateJumpNode(Map<String, List<String>> nodeErrorInfoMap,
                            DialogJumpNodePO jumpNode, DependentResourceBO resource) {
        validBaseParam(nodeErrorInfoMap, jumpNode, resource);
        if (Objects.isNull(jumpNode.getJumpType())) {
            appendErrorMsg(nodeErrorInfoMap, jumpNode, String.format("%s: 设置错误, 跳转动作不能为空", getNodeDisplayInfo(jumpNode)));
        }
        if (JumpTypeEnum.SPECIFIED_STEP.equals(jumpNode.getJumpType())) {
            if (StringUtils.isBlank(jumpNode.getJumpStepId())) {
                appendErrorMsg(nodeErrorInfoMap, jumpNode, String.format("%s: 设置错误, 指定流程不能为空", getNodeDisplayInfo(jumpNode)));
            }
            if (!resource.getStepIdNameMap().containsKey(jumpNode.getJumpStepId())) {
                appendErrorMsg(nodeErrorInfoMap, jumpNode, String.format("%s: 设置错误, 指定流程不存在, 流程id=%s", getNodeDisplayInfo(jumpNode), jumpNode.getJumpStepId()));
            }
            if (jumpNode.getStepId().equals(jumpNode.getJumpStepId())) {
                appendErrorMsg(nodeErrorInfoMap, jumpNode, String.format("%s: 设置错误, 不能跳转到当前流程", getNodeDisplayInfo(jumpNode)));
            }
        }
    }

    private void validateCollectNode(Map<String, List<String>> nodeErrorInfoMap,
                                     DialogCollectNodePO collectNode,
                                     DependentResourceBO resource) {

        // 先校验基础参数
        validateChatNode(nodeErrorInfoMap, collectNode, resource);

        // 校验引导话术
        if (CollectionUtils.isEmpty(collectNode.getEntityCollectList())) {
            appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体采集列表不能为空", getNodeDisplayInfo(collectNode)));
        } else {
            Set<String> entityNameSet = new HashSet<>();
            for (CollectNodeEntityItemPO entityItem : collectNode.getEntityCollectList()) {
                if (CollectionUtils.isEmpty(entityItem.getAnswerList())) {
                    appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体[%s]的引导话术列表不能为空",
                            getNodeDisplayInfo(collectNode), resource.getEntityId2NameMap().getOrDefault(entityItem.getEntityId(), "")));
                }

                String entityName = resource.getEntityId2NameMap().getOrDefault(entityItem.getEntityId(), "");
                if (entityNameSet.contains(entityName)) {
                    appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体[%s]重复配置",
                            getNodeDisplayInfo(collectNode), entityName));
                } else {
                    entityNameSet.add(entityName);
                }

                if (StringUtils.isBlank(entityName)) {
                    appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体采集配置中的实体未配置",
                            getNodeDisplayInfo(collectNode)));
                } else {
                    if (!resource.getVariableIdNameMap().containsKey(entityItem.getVariableId())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体[%s]的保存至变量不能为空",
                                getNodeDisplayInfo(collectNode), entityItem));
                    }

                    if (Objects.isNull(entityItem.getRepeatCount())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 实体[%s]的引导次数不能为空",
                                getNodeDisplayInfo(collectNode), entityItem));
                    }
                }
            }
        }

        // 校验条件
        if (BooleanUtils.isTrue(collectNode.getEnableSkipCondition())) {
            if (CollectionUtils.isEmpty(collectNode.getSkipConditionList())) {
                appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 拒答跳过设置条件列表不能为空",
                        getNodeDisplayInfo(collectNode)));
            } else {
                for (CollectNodeSkipCondition skipCondition : collectNode.getSkipConditionList()) {
                    if (Objects.isNull(skipCondition.getPreVarType())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 拒答跳过设置条件中的前置变量类型不能为空",
                                getNodeDisplayInfo(collectNode)));
                    }
                    if (Objects.isNull(skipCondition.getCondition())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 拒答跳过设置条件中的操作符不能为空",
                                getNodeDisplayInfo(collectNode)));
                    }
                    if (CollectionUtils.isEmpty(skipCondition.getIntentIdList())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 拒答跳过设置条件中的意图列表不能为空",
                                getNodeDisplayInfo(collectNode)));
                    }
                    if (Objects.isNull(skipCondition.getSkipType())) {
                        appendErrorMsg(nodeErrorInfoMap, collectNode, String.format("%s: 拒答跳过设置条件中的值不能为空",
                                getNodeDisplayInfo(collectNode)));
                    }
                }
            }
        }

    }

    private void validateKeyCaptureNode(Map<String, List<String>> nodeErrorInfoMap,
                            DialogKeyCaptureNodePO keyCaptureNode,
                            DependentResourceBO resource) {
        validateChatNode(nodeErrorInfoMap, (DialogChatNodePO)keyCaptureNode, resource);

        if (StringUtils.isBlank(keyCaptureNode.getResultVarId())) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 动态变量不能为空", getNodeDisplayInfo(keyCaptureNode)));
        } else if (VariableTypeEnum.isNotDynamicVariable(resource.getVarIdTypeMap().get(keyCaptureNode.getResultVarId()))) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 动态变量类型异常", getNodeDisplayInfo(keyCaptureNode)));
        }

        if(Objects.isNull(keyCaptureNode.getCommitMode())) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 按键提交方式不能为空", getNodeDisplayInfo(keyCaptureNode)));
        }

        Integer timeout = keyCaptureNode.getTimeout();
        if (Objects.isNull(timeout)) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 超时时间不能为空", getNodeDisplayInfo(keyCaptureNode)));
        } else if (timeout < 1 || timeout > 99) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 超时时间取值错误, 合法范围为[1, 99]", getNodeDisplayInfo(keyCaptureNode)));
        }

        if (BooleanUtils.isTrue(keyCaptureNode.getEnableRetryOnFailed())) {
            Integer retryTimes = keyCaptureNode.getRetryTimes();
            if (Objects.isNull(retryTimes)) {
                appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 重复次数不能为空", getNodeDisplayInfo(keyCaptureNode)));
            } else if (retryTimes < 1 || retryTimes > 5) {
                appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 重复次数取值错误, 合法范围为[1, 5]", getNodeDisplayInfo(keyCaptureNode)));
            }
        }

        List<String> selectIntentIdList = keyCaptureNode.getSelectIntentIdList();
        if (CollectionUtils.isEmpty(selectIntentIdList) || selectIntentIdList.size() != 2
            || !selectIntentIdList.containsAll(Arrays.asList(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_FAILED_INTENT_ID))) {
            appendErrorMsg(nodeErrorInfoMap, keyCaptureNode, String.format("%s: 关联意图仅支持采集成功和采集失败", getNodeDisplayInfo(keyCaptureNode)));
        }
    }

    private void validateQueryNode(Map<String, List<String>> nodeErrorInfoMap,
                            DialogQueryNodePO queryNode,
                            DependentResourceBO resource) {
        validateChatNode(nodeErrorInfoMap, queryNode, resource);

        if (CollectionUtils.isNotEmpty(queryNode.getSelectIntentIdList())) {
            if (queryNode.getSelectIntentIdList().size() > 1 || !StringUtils.equals(queryNode.getSelectIntentIdList().get(0), ApplicationConstant.DEFAULT_INTENT_ID)) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 仅支持配置兜底意图", getNodeDisplayInfo(queryNode)));
            }
        }

        if (QueryNodeApiTypeEnum.CUSTOM.equals(queryNode.getApiType())) {
            if (Objects.isNull(queryNode.getHttpMethod())) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 请求方式不能为空", getNodeDisplayInfo(queryNode)));
            }

            if (StringUtils.isBlank(queryNode.getUrl())) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 接口地址不能为空", getNodeDisplayInfo(queryNode)));
            }

            validHttpParamInfo(queryNode.getQueryList(), nodeErrorInfoMap, queryNode, resource, "Query");
            validHttpParamInfo(queryNode.getHeaderList(), nodeErrorInfoMap, queryNode, resource, "Header");
        }

        if (QueryNodeApiTypeEnum.BUILT_IN.equals(queryNode.getApiType())) {
            if (StringUtils.isBlank(queryNode.getBuiltInApiName())) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 内置接口名称不能为空", getNodeDisplayInfo(queryNode)));
            }
        }

        if (MapUtils.isNotEmpty(queryNode.getResMap())
                && queryNode.getResMap().keySet().stream().anyMatch(varId -> VariableTypeEnum.isNotDynamicVariable(resource.getVarIdTypeMap().get(varId)))) {
            appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 查询结果中动态变量类型异常", getNodeDisplayInfo(queryNode)));
        }

        Integer timeout = queryNode.getTimeout();
        if (Objects.isNull(timeout)) {
            appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 超时时间配置不能为空", getNodeDisplayInfo(queryNode)));
        } else if (timeout < 1 || timeout > 10) {
            appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 超时时间仅支持区间[1,10]", getNodeDisplayInfo(queryNode)));
        }

        String errorVarId = queryNode.getErrorVarId();
        if (StringUtils.isNotBlank(errorVarId) && VariableTypeEnum.isNotDynamicVariable(resource.getVarIdTypeMap().get(errorVarId))) {
            appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: 记录超时或者失败结果动态变量类型异常", getNodeDisplayInfo(queryNode)));
        }
    }

    private void validHttpParamInfo(List<DialogQueryNodeHttpParamInfo> paramInfoList, Map<String, List<String>> nodeErrorInfoMap,
                                    DialogQueryNodePO queryNode, DependentResourceBO resource, String location) {
        if (CollectionUtils.isEmpty(paramInfoList)) {
            return;
        }
        for (DialogQueryNodeHttpParamInfo paramInfo : queryNode.getQueryList()) {
            if (StringUtils.isBlank(paramInfo.getName())) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中参数名不能为空", getNodeDisplayInfo(queryNode), location));
            }
            QueryNodeHttpVarTypeEnum variableType = paramInfo.getVariableType();
            if (Objects.isNull(variableType)) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中变量类型不能为空", getNodeDisplayInfo(queryNode), location));
            }
            if (QueryNodeHttpVarTypeEnum.isConstant(variableType) && StringUtils.isBlank(paramInfo.getValue())) {
                appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中常量值不能为空", getNodeDisplayInfo(queryNode), location));
            }
            String varId = paramInfo.getValue();
            if (QueryNodeHttpVarTypeEnum.isCustomVariable(variableType)) {
                if (StringUtils.isBlank(varId)) {
                    appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中自定义变量值不能为空", getNodeDisplayInfo(queryNode), location));
                }
                VariableTypeEnum varType = resource.getVarIdTypeMap().get(varId);
                if (VariableTypeEnum.isNotCustomVariable(varType) && VariableTypeEnum.isNotSystemVariable(varType)) {
                    appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中自定义变量类型异常", getNodeDisplayInfo(queryNode), location));
                }
            }
            if (QueryNodeHttpVarTypeEnum.isDynamicVariable(variableType)) {
                if (StringUtils.isBlank(varId)) {
                    appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中动态变量值不能为空", getNodeDisplayInfo(queryNode), location));
                }
                VariableTypeEnum varType = resource.getVarIdTypeMap().get(varId);
                if (VariableTypeEnum.isNotDynamicVariable(varType)) {
                    appendErrorMsg(nodeErrorInfoMap, queryNode, String.format("%s: %s中动态变量类型异常", getNodeDisplayInfo(queryNode), location));
                }
            }
        }
    }

    private void validateJudegeNode(Map<String, List<String>> nodeErrorInfoMap,
                            DialogJudgeNodePO judgeNode,
                            DependentResourceBO resource) {
        validBaseParam(nodeErrorInfoMap, judgeNode, resource);

        // 校验条件组, 第一个条件组是默认分支, 不能有任何条件, 其余所有条件组必要要有条件
        if (CollectionUtils.isEmpty(judgeNode.getBranchList())) {
            appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 条件组不能为空", getNodeDisplayInfo(judgeNode)));
            return;
        }
        Map<String, String> branchNodeMap = judgeNode.getBranchRelatedNodeMap();
        if (Objects.isNull(branchNodeMap)) {
            branchNodeMap = new HashMap<>();
        }
        Set<String> branchNameSet = new HashSet<>();
        for (int i = 0; i < judgeNode.getBranchList().size(); i++) {
            boolean isLast = i == judgeNode.getBranchList().size() - 1;
            NodeConditionBranchPO branch = judgeNode.getBranchList().get(i);
            if (StringUtils.isBlank(branch.getName())) {
                appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 第%s个分支, 条件组名称不能为空", getNodeDisplayInfo(judgeNode), i + 1));
            }
            if (branchNameSet.contains(branch.getName())) {
                appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 第%s个分支, 分支名称[%s]重复", getNodeDisplayInfo(judgeNode), i + 1, branch.getName()));
            }
            branchNameSet.add(branch.getName());
            if (StringUtils.isBlank(branch.getId())) {
                appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 分支:%s, 条件组id不能为空", getNodeDisplayInfo(judgeNode), branch.getName()));
            } else {
                String nextNodeId = branchNodeMap.get(branch.getId());
                if (StringUtils.isBlank(nextNodeId)) {
                    appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 分支[%s]未连线", getNodeDisplayInfo(judgeNode), branch.getName()));
                }
            }
            if (isLast && CollectionUtils.isNotEmpty(branch.getConditionList())) {
                appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 默认分支不能有条件", getNodeDisplayInfo(judgeNode)));
            }
            if (!isLast) {
                if (CollectionUtils.isEmpty(branch.getConditionList())) {
                    appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 第%s个分支, 条件不能为空", getNodeDisplayInfo(judgeNode), i + 1));
                } else {
                    // 校验条件配置是否正确
                    try {
                        branch.validWithResource(resource);
                    } catch (ComException e) {
                        appendErrorMsg(nodeErrorInfoMap, judgeNode, String.format("%s: 分支[%s]条件组校验错误, %s", getNodeDisplayInfo(judgeNode), branch.getName(), e.getDetailMsg()));
                    }
                }
            }

        }
    }

    @Override
    public List<DialogBaseNodePO> deleteByStepId(Long botId, String stepId) {
        return deleteByStepIdList(botId, Collections.singletonList(stepId));
    }

    @Override
    public List<DialogBaseNodePO> deleteByStepIdList(Long botId, List<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("stepId").in(stepIdList));
        List<DialogBaseNodePO> nodeList = mongoTemplate.findAllAndRemove(query, DialogBaseNodePO.COLLECTION_NAME);

        try {
            sourceRefService.deleteSourceByParentRefIdList(botId, stepIdList, IntentRefTypeEnum.STEP);
        } catch (Exception e) {
            log.error("删除意图引用失败", e);
        }
        return nodeList;
    }

    @Override
    public String generateNodeId() {
        return new ObjectId().toString();
    }

    @Override
    public List<String> generateNodeIdList(Integer count) {
        if (Objects.isNull(count) || count <= 0) {
            return Collections.emptyList();
        }
        List<String> nodeIdList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            nodeIdList.add(generateNodeId());
        }
        return nodeIdList;
    }

    @Override
    public List<DialogBaseNodePO> getStepRootNode(List<DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        Set<String> childNodeIdSet = new HashSet<>();
        nodeList.forEach(node -> {
            if (node instanceof NonLeafNode) {
                NonLeafNode chatNode = (NonLeafNode) node;
                chatNode.getRelatedNodeMap().forEach((intentId, nodeId) -> {
                    childNodeIdSet.add(nodeId);
                });
            }
        });

        // 是否存在孤立节点
        return nodeList.stream()
                .filter(node -> !childNodeIdSet.contains(node.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DialogBaseNodePO> getByDependStep(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("type").is(NodeTypeEnum.JUMP.name()))
                .addCriteria(Criteria.where("jumpType").is(JumpTypeEnum.SPECIFIED_STEP.name()))
                .addCriteria(Criteria.where("jumpStepId").is(id));
        return mongoTemplate.find(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public List<Tuple2<DialogBaseNodePO, List<String>>> getByDependStepIdList(Long botId, List<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("type").is(NodeTypeEnum.JUMP.name()))
                .addCriteria(Criteria.where("jumpType").is(JumpTypeEnum.SPECIFIED_STEP.name()))
                .addCriteria(Criteria.where("jumpStepId").in(stepIdList));
        return mongoTemplate.find(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME)
                .stream()
                .map(node -> {
                    List<String> dependList = new ArrayList<>();
                    dependList.add(node.getStepId());
                    return Tuple.of(node, dependList);
                }).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getNameByIdList(List<String> nodeIdList) {
        return MyCollectionUtils.listToConvertMap(getByIdList(nodeIdList), DialogBaseNodePO::getId, DialogBaseNodePO::getName);
    }

    @Override
    public String getNameById(String nodeId) {
        if (StringUtils.isBlank(nodeId)) {
            return null;
        }
        return getNameByIdList(Collections.singletonList(nodeId)).get(nodeId);
    }

    @Override
    public void copyStepNodeInBot(Long botId, String sourceStepId, String targetStepId) {
        List<DialogBaseNodePO> nodeList = getListByStepId(botId, sourceStepId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        StepPO step = stepService.getPOById(botId, targetStepId);
        if (Objects.isNull(step)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("目标流程不存在, 流程id=%s", targetStepId));
        }
        Map<String, String> nodeIdMap = new HashMap<>();
        nodeList.forEach(node -> {
            node.setStepId(targetStepId);
            String oldId = node.getId();
            String newId = generateNodeId();
            nodeIdMap.put(oldId, newId);
            node.setId(newId);
            node.setCreateTime(LocalDateTime.now());
            node.setUpdateTime(LocalDateTime.now());
            node.setLabel(null);
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                node.getAnswerList().forEach(answer -> {
                    answer.setLabel(null);
                });
            }
        });

        // 更新节点id引用
        for (DialogBaseNodePO node : nodeList) {
            if (node instanceof NonLeafNode) {
                NonLeafNode chatNode = (NonLeafNode) node;
                if (MapUtils.isNotEmpty(chatNode.getRelatedNodeMap())) {
                    Map<String, String> newRelatedMap = new HashMap<>();
                    chatNode.getRelatedNodeMap().forEach((intentId, nodeId) -> {
                        newRelatedMap.put(intentId, nodeIdMap.get(nodeId));
                    });
                    chatNode.setRelatedNodeMap(newRelatedMap);
                }
            }
        }

        generateLabel(botId, step, nodeList);
        mongoTemplate.insert(nodeList, DialogBaseNodePO.COLLECTION_NAME);
        DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable());
        addSourceRef(nodeList, resource);
    }

    @Override
    public DialogBaseNodePO getById(Long botId, String stepId, String nodeId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("stepId").is(stepId))
                .addCriteria(Criteria.where("_id").is(nodeId));
        return mongoTemplate.findOne(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public List<DialogBaseNodePO> getByIdList(Long botId, List<String> nodeIdList) {
        if (CollectionUtils.isEmpty(nodeIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").in(nodeIdList));
        return mongoTemplate.find(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public void updateAnswerList(DialogBaseNodePO node) {
        if (Objects.isNull(node)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不能为空");
        }

        doUpdateAnswerList(node.getBotId(), node, node.getUpdateUserId());
    }

    private void doUpdateAnswerList(Long botId, DialogBaseNodePO node, Long userId) {
        if (Objects.isNull(node)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("stepId").is(node.getStepId()))
                .addCriteria(Criteria.where("_id").is(node.getId()));
        Update update = new Update();
        update.set("updateTime", LocalDateTime.now());
        update.set("updateUserId", userId);
        // 尽量只保存必要的字段, 尽量避免同时修改带来的脏写问题
        if (node instanceof DialogCollectNodePO) {
            update.set("entityCollectList", ((DialogCollectNodePO) node).getEntityCollectList());
        } else {
            update.set("answerList", node.getAnswerList());
        }
        mongoTemplate.updateFirst(query, update, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public void updateAnswerListAndVariableRefInfo(Long botId, List<DialogBaseNodePO> nodeList, Long userId) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        nodeList.forEach(node -> {
            doUpdateAnswerList(botId, node, userId);
        });
        updateNodeVariableRef(botId, nodeList, dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable()));
    }

    @Override
    public void updateActionList(DialogBaseNodePO node) {
        if (Objects.isNull(node)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不能为空");
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(node.getBotId()))
                .addCriteria(Criteria.where("stepId").is(node.getStepId()))
                .addCriteria(Criteria.where("_id").is(node.getId()));
        Update update = new Update();
        update.set("actionList", node.getActionList());
        mongoTemplate.updateFirst(query, update, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    private List<DialogBaseNodePO> getByIdList(List<String> nodeIdList) {
        if (CollectionUtils.isEmpty(nodeIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(nodeIdList));

        return mongoTemplate.find(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public List<DialogBaseNodePO> getListByStepId(Long botId, String stepId) {
        if (StringUtils.isBlank(stepId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程id不能为空");
        }
        List<DialogBaseNodePO> result = getListByStepIdList(botId, Collections.singletonList(stepId));
        result.forEach(this::cleanNextNodeMapping);
        return result;
    }

    @Override
    public List<DialogBaseNodeVO> getVOListByStepId(StepNodeQuery queryCondition) {
        queryCondition.setEnableAudioCompletedField(true);
        return getVOListByStepIdList(queryCondition, Collections.singletonList(queryCondition.getStepId()));
    }

    @Override
    public List<DialogBaseNodeVO> getVOListByStepIdList(StepNodeQuery queryCondition, List<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }
        Long botId = queryCondition.getBotId();
        List<DialogBaseNodePO> allNodeList = getListByStepIdList(botId, stepIdList);
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(allNodeList, DialogBaseNodePO::getStepId);

        List<IntentPO> intentList = intentService.getAllByBotId(queryCondition.getBotId());
        Map<String, SimpleIntentVO> intentMap = new HashMap<>(intentList.size() + 3);
        intentMap.put(ApplicationConstant.USER_SILENCE_INTENT_ID, SimpleIntentVO.userSilence());
        intentMap.put(ApplicationConstant.DEFAULT_INTENT_ID, SimpleIntentVO.defaultIntent());
        intentMap.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, SimpleIntentVO.collectSuccess());
        intentMap.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, SimpleIntentVO.collectFailed());
        for (IntentPO intentPO : intentList) {
            intentMap.put(intentPO.getId(), SimpleIntentVO.createFrom(intentPO));
        }
        boolean requireActionNameResource = allNodeList.stream()
                .anyMatch(node -> CollectionUtils.isNotEmpty(node.getActionList()));
        ActionNameResourceBO nameResourceBO = requireActionNameResource ? intentRuleActionService.getSourceId2NameMapByBotId(queryCondition.getBotId()) : new ActionNameResourceBO();
        Set<String> knowledgeIdSet = knowledgeService.getAllListByBotId(botId).stream().map(KnowledgePO::getId).distinct().collect(Collectors.toSet());
        Set<String> allStepIdSet = stepService.getAllListByBotId(botId).stream().map(StepPO::getId).distinct().collect(Collectors.toSet());
        Map<String, String> nodeIdLabelMap = MyCollectionUtils.listToMap(algorithmLabelService.getAllByBotId(botId), AlgorithmLabelPO::getNodeId, AlgorithmLabelPO::getLabel);
        List<DialogBaseNodeVO> result = new ArrayList<>();

        // 判断当前是否是真人录音, 如果是合成音, 则不设置是否已录音
        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        Set<String> audioAnswerTextSet = new HashSet<>();
        if (BooleanUtils.isTrue(queryCondition.getEnableAudioCompletedField())
                && Objects.nonNull(botAudioConfig)
                && !AudioTypeEnum.COMPOSE.equals(botAudioConfig.getAudioType())
                && Objects.nonNull(botAudioConfig.getRecordUserId())) {
            List<AnswerAudioMappingPO> audioMappingList = answerAudioMappingService.listAllAvailableAudio(botId, botAudioConfig.getRecordUserId(), botAudioConfig.getAudioType(), null);
            audioAnswerTextSet.addAll(audioMappingList.stream().map(AnswerAudioMappingPO::getText).map(AnswerTextUtils::removeAnswerPrefixAndSuffixSymbols).collect(Collectors.toSet()));
        }

        // 查询所有节点引用中的意图信息
        Set<String> allNodeRefIntentIdSet = sourceRefService.getListBySourceTypeAndRefType(botId, SourceTypeEnum.INTENT, IntentRefTypeEnum.NODE)
                .stream()
                .map(SourceRefPO::getSourceId)
                .collect(Collectors.toSet());

        final List<SimpleIntentVO> allNodeRefIntentList = allNodeRefIntentIdSet
                .stream()
                .map(intentMap::get)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(SimpleIntentVO::getName))
                .collect(Collectors.toList());

        // 引用关系中没有这两个意图, 默认加上(即使从未被引用, 也关系不大)
        allNodeRefIntentList.add(0, SimpleIntentVO.defaultIntent());
        allNodeRefIntentList.add(1, SimpleIntentVO.userSilence());
        allNodeRefIntentList.add(2, SimpleIntentVO.collectSuccess());
        allNodeRefIntentList.add(3, SimpleIntentVO.collectFailed());

        stepNodeListMap.forEach((stepId, poList) -> {
            List<DialogBaseNodePO> rootNodes = getStepRootNode(poList);
            Set<String> rootIdSet = rootNodes.stream().map(DialogBaseNodePO::getId).collect(Collectors.toSet());
            List<DialogBaseNodeVO> voList = poList.stream()
                    .map(po -> {
                        DialogBaseNodeVO vo = convertPo2Vo(po);
                        if (CollectionUtils.isNotEmpty(vo.getActionList())) {
                            intentRuleActionService.addSourceName(vo.getActionList(), nameResourceBO);
                        }
                        vo.setIsRoot(rootIdSet.contains(vo.getId()));
                        return vo;
                    })
                    .peek(vo -> {
                        if (vo instanceof DialogChatNodeVO) {
                            DialogChatNodeVO chat = (DialogChatNodeVO) vo;
                            List<NodeIntentBranchStatsVO> intentPairList = chat.getSelectIntentIdList().stream()
                                    .map(intentMap::get)
                                    .filter(Objects::nonNull)
                                    .map(intent -> MyBeanUtils.copy(intent, NodeIntentBranchStatsVO.class))
                                    .collect(Collectors.toList());
                            chat.setIntentInfoList(intentPairList);
                            chat.setAllNodeRefIntentList(allNodeRefIntentList);
                        }
                        if (CollectionUtils.isNotEmpty(vo.getMismatchStepIdList())){
                            vo.setMismatchStepIdList(vo.getMismatchStepIdList().stream().filter(allStepIdSet::contains).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(vo.getMismatchKnowledgeIdList())){
                            vo.setMismatchKnowledgeIdList(vo.getMismatchKnowledgeIdList().stream().filter(knowledgeIdSet::contains).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(vo.getUninterruptedReplyStepIdList())) {
                            vo.setUninterruptedReplyStepIdList(vo.getUninterruptedReplyStepIdList().stream().filter(allStepIdSet::contains).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(vo.getUninterruptedReplyKnowledgeIdList())) {
                            vo.setUninterruptedReplyKnowledgeIdList(vo.getUninterruptedReplyKnowledgeIdList().stream().filter(knowledgeIdSet::contains).collect(Collectors.toList()));
                        }
                        vo.setAlgorithmLabel(nodeIdLabelMap.get(vo.getId()));
                    })
                    // 设置是否完成录音字段
                    .peek(node -> {
                        node.setAudioCompleted(true);
                        if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                            Set<String> allAnswerPartionTextSet = node.getAnswerList().stream()
                                    .map(BaseAnswerContent::getText)
                                    .filter(StringUtils::isNotBlank)
                                    .map(text -> {
                                        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, AudioTypeEnum.MIXTURE.equals(botAudioConfig.getAudioType()));
                                        return splitter.getTextList();
                                    })
                                    .flatMap(Collection::stream)
                                    .filter(StringUtils::isNotBlank)
                                    .map(AnswerTextUtils::removeAnswerPrefixAndSuffixSymbols)
                                    .collect(Collectors.toSet());
                            node.setAudioCompleted(audioAnswerTextSet.containsAll(allAnswerPartionTextSet));
                        }
                    })
                    .collect(Collectors.toList());
            StepNodeQuery query = new StepNodeQuery();
            BeanUtils.copyProperties(queryCondition, query);
            query.setStepId(stepId);
            result.addAll(wrapStatsInfo(voList, query));
        });
        return result;
    }

    private List<DialogBaseNodeVO> wrapStatsInfo(List<DialogBaseNodeVO> nodeList, StepNodeQuery queryCondition) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        botStatsFacadeService.wrapNodeStatsInfo(nodeList, queryCondition);
        return nodeList;
    }

    private DialogBaseNodeVO convertPo2Vo(DialogBaseNodePO po) {
        if (po instanceof DialogCollectNodePO) {
            return MyBeanUtils.copy(po, DialogCollectNodeVO.class);
        } if (po instanceof DialogQueryNodePO) {
            return MyBeanUtils.copy(po, DialogQueryNodeVO.class);
        } else if (po instanceof DialogKeyCaptureNodePO) {
            return MyBeanUtils.copy(po, DialogKeyCaptureNodeVO.class);
        } else if (po instanceof DialogChatNodePO) {
            return MyBeanUtils.copy(po, DialogChatNodeVO.class);
        } else if (po instanceof DialogJudgeNodePO) {
            return MyBeanUtils.copy(po, DialogJudgeNodeVO.class);
        } else {
            return MyBeanUtils.copy(po, DialogJumpNodeVO.class);
        }
    }

    @Override
    public List<DialogBaseNodePO> getListByStepIdList(Long botId, Collection<String> stepIdList) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("stepId").in(stepIdList));
        return mongoTemplate.find(query, DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    @Override
    public List<SimpleNodeInfoVO> getSimpleInfoList(Long botId, List<String> stepIdList) {
        return getListByStepIdList(botId, stepIdList).stream()
                .map(item -> MyBeanUtils.copy(item, SimpleNodeInfoVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<DialogBaseNodePO> getAllListByBotId(Long botId) {
        // 删除step的时候, 不删除对应的节点, 所以需要根据stepId进行查询
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<String> stepIdList = stepList.stream().map(StepPO::getId).collect(Collectors.toList());
        return getListByStepIdList(botId, stepIdList);
    }

    private Map<String, List<String>> validWholeStep(StepPO step,
                                                     Boolean isLastMainStep,
                                                     List<DialogBaseNodePO> nodeList,
                                                     DependentResourceBO resource,
                                                     ActionNameResourceBO actionResource,
                                                     SnapshotValidateConfigBO validateConfig) {

        if (CollectionUtils.isEmpty(nodeList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("流程[%s]节点列表不能为空", step.getName()));
        }

        Map<String, List<String>> nodeErrorInfoMap = new HashMap<>(16);

        Set<String> nodeIdSet = new HashSet<>();
        Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);
        nodeList.forEach(node -> {
            if (StringUtils.isBlank(node.getId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: 节点id不能为空", getNodeDisplayInfo(node))).setData(node);
            }
            if (nodeIdSet.contains(node.getId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s: 节点id重复了", getNodeDisplayInfo(node))).setData(node);
            }
            nodeIdSet.add(node.getId());
        });

        // 对依赖的资源进行校验
        nodeList.forEach(node -> {
            try {
                validAndThrow(step, nodeErrorInfoMap, node, resource, actionResource, validateConfig);
            } catch (ComException e) {
                appendErrorMsg(nodeErrorInfoMap, node, e.getDetailMsg());
            }
        });

        // 对画布完整性进行校验
        // 如果是最后一个主流程, 则所有的跳转节点不能出现跳转到下一主动流程
        nodeList.forEach(node -> {
            if (node instanceof DialogChatNodePO) {
                DialogChatNodePO chatNode = (DialogChatNodePO) node;
                chatNode.getIntentRelatedNodeMap().forEach((intentId, nodeId) -> {
                    if (node.getId().equals(nodeId)) {
                        appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 不能连接自己", getNodeDisplayInfo(node)));
                    }
                    if (!nodeIdSet.contains(nodeId)) {
                        appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: %s意图连接的节点不存在", getNodeDisplayInfo(chatNode), resource.getIntentIdNameMap().getOrDefault(intentId, intentId)));
                    }
                });
            } else if (node instanceof DialogJumpNodePO) {
                DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
                if (isLastMainStep) {
                    if (JumpTypeEnum.NEXT_STEP.equals(jumpNode.getJumpType())) {
                        appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 设置错误, 最后一个主流程无法跳转到下一个主动流程", getNodeDisplayInfo(jumpNode)));
                    }
                } else if (StepTypeEnum.INDEPENDENT.equals(step.getType())) {
                    if (JumpTypeEnum.NEXT_STEP.equals(jumpNode.getJumpType())) {
                        appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 设置错误, 独立流程无法跳转到下一个主动流程", getNodeDisplayInfo(jumpNode)));
                    }
                }
                if (StepTypeEnum.MAIN.equals(step.getType()) && JumpTypeEnum.ORIGINAL_STEP.equals(jumpNode.getJumpType())) {
                    appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 设置错误, 主流程不能设置跳转到原主流程", getNodeDisplayInfo(jumpNode)));
                }
            } else if (node instanceof DialogJudgeNodePO) {
                DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) node;
                Map<String, String> relatedNodeMap = judgeNode.getBranchRelatedNodeMap() == null ? Collections.emptyMap() : judgeNode.getBranchRelatedNodeMap();
                if (CollectionUtils.isNotEmpty(judgeNode.getBranchList())) {
                    for (NodeConditionBranchPO branch : judgeNode.getBranchList()) {
                        String branchId = branch.getId();
                        if (StringUtils.isNotBlank(branchId)
                                && relatedNodeMap.containsKey(branchId)
                                && !nodeIdSet.contains(relatedNodeMap.get(branchId))) {
                            appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 分支[%s]连接的节点不存在", getNodeDisplayInfo(judgeNode), branch.getName()));
                        }
                    }
                }
            }
        });

        // 是否存在环形树结构
        checkInvalidCircleStruct(nodeList, nodeMap, nodeErrorInfoMap);


        // 校验根节点
        List<DialogBaseNodePO> rootNodeList = getStepRootNode(nodeList);
        if (CollectionUtils.size(rootNodeList) != 1) {
            for (DialogBaseNodePO node : rootNodeList) {
                appendErrorMsg(nodeErrorInfoMap, node, String.format("%s: 流程存在多个根节点", getNodeDisplayInfo(node)));
            }
        }
        for (DialogBaseNodePO rootNode : rootNodeList) {
            if (!NodeTypeEnum.CHAT.equals(rootNode.getType())
                    && !NodeTypeEnum.QUERY.equals(rootNode.getType())
                    && !NodeTypeEnum.COLLECT.equals(rootNode.getType())
                    && !NodeTypeEnum.KEY_CAPTURE.equals(rootNode.getType())) {
                appendErrorMsg(nodeErrorInfoMap, rootNode, String.format("%s: 流程根节点不是对话节点/信息查询节点/信息采集节点/按键采集节点", getNodeDisplayInfo(rootNode)));
            }
        }

        nodeList.forEach(node -> {
            if (node instanceof NonLeafNode) {
                NonLeafNode chatNode = (NonLeafNode) node;
                // 叶子节点一定是跳转节点
                if (MapUtils.isEmpty(chatNode.getRelatedNodeMap())) {
                    appendErrorMsg(nodeErrorInfoMap, node, String.format("%S: 流程末节点不是跳转节点", getNodeDisplayInfo(node)));
                }
            }
        });

        return nodeErrorInfoMap;
    }

    /**
     * 判断是否存在环形结构
     * 1. 如果一个节点有两个不同的父节点, 则存在环形结构(因为只有一个根节点)
     * 2. 如果一直向上回溯父节点, 最终又回到当前节点, 则存在环形结构
     */
    private void checkInvalidCircleStruct(List<DialogBaseNodePO> nodeList, Map<String, DialogBaseNodePO> nodeMap, Map<String, List<String>> nodeErrorInfoMap) {
        Map<String, Set<String>> nodeParentMap = new HashMap<>();
        nodeList.forEach(node -> {
            List<String> nextNodeIdList = new ArrayList<>();
            if (node instanceof NonLeafNode) {
                NonLeafNode chatNode = (NonLeafNode) node;
                if (CollectionUtils.isNotEmpty(chatNode.getNextNodeIdList())) {
                    nextNodeIdList.addAll(chatNode.getNextNodeIdList());
                }
            }
            for (String nextNodeId : nextNodeIdList) {
                nodeParentMap.computeIfAbsent(nextNodeId, (id) -> new HashSet<>())
                        .add(node.getId());
            }
        });

        // 校验是否存在环形结构
        // 1. 一个节点有两个不同的父, 2. 从当前节点一直向上追溯, 最终能找到自己
        nodeParentMap.forEach((childNodeId, parentIdSet) -> {
            DialogBaseNodePO childNode = nodeMap.get(childNodeId);
            if (CollectionUtils.size(parentIdSet) > 1) {
                appendErrorMsg(nodeErrorInfoMap, childNode, String.format("%s: 存在非法环形结构", getNodeDisplayInfo(childNode)));
            } else {
                Set<String> paths = new HashSet<>();
                paths.add(childNodeId);
                Optional<String> parentId = parentIdSet.stream().findFirst();
                while (parentId.isPresent()) {
                    if (paths.contains(parentId.get())) {
                        if (childNodeId.equals(parentId.get())) {
                            appendErrorMsg(nodeErrorInfoMap, childNode, String.format("%s: 存在非法环形结构", getNodeDisplayInfo(childNode)));
                        }
                        break;
                    }
                    paths.add(parentId.get());
                    parentId = nodeParentMap.getOrDefault(parentId.get(), Collections.emptySet()).stream().findFirst();
                }
            }
        });
    }

    private void appendErrorMsg(Map<String, List<String>>msgMap, DialogBaseNodePO node, String msg) {
        msgMap.computeIfAbsent(node.getId(), k -> new ArrayList<>()).add(msg);
    }

    private void appendErrorMsg(Map<String, List<String>>msgMap, String nodeId, String msg) {
        msgMap.computeIfAbsent(nodeId, k -> new ArrayList<>()).add(msg);
    }

    private String getNodeDisplayInfo(DialogBaseNodePO node) {
        if (StringUtils.isBlank(node.getLabel())) {
            return String.format("%s", node.getName());
        } else {
            return String.format("%s[%s]", node.getName(), node.getLabel());
        }
    }

    @Override
    public List<DialogBaseNodePO> getAllNodeList() {
        return mongoTemplate.findAll(DialogBaseNodePO.class);
    }

    @Override
    public void resetAllNodeLabel(Long botId, String stepId) {
        List<DialogBaseNodePO> nodeList = getListByStepId(botId, stepId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        StepPO step = stepService.getById(botId, stepId);
        if (step == null) {
            return;
        }
        for (DialogBaseNodePO node : nodeList) {
            node.setLabel(null);
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                for (BaseAnswerContent answer : node.getAnswerList()) {
                    answer.setLabel(null);
                }
            }
        }
        generateLabel(botId, step, nodeList);
        for (DialogBaseNodePO nodePO : nodeList) {
            mongoTemplate.save(nodePO, DialogChatNodePO.COLLECTION_NAME);
        }
    }

    @Override
    public void resetResourceReferenceInfo(Long newBotId) {
        List<DialogBaseNodePO> nodeList = getAllListByBotId(newBotId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        DependentResourceBO dependentResource = prepareResource(newBotId);
        // 更新引用的意图
        addSourceRef(nodeList, dependentResource);
    }

    @Override
    public List<SimpleIntentVO> queryAllNodeRefIntentInfoList(Long botId) {
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        Map<String, SimpleIntentVO> intentMap = new HashMap<>(intentList.size() + 2);
        for (IntentPO intentPO : intentList) {
            intentMap.put(intentPO.getId(), SimpleIntentVO.createFrom(intentPO));
        }
        // 查询所有节点引用中的意图信息
        Set<String> allNodeRefIntentIdSet = sourceRefService.getListBySourceType(botId, SourceTypeEnum.INTENT)
                .stream()
                .filter(ref -> IntentRefTypeEnum.NODE.equals(ref.getRefType()))
                .map(SourceRefPO::getSourceId)
                .collect(Collectors.toSet());

        final List<SimpleIntentVO> allNodeRefIntentList = allNodeRefIntentIdSet
                .stream()
                .map(intentMap::get)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(SimpleIntentVO::getName))
                .collect(Collectors.toList());

        // 引用关系中没有这两个意图, 默认加上(即使从未被引用, 也关系不大)
        allNodeRefIntentList.add(0, SimpleIntentVO.defaultIntent());
        allNodeRefIntentList.add(1, SimpleIntentVO.userSilence());
        allNodeRefIntentList.add(2, SimpleIntentVO.collectSuccess());
        allNodeRefIntentList.add(3, SimpleIntentVO.collectFailed());
        return allNodeRefIntentList;
    }

    @Override
    public List<DialogBaseNodePO> breadthTraverse(List<DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return nodeList;
        }

        // 找到第一个根节点
        List<DialogBaseNodePO> rootNodeList = getStepRootNode(nodeList);
        if (CollectionUtils.isEmpty(rootNodeList)) {
            log.warn("环状结构, 广度优先遍历失败, 直接返回原列表");
            return nodeList;
        }
        DialogBaseNodePO rootNode = rootNodeList.get(0);
        Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);

        Queue<DialogBaseNodePO> workingQueue = new LinkedList<>();
        Set<String> visitedNodeIdSet = new HashSet<>();
        workingQueue.add(rootNode);
        visitedNodeIdSet.add(rootNode.getId());
        List<DialogBaseNodePO> result = new ArrayList<>();
        while (CollectionUtils.isNotEmpty(workingQueue)) {
            DialogBaseNodePO node = workingQueue.poll();
            result.add(node);
            if (node instanceof NonLeafNode) {
                NonLeafNode chatNode = (NonLeafNode) node;
                Map<String, String> relatedNodeMap = chatNode.getRelatedNodeMap();
                if (node instanceof DialogChatNodePO) {
                    // 对话节点默认意图优先处理
                    String defaultNodeId = relatedNodeMap.getOrDefault(ApplicationConstant.DEFAULT_INTENT_ID, "");
                    DialogBaseNodePO defaultChildNode = nodeMap.get(defaultNodeId);
                    if (Objects.nonNull(defaultChildNode) && !visitedNodeIdSet.contains(defaultChildNode.getId())) {
                        workingQueue.add(defaultChildNode);
                        visitedNodeIdSet.add(defaultChildNode.getId());
                    }
                }
                relatedNodeMap.forEach((intentId, nodeId) -> {
                    DialogBaseNodePO childNode = nodeMap.get(nodeId);
                    if (Objects.nonNull(childNode) && !visitedNodeIdSet.contains(childNode.getId())) {
                        workingQueue.add(childNode);
                        visitedNodeIdSet.add(childNode.getId());
                    }
                });
            }
        }

        // 遍历节点列表, 把孤立的节点加入的结果列表中
        for (DialogBaseNodePO node : nodeList) {
            if (!visitedNodeIdSet.contains(node.getId())) {
                result.add(node);
            }
        }

        return result;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<DialogBaseNodePO> nodePOList = getAllListByBotId(context.getSrcBotId());
        context.getSnapshot().setNodeList(nodePOList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        RobotSnapshotPO snapshot = context.getSnapshot();

        List<StepPO> stepList = context.getSnapshot().getStepList();
        List<DialogBaseNodePO> nodeList = snapshot.getNodeList();

        Map<String, List<DialogBaseNodePO>> stepNodeMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);
        Optional<StepPO> lastMainStepOpt = stepService.getLastMainStep(stepList);

        for (StepPO step : stepList) {
            try {
                if (step.isLlmStep()) {
                    continue;
                }
                boolean isLastMainStep = lastMainStepOpt.isPresent() && lastMainStepOpt.get().equals(step);
                List<DialogBaseNodePO> stepNodeList = stepNodeMap.getOrDefault(step.getId(), Collections.emptyList());
                context.getInvalidMsgList().addAll(hangupAnswerAndActionValidateService.validateNode(step, stepNodeList));
                Map<String, List<String>> msgMap = validWholeStep(step, isLastMainStep, stepNodeList,
                        context.getDependentResource(),
                        context.getActionNameResource(), context.getValidateConfig());
               if (MapUtils.isNotEmpty(msgMap)) {
                   Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(stepNodeList, DialogBaseNodePO::getId);
                   msgMap.forEach((nodeId, msgList) -> {
                       DialogBaseNodePO node = nodeMap.get(nodeId);
                       SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                               .builder()
                               .resourceType(BotResourceTypeEnum.NODE)
                               .resourceId(step.getId())
                               .failMsg(String.format("%s", String.join(";", msgList)))
                               .resourceName(step.getName())
                               .resourceLabel(step.getLabel())
                               .nodeName(node.getName())
                               .nodeLabel(node.getLabel())
                               .nodeId(nodeId)
                               .build();
                       context.getInvalidMsgList().add(msg);
                   });
               }

            } catch (ComException e) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.NODE)
                        .resourceId(step.getId())
                        .failMsg(e.getDetailMsg())
                        .resourceName(step.getName())
                        .resourceLabel(step.getLabel())
                        .build();
                if (e.getData() instanceof DialogBaseNodePO) {
                    DialogBaseNodePO node = (DialogBaseNodePO) e.getData();
                    msg.setNodeId(node.getId());
                    msg.setNodeName(node.getName());
                    msg.setNodeLabel(node.getLabel());
                }
                context.getInvalidMsgList().add(msg);
            }
        }
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long targetBotId = context.getTargetBotId();
        Long currentUserId = context.getCurrentUserId();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        List<DialogBaseNodePO> nodeList = context.getSnapshot().getNodeList();

        if (context.isCopy()) {
            Map<Integer, String> intentLevelCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(context.getIntentLevelTagId());
            Map<String, String> nodeIdMapping = mapping.getNodeIdMapping();
            nodeList.forEach(nodePO -> {
                String oldId = nodePO.getId();
                String newId = new ObjectId().toString();
                nodeIdMapping.put(oldId, newId);
            });

            // 节点的id依赖目前只有节点内部存在, 暂时节点赋值内部自己解决
            for (DialogBaseNodePO node : nodeList) {
                onCopyNode(context, targetBotId, currentUserId, mapping, intentLevelCode2NameMap, node);
            }
            mongoTemplate.insert(nodeList, DialogBaseNodePO.COLLECTION_NAME);
        }
    }

    private void onCopyNode(RobotResourceContext context,
                            Long targetBotId,
                            Long currentUserId,
                            ResourceCopyReferenceMappingBO mapping,
                            Map<Integer, String> intentLevelCode2NameMap,
                            DialogBaseNodePO node) {

        node.setId(mapping.nodeIdMapping.get(node.getId()));
        node.setBotId(targetBotId);
        node.setStepId(mapping.stepIdMapping.get(node.getStepId()));
        node.setCreateTime(LocalDateTime.now());
        node.setUpdateTime(LocalDateTime.now());
        node.setCreateUserId(currentUserId);
        node.setUpdateUserId(currentUserId);

        //更新动作id
        if (context.isCopyFromCommonBotToMagicTemplateBot()) {
            // 旗舰版BOT复制到模板BOT要删除所有动作配置
            node.setIsEnableAction(false);
            node.setActionList(Collections.emptyList());
        } else {
            if (node.getIsEnableAction() != null && node.getIsEnableAction()
                    && CollectionUtils.isNotEmpty(node.getActionList())) {
                if (SystemEnum.isOPE(context.getSystemEnum())) {
                    node.getActionList().forEach(action -> action.setSourceIdList(null));
                }
            }
        }

        mapping.mappingMismatch(node);
        mapping.mappingUninterrupted(node);

        if (node instanceof DialogChatNodePO) {
            DialogChatNodePO chatNode = (DialogChatNodePO) node;
            if (MapUtils.isNotEmpty(chatNode.getIntentRelatedNodeMap())) {
                Map<String, String> newIntentRelatedNodeMap = new HashMap<>(chatNode.getIntentRelatedNodeMap().size());
                chatNode.getIntentRelatedNodeMap().forEach((oldIntentId, oldNodeId) -> {
                    //todo 数据异常的处理策略
                    newIntentRelatedNodeMap.put(
                            mapping.intentIdMapping.getOrDefault(oldIntentId, oldIntentId),
                            mapping.nodeIdMapping.getOrDefault(oldNodeId, oldNodeId)
                    );
                });
                chatNode.setIntentRelatedNodeMap(newIntentRelatedNodeMap);
            }
            List<String> selectIntentIdList = chatNode.getSelectIntentIdList();
            if (CollectionUtils.isNotEmpty(selectIntentIdList)) {
                List<String> newList = selectIntentIdList.stream()
                        .map(mapping.intentIdMapping::get)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                // 添加用户无应答和兜底意图
                if (selectIntentIdList.contains(ApplicationConstant.USER_SILENCE_INTENT_ID)) {
                    newList.add(0, ApplicationConstant.USER_SILENCE_INTENT_ID);
                }
                if (selectIntentIdList.contains(ApplicationConstant.DEFAULT_INTENT_ID)) {
                    newList.add(0, ApplicationConstant.DEFAULT_INTENT_ID);
                }
                if (selectIntentIdList.contains(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID)) {
                    newList.add(0, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
                }
                if (selectIntentIdList.contains(ApplicationConstant.COLLECT_FAILED_INTENT_ID)) {
                    newList.add(0, ApplicationConstant.COLLECT_FAILED_INTENT_ID);
                }
                chatNode.setSelectIntentIdList(newList);
            }
            if (CollectionUtils.isNotEmpty(chatNode.getTriggerWaitIntentIdList())) {
                chatNode.setTriggerWaitIntentIdList(chatNode.getTriggerWaitIntentIdList().stream()
                        .map(item -> mapping.getIntentIdMapping().getOrDefault(item, item))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()));
            }
        } else if (node instanceof DialogJumpNodePO) {
            DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
            if (JumpTypeEnum.SPECIFIED_STEP.equals(jumpNode.getJumpType())) {
                String newStepId = mapping.stepIdMapping.get(jumpNode.getJumpStepId());
                if (StringUtils.isNotBlank(newStepId)) {
                    jumpNode.setJumpStepId(newStepId);
                }
            }
        } else if (node instanceof DialogJudgeNodePO) {
            DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) node;
            if (MapUtils.isNotEmpty(judgeNode.getBranchRelatedNodeMap())) {
                Map<String, String> newBranchRelatedNodeMap = new HashMap<>(judgeNode.getBranchRelatedNodeMap().size());
                judgeNode.getBranchRelatedNodeMap().forEach((oldBranchId, oldNodeId) -> {
                    newBranchRelatedNodeMap.put(oldBranchId, mapping.nodeIdMapping.getOrDefault(oldNodeId, oldNodeId));
                });
                judgeNode.setBranchRelatedNodeMap(newBranchRelatedNodeMap);
            }
            if (CollectionUtils.isNotEmpty(judgeNode.getBranchList())) {
                judgeNode.getBranchList().forEach(mapping::mapConditionBranch);
            }
        }

        if (node instanceof DialogCollectNodePO) {
            DialogCollectNodePO collectNode = (DialogCollectNodePO) node;

            if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                collectNode.getEntityCollectList().forEach(item -> {
                    item.setEntityId(mapping.getEntityIdMapping().getOrDefault(item.getEntityId(), item.getEntityId()));
                    item.setVariableId(mapping.getVariableIdMapping().getOrDefault(item.getVariableId(), item.getVariableId()));
                });
            }

            if (BooleanUtils.isTrue(collectNode.getEnableSkipCondition()) && CollectionUtils.isNotEmpty(collectNode.getSkipConditionList())) {
                collectNode.getSkipConditionList().forEach(condition -> {
                    if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                        condition.setIntentIdList(condition.getIntentIdList().stream()
                                .map(mapping.getIntentIdMapping()::get)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList()));
                    }
                });
            }
        }

        if (node instanceof DialogQueryNodePO) {
            DialogQueryNodePO queryNode = (DialogQueryNodePO) node;
            IdMappingUtils.mappingQueryNodeHttpInfo(queryNode, mapping.getVariableIdMapping());
        }

        // 更新按键采集节点中的动态变量id
        if (node instanceof DialogKeyCaptureNodePO) {
            DialogKeyCaptureNodePO keyCaptureNode = (DialogKeyCaptureNodePO) node;
            keyCaptureNode.setResultVarId(mapping.mapVarId(keyCaptureNode.getResultVarId()));
        }

        // 更新答案列表关联的变量id
        if (CollectionUtils.isNotEmpty(node.getAnswerList())){
            for (NodeAnswer nodeAnswer : node.getAnswerList()) {
                mapping.mapConditionGroupVarId(nodeAnswer);
            }
        }

        if (BooleanUtils.isNotTrue(node.getEnableIntentLevel())) {
            node.setIntentLevelDetailCode(null);
        } else {
            if (!intentLevelCode2NameMap.containsKey(node.getIntentLevelDetailCode())) {
                node.setIntentLevelDetailCode(null);
            }
        }

        // 更新动态变量赋值
        if (BooleanUtils.isTrue(node.getEnableAssign())) {
            mapping.mapAssignConfig(node.getConstantAssign(), node.getEntityAssign(), node.getOriginInputAssign());
        } else {
            node.setEntityAssign(null);
            node.setConstantAssign(null);
            node.setOriginInputAssign(null);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(StepServiceImpl.class, IntentServiceImpl.class, KnowledgeServiceImpl.class,
                VariableServiceImpl.class, SpecialAnswerConfigServiceImpl.class, EntityServiceImpl.class);
    }

    @Override
    public void updateDependVariableName(Long botId, String variableId, String oldVariableName, String newVariableName) {
        List<DialogBaseNodePO> nodeList = getAllListByBotId(botId);

        if (CollectionUtils.isNotEmpty(nodeList)) {
            List<DialogBaseNodePO> updateList = new ArrayList<>(nodeList.size());
            nodeList.forEach(node -> {
                boolean updated = false;
                for (NodeAnswer answer : node.getAnswerList()) {
                    if (answer.updateVariableName(oldVariableName, newVariableName)) {
                        updated = true;
                    }
                }
                if (updated) {
                    updateList.add(node);
                }
            });

            updateList.forEach(node -> {
                mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
            });
        }
    }

    private void generateLabel(Long botId, StepPO step, List<? extends DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        if (StringUtils.isBlank(step.getLabel())) {
            if (StepTypeEnum.MAIN.equals(step.getType())) {
                labelGenerateService.mainStepLabel(botId, Collections.singletonList(step));
            } else {
                labelGenerateService.independentStepLabel(botId, Collections.singletonList(step));
            }
            stepService.updateStepLabel(step.getBotId(), step.getId(), step.getLabel());
        }

        labelGenerateService.nodeLabel(botId, step.getLabel(), nodeList);
        // 为答案生成标签
        List<NodeAnswer> answerList = nodeList.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getAnswerList()))
                .flatMap(item -> item.getAnswerList().stream())
                .collect(Collectors.toList());
        generateAnswerLabel(botId, answerList);
    }

    private void generateAnswerLabel(Long botId, List<NodeAnswer> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return;
        }
        labelGenerateService.answerLabel(botId, answerList);
    }

    @Override
    public boolean containsSwitchToHumanServiceNode(Long dialogFlowId) {
        if (Objects.isNull(dialogFlowId)) {
            return false;
        }
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return false;
        }

        return containsSwitchToHumanServiceNodeByBotId(botId);
    }

    @Override
    public boolean containsSwitchToHumanServiceNodeByBotId(Long botId) {
        return mongoTemplate.exists(
                Query.query(Criteria.where("type").is(NodeTypeEnum.JUMP).and("botId").is(botId).and("jumpType").is(JumpTypeEnum.HUMAN_SERVICE)),
                DialogBaseNodePO.COLLECTION_NAME)
                || mongoTemplate.exists(
                Query.query(Criteria.where("answerList.postAction").is(PostActionTypeEnum.HUMAN_SERVICE).and("botId").is(botId)),
                KnowledgePO.COLLECTION_NAME)
                || mongoTemplate.exists(
                Query.query(Criteria.where("answerList.postAction").is(PostActionTypeEnum.HUMAN_SERVICE).and("botId").is(botId)),
                SpecialAnswerConfigPO.COLLECTION_NAME);
    }

    @Override
    public List<SimpleNode> getAllSimpleNodeListByBotId(Long botId) {
        List<DialogBaseNodePO> allNodeList = getAllListByBotId(botId);

        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        List<AnswerAudioMappingPO> answerAudioMappingList = answerAudioMappingService.getAllByBotId(botId, botAudioConfig.getRecordUserId(), botAudioConfig.getAudioType());
        Map<String, AnswerAudioMappingPO> textAudioMap = MyCollectionUtils.listToMap(answerAudioMappingList, AnswerAudioMappingPO::getText);

        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        Map<String, String> intentIdNameMap = new HashMap<>(MyCollectionUtils.listToConvertMap(intentList, IntentPO::getId, IntentPO::getName));
        intentIdNameMap.put(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.DEFAULT_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.USER_SILENCE_INTENT_ID, ApplicationConstant.USER_SILENCE_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        return allNodeList.stream()
                .map(node -> {
                    SimpleNode simpleNode = new SimpleNode();
                    simpleNode.setId(node.getId());
                    simpleNode.setLabel(node.getLabel());
                    simpleNode.setName(node.getName());
                    simpleNode.setStepId(node.getStepId());
                    simpleNode.setType(node.getType().getDesc());
                    simpleNode.setBotId(botId);

                    List<NodeLinkInfo> nodeLinkList = new ArrayList<>();
                    if (node instanceof DialogChatNodePO) {
                        DialogChatNodePO chatNode = (DialogChatNodePO) node;
                        for (String intentId : chatNode.getSelectIntentIdList()) {
                            String nextNodeId = chatNode.getRelatedNodeMap().get(intentId);
                            NodeLinkInfo nodeLink = new NodeLinkInfo();
                            nodeLink.setLinkName(intentIdNameMap.getOrDefault(intentId, intentId));
                            nodeLink.setNextNodeId(nextNodeId);
                            nodeLinkList.add(nodeLink);
                        }
                    } else if (node instanceof DialogJudgeNodePO) {
                        DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) node;
                        if (CollectionUtils.isNotEmpty(judgeNode.getBranchList())) {
                            for (NodeConditionBranchPO branch : judgeNode.getBranchList()) {
                                String nextNodeId = judgeNode.getRelatedNodeMap().get(branch.getId());
                                NodeLinkInfo nodeLink = new NodeLinkInfo();
                                nodeLink.setLinkName(branch.getName());
                                nodeLink.setNextNodeId(nextNodeId);
                                nodeLinkList.add(nodeLink);
                            }
                        }
                    } else if (node instanceof DialogJumpNodePO) {
                        DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
                        simpleNode.setNodeLinkList(Collections.emptyList());
                        simpleNode.setJumpType(jumpNode.getJumpType().getDesc());
                    }

                    simpleNode.setNodeLinkList(nodeLinkList);

                    List<AnswerAudioDetail> answerList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                        for (int i = 0; i < node.getAnswerList().size(); i++) {
                            NodeAnswer nodeAnswer = node.getAnswerList().get(i);
                            AnswerAudioDetail answerAudioDetail = new AnswerAudioDetail();
                            answerAudioDetail.setIndex(i);
                            answerAudioDetail.setLabel(nodeAnswer.getLabel());
                            answerAudioDetail.setText(nodeAnswer.getText());
                            AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(nodeAnswer.getText(), AudioTypeEnum.MIXTURE.equals(botAudioConfig.getAudioType()));
                            List<AnswerAudioElement> answerAudioElementList =  splitter.getTextPlaceholderList().stream()
                                    .map(item -> {
                                        AnswerAudioElement answerAudioElement = MyBeanUtils.copy(item, AnswerAudioElement.class);
                                        if (TextPlaceholderTypeEnum.TEXT.equals(item.getType())) {
                                            AnswerAudioMappingPO audioMapping = textAudioMap.get(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(item.getValue()));
                                            if (Objects.nonNull(audioMapping)) {
                                                answerAudioElement.setAudioKey(audioMapping.getUrl());
                                                answerAudioElement.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(audioMapping.getUrl()));
                                            }
                                        }
                                        return answerAudioElement;
                                    }).collect(Collectors.toList());
                            answerAudioDetail.setAnswerElementList(answerAudioElementList);
                            answerList.add(answerAudioDetail);
                        }
                    }

                    simpleNode.setAnswerList(answerList);
                    return simpleNode;
                }).collect(Collectors.toList());
    }

    @Override
    public void updateBySimpleNode(SimpleNode node, Long userId) {
        // 更新节点
        // 来源为开放平台那边
        if (Objects.isNull(node)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点信息不能为空");
        }
        if (StringUtils.isBlank(node.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点id不能为空");
        }
        if (Objects.isNull(node.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (StringUtils.isBlank(node.getStepId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "stepId不能为空");
        }
        // 仅支持更新节点答案信息
        DialogBaseNodePO originNode = getById(node.getBotId(), node.getStepId(), node.getId());
        if (Objects.isNull(originNode)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "节点不存在");
        }
        if (CollectionUtils.isEmpty(node.getAnswerList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点答案列表不能为空");
        }
        if (CollectionUtils.size(node.getAnswerList()) != CollectionUtils.size(originNode.getAnswerList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不支持新增或删除答案");
        }
        List<Tuple2<String, String>> answerChangeList = new ArrayList<>();

        for (int i = 0; i < originNode.getAnswerList().size(); i++) {
            NodeAnswer answer = originNode.getAnswerList().get(i);
            AnswerAudioDetail answerAudioDetail = node.getAnswerList().get(i);
            if (StringUtils.isBlank(answerAudioDetail.getText())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案文本不能为空");
            }
            if (!StringUtils.equals(answerAudioDetail.getText(), answer.getText())) {
                answerChangeList.add(Tuple.of(answer.getText(), answerAudioDetail.getText()));
                answer.setText(answerAudioDetail.getText());
            }
        }

        if (CollectionUtils.isNotEmpty(answerChangeList)) {
            updateAnswerList(originNode);
            botService.updateAuditStatus(node.getBotId(), AuditStatusEnum.DRAFT);
            // 添加操作日志
            String changeDetail = answerChangeList.stream()
                    .map(item -> String.format("【%s】修改为【%s】", item._1, item._2)).collect(Collectors.joining("; "));
            String detail = String.format("编辑%s节点【%s:%s】话术%s",
                    originNode.getType().getDesc(), originNode.getLabel(), originNode.getName(), changeDetail);
            operationLogService.save(originNode.getBotId(), OperationLogTypeEnum.STEP, OperationLogResourceTypeEnum.NODE, detail, userId);

            // 判断是否是ai合成音, 并自动提交合成任务
            Set<String> originalTextList = answerChangeList.stream().map(Tuple2::_2).collect(Collectors.toSet());
            ttsJobService.createIfComposeAudioType(node.getBotId(), originalTextList, userId);
        }
    }

    @Override
    public Mono<QueryNodeApiTestResultVO> apiTest(QueryNodeApiTestReqVO req) {
        return QueryNodeApiTestUtils.test(req)
                .map(result -> {
                    QueryNodeApiTestResultVO vo = MyBeanUtils.copy(result, QueryNodeApiTestResultVO.class);
                    Map<String, String> resMap = req.getResMap();
                    Map<String, String> varIdValueMap = vo.getVarIdValueMap();

                    Map<String, String> varIdNameMap = MyCollectionUtils.listToConvertMap(variableService.getListByBotId(req.getBotId()), VariablePO::getId, VariablePO::getName);

                    List<QueryNodeApiTestResultVO.ParseResultVO> parseResultList = new ArrayList<>(varIdValueMap.size());
                    if (MapUtils.isNotEmpty(resMap)) {
                        for (Map.Entry<String, String> entry : resMap.entrySet()) {
                            QueryNodeApiTestResultVO.ParseResultVO parseResultVO = new QueryNodeApiTestResultVO.ParseResultVO();
                            parseResultVO.setVarId(entry.getKey());
                            parseResultVO.setVarName(varIdNameMap.getOrDefault(entry.getKey(), ""));
                            parseResultVO.setJsonPath(entry.getValue());
                            parseResultVO.setValue(varIdValueMap.get(entry.getKey()));
                            parseResultList.add(parseResultVO);
                        }
                    }
                    vo.setParseResultList(parseResultList);
                    return vo;
                })
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, MDC.get(ApplicationConstant.MDC_LOG_ID)));
    }

    @Override
    public void updateAllNodeLabelPrefix(Long botId, String stepId, String oldPrefix, String newPrefix) {
        List<DialogBaseNodePO> nodeList = getListByStepId(botId, stepId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        nodeList.forEach(node -> node.setLabel(node.getLabel().replace(oldPrefix, newPrefix)));
        deleteByStepId(botId, stepId);
        mongoTemplate.insert(nodeList, DialogBaseNodePO.COLLECTION_NAME);
    }


    private DialogBaseNodePO getByBotIdAndLabel(Long botId, String label) {
        return mongoTemplate.findOne(Query.query(Criteria.where("botId").is(botId).and("label").is(label)), DialogBaseNodePO.class, DialogBaseNodePO.COLLECTION_NAME);
    }

    private void autoCreateDependentResource(NodeSyncVO sync, DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Long userId,
                                             List<VariablePO> srcVariableList, List<BaseEntityPO> srcEntityList, List<IntentPO> srcIntentList,
                                             Map<String, String> variableIdMap, Map<String, String> entityIdMap, Map<String, String> intentIdMap) {
        Long srcBotId = srcNode.getBotId();
        Set<String> dependentVariableIdSet = new HashSet<>();
        Set<String> dependentEntityIdSet = new HashSet<>();
        Set<String> dependentIntentIdSet = new HashSet<>();
        if (BooleanUtils.isTrue(sync.getSyncAnswer())) {
            DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(srcBotId).variable());
            dependentVariableIdSet.addAll(srcNode.calDependVariableIdSet(dependentResource));
        }
        if (BooleanUtils.isTrue(sync.getSyncAssign())) {
            if (BooleanUtils.isTrue(srcNode.getEnableAssign())) {
                if (Objects.nonNull(srcNode.getOriginInputAssign())) {
                    dependentVariableIdSet.addAll(srcNode.getOriginInputAssign().getDependVariableIdList());
                    dependentIntentIdSet.addAll(srcNode.getOriginInputAssign().getDependIntentIdList());
                }
                if (Objects.nonNull(srcNode.getEntityAssign())) {
                    dependentIntentIdSet.addAll(srcNode.getEntityAssign().getDependentEntityIdSet());
                    dependentVariableIdSet.addAll(srcNode.getEntityAssign().getDependVariableIdList());
                }
                if (Objects.nonNull(srcNode.getConstantAssign())) {
                    dependentVariableIdSet.addAll(srcNode.getConstantAssign().getDependVariableIdList());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dependentVariableIdSet)) {
            variableIdMap.putAll(
                    variableService.syncDependVariable(
                            srcVariableList.stream().filter(item -> dependentVariableIdSet.contains(item.getId())).collect(Collectors.toList()), targetNode.getBotId(), userId
                    ));
        }
        if (CollectionUtils.isNotEmpty(dependentEntityIdSet)) {
            for (BaseEntityPO entity : srcEntityList.stream().filter(item -> dependentEntityIdSet.contains(item.getId())).collect(Collectors.toList())) {
                BaseEntityPO targetEntity = entityService.singleSync(targetNode.getBotId(), entity, SyncModeEnum.SKIP);
                if (Objects.nonNull(targetEntity)) {
                    entityIdMap.put(entity.getId(), targetEntity.getId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dependentIntentIdSet)) {
            List<IntentPO> dependentIntentList = srcIntentList.stream().filter(item -> dependentIntentIdSet.contains(item.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dependentIntentList)) {
                dependentIntentIdSet.addAll(dependentIntentList.stream().filter(item -> IntentTypeEnum.COMPOSITE.equals(item.getIntentType()) && CollectionUtils.isNotEmpty(item.getCompositeConditionList()))
                        .map(IntentPO::getCompositeConditionList).flatMap(List::stream).map(CompositeIntentCondition::getIntentIdList)
                        .filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet()));
                dependentIntentList = srcIntentList.stream().filter(item -> dependentIntentIdSet.contains(item.getId())).collect(Collectors.toList());
                List<IntentCorpusPO> corpusList = intentCorpusService.findByIntentIdIn(dependentIntentIdSet);
                List<? extends IntentPO> targetIntentList = intentService.singleSync(targetNode.getBotId(), SyncModeEnum.SKIP, userId, DeepCopyUtils.copyList(dependentIntentList), corpusList);
                if (CollectionUtils.isNotEmpty(targetIntentList)) {
                    intentIdMap.putAll(
                            buildIdMapping(MyCollectionUtils.listToMap(dependentIntentList, IntentPO::getId, IntentPO::getName),
                                    MyCollectionUtils.listToMap(targetIntentList, IntentPO::getName, IntentPO::getId))
                    );
                }
            }
        }
    }

    @Override
    public NodeSyncResultVO sync(NodeSyncVO sync, Long userId) {
        sync.setCurrentUserId(userId);
        Long srcBotId = sync.getSrcBotId();
        List<Long> targetBotIdList = sync.getTargetBotIdList();
        Assert.notEmpty(targetBotIdList, "目标Bot不能为空");
        Assert.isTrue(!targetBotIdList.contains(srcBotId), "目标bot不能包含自己");
        Assert.notEmpty(sync.getTargetIdList(), "节点ID不能为空");
        DialogBaseNodePO srcNode = getByIdList(sync.getSrcBotId(), sync.getTargetIdList()).stream().findFirst().orElse(null);
        if (Objects.isNull(srcNode)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不存在");
        }

        BotAudioConfigPO srcBotConfig = BooleanUtils.isTrue(sync.getSyncAudio())
                ? botConfigService.getAudioConfig(srcBotId)
                : null;

        Map<String, String> srcStepIdNameMap = sync.mapStep()
                ? MyCollectionUtils.listToMap(stepService.getAllListByBotId(srcBotId), StepPO::getId, StepPO::getName)
                : Collections.emptyMap();

        Map<String, String> srcKnowledgeIdNameMap = sync.mapKnowledge()
                ? MyCollectionUtils.listToMap(knowledgeService.getAllListByBotId(srcBotId), KnowledgePO::getId, KnowledgePO::getName)
                : Collections.emptyMap();

        Map<String, String> srcSpecialAnswerConfigIdNameMap = sync.mapSpecialAnswerConfig()
                ? MyCollectionUtils.listToMap(specialAnswerConfigService.getByBotId(srcBotId), SpecialAnswerConfigPO::getId, SpecialAnswerConfigPO::getName)
                : Collections.emptyMap();

        List<IntentPO> srcIntentList = intentService.getAllByBotId(srcBotId);
        Map<String, String> srcIntentIdNameMap = sync.mapIntent()
                ? intentService.getIntentIdNameMapWithDefault(srcBotId)
                : Collections.emptyMap();

        List<VariablePO> srcVariableList = Collections.emptyList();
        Map<String, String> srcVariableIdNameMap = sync.mapVariable()
                ? MyCollectionUtils.listToMap(srcVariableList = variableService.getListByBotId(srcBotId), VariablePO::getId, VariablePO::getName)
                : Collections.emptyMap();

        List<BaseEntityPO> srcEntityList = Collections.emptyList();
        Map<String, String> srcEntityIdNameMap = sync.mapEntity()
                ? MyCollectionUtils.listToMap(srcEntityList = entityService.getAllByBotId(srcBotId), BaseEntityPO::getId, BaseEntityPO::getName)
                : Collections.emptyMap();

        NodeSyncResultVO result = new NodeSyncResultVO();
        Map<Long, String> failBotMap = new HashMap<>();

        for (Long targetBotId : targetBotIdList) {
            try {
                DialogBaseNodePO oldNode = getByBotIdAndLabel(targetBotId, srcNode.getLabel());
                if (Objects.isNull(oldNode)) {
                    failBotMap.put(targetBotId, "目标节点不存在");
                    continue;
                }
                if (!Objects.equals(oldNode.getType(), srcNode.getType())) {
                    failBotMap.put(targetBotId, "目标节点类型不匹配");
                    continue;
                }

                DialogBaseNodePO targetNode = DeepCopyUtils.copyObject(oldNode);

                Map<String, String> stepIdMap = sync.mapStep()
                        ? buildIdMapping(srcStepIdNameMap, MyCollectionUtils.listToMap(stepService.getAllListByBotId(targetBotId), StepPO::getName, StepPO::getId))
                        : Collections.emptyMap();

                Map<String, String> knowledgeIdMap = sync.mapKnowledge()
                        ? buildIdMapping(srcKnowledgeIdNameMap, MyCollectionUtils.listToMap(knowledgeService.getAllListByBotId(targetBotId), KnowledgePO::getName, KnowledgePO::getId))
                        : Collections.emptyMap();

                Map<String, String> specialAnswerConfigIdMap = sync.mapSpecialAnswerConfig()
                        ? buildIdMapping(srcSpecialAnswerConfigIdNameMap, MyCollectionUtils.listToMap(specialAnswerConfigService.getByBotId(targetBotId), SpecialAnswerConfigPO::getName, SpecialAnswerConfigPO::getId))
                        : Collections.emptyMap();

                Map<String, String> intentIdMap = sync.mapIntent()
                        ? buildIdMapping(srcIntentIdNameMap, intentService.getIntentIdNameMapWithDefault(targetBotId).entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v1)))
                        : Collections.emptyMap();

                Map<String, String> variableIdMap = sync.mapVariable()
                        ? buildIdMapping(srcVariableIdNameMap, MyCollectionUtils.listToMap(variableService.getListByBotId(targetBotId), VariablePO::getName, VariablePO::getId))
                        : Collections.emptyMap();

                Map<String, String> entityIdMap = sync.mapEntity()
                        ? buildIdMapping(srcEntityIdNameMap, MyCollectionUtils.listToMap(entityService.getAllByBotId(targetBotId), BaseEntityPO::getName, BaseEntityPO::getId))
                        : Collections.emptyMap();

                // 自动创建变量、实体、意图
                autoCreateDependentResource(sync, srcNode, targetNode, userId, srcVariableList, srcEntityList, srcIntentList, variableIdMap, entityIdMap, intentIdMap);

                // 同步不允许用户打断
                if (BooleanUtils.isTrue(sync.getSyncUninterrupted())) {
                    syncUninterrupted(srcNode, targetNode, stepIdMap, knowledgeIdMap, specialAnswerConfigIdMap, intentIdMap);
                }

                // 同步不关联
                if (BooleanUtils.isTrue(sync.getSyncMismatch())) {
                    syncMismatch(srcNode, targetNode, stepIdMap, knowledgeIdMap, specialAnswerConfigIdMap);
                }

                // 同步客户无应答
                if (BooleanUtils.isTrue(sync.getSyncCustomUserSilence())) {
                    syncCustomUserSilence(srcNode, targetNode);
                }

                // 同步AI应答时长
                if (BooleanUtils.isTrue(sync.getSyncWaitUserSayFinish())) {
                    syncWaitUserSayFinish(srcNode, targetNode, intentIdMap);
                }

                // 同步意向分类
                if (BooleanUtils.isTrue(sync.getSyncIntentLevel())) {
                    syncIntentLevel(srcNode, targetNode);
                }

                // 同步返回时执行跳转原主动流程逻辑
                if (BooleanUtils.isTrue(sync.getSyncPullback())) {
                    syncPullback(srcNode, targetNode);
                }

                // 同步返回时语音播报设置
                if (BooleanUtils.isTrue(sync.getSyncCustomReplay())) {
                    syncCustomReplay(srcNode, targetNode);
                }

                // 同步动作配置
                if (BooleanUtils.isTrue(sync.getSyncAction())) {
                    syncAction(srcNode, targetNode, sync.getSyncActionCategoryList());
                }

                // 同步动态变量赋值
                if (BooleanUtils.isTrue(sync.getSyncAssign())) {
                    syncAssign(srcNode, targetNode, variableIdMap, entityIdMap, intentIdMap);
                }

                // 同步跳转至
                if (BooleanUtils.isTrue(sync.getSyncJump())) {
                    syncJump(srcNode, targetNode);
                }

                // 同步答案
                if (BooleanUtils.isTrue(sync.getSyncAnswer())) {
                    syncAnswer(srcNode, targetNode, variableIdMap);
                }

                // 同步节点名称
                if (BooleanUtils.isTrue(sync.getSyncNodeName())) {
                    targetNode.setName(srcNode.getName());
                }

                mongoTemplate.save(targetNode, DialogBaseNodePO.COLLECTION_NAME);
                sourceRefService.deleteSourceByRefIds(targetBotId, Collections.singletonList(targetNode.getId()));
                DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(targetBotId).variable());
                addSourceRef(Collections.singletonList(targetNode), dependentResource);

                // 同步音频
                if (BooleanUtils.isTrue(sync.getSyncAudio())) {
                    syncAudio(srcNode, targetNode, srcBotConfig, userId);
                }
            } catch (Exception e) {
                failBotMap.put(targetBotId, e.getMessage());
            }
        }
        botSyncOperationLogService.nodeSync(srcNode, sync, ListUtils.subtract(targetBotIdList, new ArrayList<>(failBotMap.keySet())), new ArrayList<>(failBotMap.keySet()));
        result.setFailBotMap(failBotMap);
        result.setFailNum(failBotMap.size());
        result.setSuccessNum(targetBotIdList.size() - failBotMap.size());
        return result;
    }

    @Override
    public void updateQueryNodeOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        // 查询所有的查询节点, 判断查询节点中的 body 是否引用了变量
        List<DialogBaseNodePO> nodeList = getAllListByBotId(botId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }

        List<DialogBaseNodePO> updateList = new ArrayList<>();

        for (DialogBaseNodePO node : nodeList) {
            if (node instanceof DialogQueryNodePO) {
                DialogQueryNodePO queryNode = (DialogQueryNodePO) node;
                if (StringUtils.isNotBlank(queryNode.getBody())) {
                    String body = queryNode.getBody();
                    String newBody = AnswerTextUtils.convertTemplateOnVariableRename(body, oldVariableName, newVariableName);
                    if (!body.equals(newBody)) {
                        queryNode.setBody(newBody);
                        queryNode.setUpdateUserId(userId);
                        queryNode.setUpdateTime(LocalDateTime.now());
                        updateList.add(queryNode);
                        log.debug("更新查询节点{} oldBody: {}, newBody: {}", queryNode.getLabel(), body, queryNode.getBody());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(node -> {
                mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
            });
        }

    }

    private void syncAssign(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Map<String, String> variableIdMap, Map<String, String> entityIdMap, Map<String, String> intentIdMap) {
        targetNode.setEnableAssign(srcNode.getEnableAssign());
        if (BooleanUtils.isNotTrue(srcNode.getEnableAssign())) {
            targetNode.setEntityAssign(null);
            targetNode.setConstantAssign(null);
            targetNode.setOriginInputAssign(null);
        } else {
            targetNode.setEntityAssign(srcNode.getEntityAssign());
            targetNode.setConstantAssign(srcNode.getConstantAssign());
            targetNode.setOriginInputAssign(srcNode.getOriginInputAssign());
            ResourceCopyReferenceMappingBO mapping = new ResourceCopyReferenceMappingBO();
            mapping.getVariableIdMapping().putAll(variableIdMap);
            mapping.getEntityIdMapping().putAll(entityIdMap);
            mapping.getIntentIdMapping().putAll(intentIdMap);
            mapping.mapAssignConfig(targetNode.getConstantAssign(), targetNode.getEntityAssign(), targetNode.getOriginInputAssign());
        }
    }

    private void syncAction(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, List<ActionCategoryEnum> syncActionCategoryList) {
        targetNode.setIsEnableAction(srcNode.getIsEnableAction());
        if (BooleanUtils.isNotTrue(srcNode.getIsEnableAction())) {
            targetNode.setActionList(Collections.emptyList());
            return;
        }
        if (CollectionUtils.isNotEmpty(syncActionCategoryList)) {
            Long srcTenantId = botRefService.getTenantIdByBotId(srcNode.getBotId());
            Long targetTenantId = botRefService.getTenantIdByBotId(targetNode.getBotId());
            boolean isSameTenant = Objects.equals(srcTenantId, targetTenantId);
            Map<ActionCategoryEnum, RuleActionParam> srcAntionMap = MyCollectionUtils.listToMap(srcNode.getActionList(), RuleActionParam::getActionType);
            Map<ActionCategoryEnum, RuleActionParam> targetAntionMap = new HashMap<>(MyCollectionUtils.listToMap(targetNode.getActionList(), RuleActionParam::getActionType));
            syncActionCategoryList.forEach(category -> {
                RuleActionParam ruleActionParam = srcAntionMap.get(category);
                if (Objects.nonNull(ruleActionParam) && !isSameTenant) {
                    ruleActionParam.setSourceIdList(Collections.emptyList());
                }
                targetAntionMap.put(category, ruleActionParam);
            });
            targetNode.setActionList(targetAntionMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
    }

    private void syncCustomReplay(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode) {
        if (!(srcNode instanceof DialogChatNodePO) || !(targetNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO srcChatNode = (DialogChatNodePO) srcNode;
        DialogChatNodePO targetChatNode = (DialogChatNodePO) targetNode;
        targetChatNode.setEnableCustomReplay(srcChatNode.getEnableCustomReplay());
        targetChatNode.setCustomReplayThreshold(srcChatNode.getCustomReplayThreshold());
        targetChatNode.setExceedThresholdReplayStrategy(srcChatNode.getExceedThresholdReplayStrategy());
    }

    private void syncPullback(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode) {
        if (!(srcNode instanceof DialogChatNodePO) || !(targetNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO srcChatNode = (DialogChatNodePO) srcNode;
        DialogChatNodePO targetChatNode = (DialogChatNodePO) targetNode;
        targetChatNode.setEnablePullback(srcChatNode.getEnablePullback());
    }

    private void syncIntentLevel(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode) {
        targetNode.setEnableIntentLevel(srcNode.getEnableIntentLevel());
        targetNode.setIntentLevelDetailCode(null);
        BotPO targetBot = botService.getById(targetNode.getBotId());
        if (Objects.nonNull(targetBot)) {
            Map<Integer, String> intentLevelTagDetailCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(targetBot.getIntentLevelTagId());
            if (intentLevelTagDetailCode2NameMap.containsKey(srcNode.getIntentLevelDetailCode())) {
                targetNode.setIntentLevelDetailCode(srcNode.getIntentLevelDetailCode());
            }
        }
    }

    private void syncWaitUserSayFinish(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Map<String, String> intentIdMap) {
        if (!(srcNode instanceof DialogChatNodePO) || !(targetNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO srcChatNode = (DialogChatNodePO) srcNode;
        DialogChatNodePO targetChatNode = (DialogChatNodePO) targetNode;
        targetChatNode.setEnableWaitUserSayFinish(srcChatNode.getEnableWaitUserSayFinish());
        targetChatNode.setWaitUserSayFinishMs(srcChatNode.getWaitUserSayFinishMs());
        if (BooleanUtils.isTrue(srcChatNode.getEnableWaitUserSayFinish()) && CollectionUtils.isNotEmpty(srcChatNode.getTriggerWaitIntentIdList())) {
            targetChatNode.setTriggerWaitIntentIdList(srcChatNode.getTriggerWaitIntentIdList().stream().map(intentIdMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
        } else {
            targetChatNode.setTriggerWaitIntentIdList(Collections.emptyList());
        }
    }

    private void syncAudio(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, BotAudioConfigPO srcBotConfig, Long userId) {
        List<NodeAnswer> answerList = srcNode.getAnswerList();
        Long targetBotId = targetNode.getBotId();
        if (CollectionUtils.isNotEmpty(answerList)) {
            Set<String> answerSet = answerList.stream().map(NodeAnswer::getText).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(answerSet)) {
                BotAudioConfigPO targetBotConfig = botConfigService.getAudioConfig(targetBotId);
                if (Objects.nonNull(srcBotConfig) && Objects.nonNull(targetBotConfig)
                        && !AudioTypeEnum.COMPOSE.equals(srcBotConfig.getAudioType()) && !AudioTypeEnum.COMPOSE.equals(targetBotConfig.getAudioType())
                        && Objects.equals(srcBotConfig.getRecordUserId(), targetBotConfig.getRecordUserId())) {
                    answerAudioMappingService.copyAllOnCopyBot(srcNode.getBotId(), srcBotConfig.getRecordUserId(), targetBotId, targetBotConfig.getRecordUserId(), answerSet, userId, SyncModeEnum.COVER);
                }
            }
        }
    }

    private void syncJump(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode) {
        if (srcNode instanceof DialogJumpNodePO && targetNode instanceof DialogJumpNodePO) {
            DialogJumpNodePO srcJumpNode = (DialogJumpNodePO) srcNode;
            DialogJumpNodePO targetJumpNode = (DialogJumpNodePO) targetNode;
            targetJumpNode.setJumpType(srcJumpNode.getJumpType());
            targetJumpNode.setJumpStepId(null);
            if (JumpTypeEnum.SPECIFIED_STEP.equals(srcJumpNode.getJumpType())) {
                String srcJumpStepId = srcJumpNode.getJumpStepId();
                StepVO srcJumpStep = stepService.getById(srcNode.getBotId(), srcJumpStepId);
                if (Objects.nonNull(srcJumpStep)) {
                    String srcJumpStepName = srcJumpStep.getName();
                    String targetJumpStepId = stepService.getIdNamePairByBotId(targetNode.getBotId()).stream().filter(pair -> srcJumpStepName.equals(pair.getName())).map(IdNamePair::getId).findFirst().orElse(null);
                    targetJumpNode.setJumpStepId(targetJumpStepId);
                }
            }
        }
    }

    private void syncAnswer(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Map<String, String> varIdMap) {
        Long srcBotId = srcNode.getBotId();
        List<NodeAnswer> answerList = srcNode.getAnswerList();
        if (CollectionUtils.isEmpty(answerList)) {
            targetNode.setAnswerList(null);
            return;
        }
        DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(srcBotId).variable());
        Set<String> dependendsVarIdSet = answerList.stream().map(answer -> answer.calDependsVariableIdSet(dependentResource)).flatMap(Set::stream).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(dependendsVarIdSet)) {
            ResourceCopyReferenceMappingBO mapping = new ResourceCopyReferenceMappingBO();
            mapping.getVariableIdMapping().putAll(varIdMap);
            for (NodeAnswer answer : answerList) {
                if (BooleanUtils.isTrue(answer.getEnableVarCondition()) && CollectionUtils.isNotEmpty(answer.getConditionList())) {
                    mapping.mapConditionGroupVarId(answer);
                }
            }
        }
        targetNode.setAnswerList(answerList);
    }

    private void syncCustomUserSilence(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode) {
        if (!(srcNode instanceof DialogChatNodePO) || !(targetNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO srcChatNode = (DialogChatNodePO) srcNode;
        DialogChatNodePO targetChatNode = (DialogChatNodePO) targetNode;
        targetChatNode.setEnableCustomUserSilence(srcChatNode.getEnableCustomUserSilence());
        targetChatNode.setCustomUserSilenceSecond(srcChatNode.getCustomUserSilenceSecond());
    }

    private void syncMismatch(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Map<String, String> stepIdMap, Map<String, String> knowledgeIdMap,
                              Map<String, String> specialAnswerConfigIdMap) {
        if (BooleanUtils.isNotTrue(srcNode.getMismatchKnowledgeAndStep())) {
            targetNode.setMismatchKnowledgeAndStep(false);
            targetNode.setMismatchAllKnowledge(false);
            targetNode.setMismatchKnowledgeIdList(Collections.emptyList());
            targetNode.setMismatchAllStep(false);
            targetNode.setMismatchStepIdList(Collections.emptyList());
            targetNode.setMismatchAllSpecialAnswerConfig(false);
            targetNode.setMismatchSpecialAnswerConfigIdList(Collections.emptyList());
        } else {
            targetNode.setMismatchKnowledgeAndStep(true);
            if (BooleanUtils.isTrue(srcNode.getMismatchAllStep())) {
                targetNode.setMismatchAllStep(true);
            } else if (CollectionUtils.isNotEmpty(srcNode.getMismatchStepIdList())) {
                targetNode.setMismatchAllStep(false);
                targetNode.setMismatchStepIdList(srcNode.getMismatchStepIdList());
            }
            if (BooleanUtils.isTrue(srcNode.getMismatchAllKnowledge())) {
                targetNode.setMismatchAllKnowledge(true);
            } else if (CollectionUtils.isNotEmpty(srcNode.getMismatchKnowledgeIdList())) {
                targetNode.setMismatchAllKnowledge(false);
                targetNode.setMismatchKnowledgeIdList(srcNode.getMismatchKnowledgeIdList());
            }
            if (BooleanUtils.isTrue(srcNode.getMismatchAllSpecialAnswerConfig())) {
                targetNode.setMismatchAllSpecialAnswerConfig(true);
            } else if (CollectionUtils.isNotEmpty(srcNode.getMismatchSpecialAnswerConfigIdList())) {
                targetNode.setMismatchAllSpecialAnswerConfig(false);
                targetNode.setMismatchSpecialAnswerConfigIdList(srcNode.getMismatchSpecialAnswerConfigIdList());
            }
            targetNode.mapMismatch(stepIdMap, knowledgeIdMap, specialAnswerConfigIdMap);
        }
    }

    private void syncUninterrupted(DialogBaseNodePO srcNode, DialogBaseNodePO targetNode, Map<String, String> stepIdMap, Map<String, String> knowledgeIdMap,
                                   Map<String, String> specialAnswerConfigIdMap, Map<String, String> intentIdMap) {
        if (BooleanUtils.isNotTrue(srcNode.getEnableUninterrupted())) {
            targetNode.setEnableUninterrupted(false);
            targetNode.setUninterruptedReplyStepIdList(Collections.emptyList());
            targetNode.setUninterruptedReplyKnowledgeIdList(Collections.emptyList());
            targetNode.setUninterruptedReplySpecialAnswerIdList(Collections.emptyList());
            targetNode.setUninterruptedReplyBranchIntentIdList(Collections.emptyList());
        } else {
            targetNode.setEnableUninterrupted(true);
            targetNode.setCustomInterruptThreshold(srcNode.getCustomInterruptThreshold());
            targetNode.setUninterruptedReplyStepIdList(srcNode.getUninterruptedReplyStepIdList());
            targetNode.setUninterruptedReplyKnowledgeIdList(srcNode.getUninterruptedReplyKnowledgeIdList());
            targetNode.setUninterruptedReplySpecialAnswerIdList(srcNode.getUninterruptedReplySpecialAnswerIdList());
            targetNode.setUninterruptedReplyBranchIntentIdList(srcNode.getUninterruptedReplyBranchIntentIdList());
            targetNode.mapUninterrupted(stepIdMap, knowledgeIdMap, specialAnswerConfigIdMap, intentIdMap);
        }
    }

    private static <T, R> Map<T, T> buildIdMapping(Map<T, R> oldMap, Map<R, T> newMap) {
        if (MapUtils.isEmpty(oldMap) || MapUtils.isEmpty(newMap)) {
            return Collections.emptyMap();
        }
        Map<T, T> resultMap = new HashMap<>();
        oldMap.forEach((k, v) -> Optional.ofNullable(newMap.get(v)).ifPresent(res -> resultMap.put(k, res)));
        return resultMap;
    }
}
