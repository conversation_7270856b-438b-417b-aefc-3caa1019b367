package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.helper.AnswerChangeLogHelper;
import com.yiwise.dialogflow.helper.IntentActionConfigCompareHelper;
import com.yiwise.dialogflow.helper.IntentLevelConfigCompareHelper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DependResourceService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.operationlog.SpecialAnswerOperationLogService;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpecialAnswerOperationLogServiceImpl implements SpecialAnswerOperationLogService {

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private BotService botService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private UninterruptedOperationLogService uninterruptedOperationLogService;

    @Override
    public void compareAndCreateOperationLog(Long botId,
                                             SpecialAnswerConfigPO oldSpecialAnswer,
                                             SpecialAnswerConfigPO newSpecialAnswer,
                                             Long userId) {
        if (Objects.isNull(oldSpecialAnswer) || Objects.isNull(newSpecialAnswer)) {
            return;
        }

        List<String> logDetailList = new ArrayList<>();
        DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable().intent());

        logDetailList.addAll(AnswerChangeLogHelper.compareAnswerChange(oldSpecialAnswer.getAnswerList(), newSpecialAnswer.getAnswerList(), updatePrefix(oldSpecialAnswer), resource));
        BotPO bot = botService.selectByKey(botId);
        Map<Integer, String> intentLevelCode2NameMap = Collections.emptyMap();
        if (Objects.nonNull(bot) && Objects.nonNull(bot.getIntentLevelTagId())) {
            intentLevelCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(bot.getIntentLevelTagId());
        }
        compareIntentLevel(oldSpecialAnswer, newSpecialAnswer, logDetailList, intentLevelCode2NameMap);

        // 触发动作设置
        ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMapByBotId(newSpecialAnswer.getBotId());
        compareActionConfig(oldSpecialAnswer, newSpecialAnswer, logDetailList, actionNameResource);

        // 延迟挂机
        compareDelayHangup(oldSpecialAnswer, newSpecialAnswer, logDetailList);

        compareIntent(oldSpecialAnswer, newSpecialAnswer, logDetailList, resource);

        compareExcludeIntent(oldSpecialAnswer, newSpecialAnswer, logDetailList, resource);

        compareCustomerConcern(oldSpecialAnswer, newSpecialAnswer, logDetailList);

        compareLLMChat(oldSpecialAnswer, newSpecialAnswer, logDetailList, prepareResource(newSpecialAnswer.getBotId()));

        // 人工介入
        compareEnableHumanIntervention(oldSpecialAnswer, newSpecialAnswer, logDetailList);

        compareAssistant(oldSpecialAnswer, newSpecialAnswer, logDetailList);

        save(botId, logDetailList, userId);
    }

    private void compareEnableHumanIntervention(SpecialAnswerConfigPO oldSpecialAnswer, SpecialAnswerConfigPO newSpecialAnswer, List<String> logDetailList) {
        boolean oldEnableHumanIntervention = BooleanUtils.isTrue(oldSpecialAnswer.getEnableHumanIntervention());
        boolean newEnableHumanIntervention = BooleanUtils.isTrue(newSpecialAnswer.getEnableHumanIntervention());
        if (oldEnableHumanIntervention != newEnableHumanIntervention) {
            logDetailList.add(String.format("%s启动人工介入:【%s】修改为【%s】",
                    updatePrefix(oldSpecialAnswer),
                    oldEnableHumanIntervention ? "开启" : "关闭",
                    newEnableHumanIntervention ? "开启" : "关闭"));
        }
    }

    private void compareAssistant(SpecialAnswerConfigPO oldSpecialAnswer, SpecialAnswerConfigPO newSpecialAnswer, List<String> logDetailList) {
        if (!Objects.equals(oldSpecialAnswer.getEnableHighestPriority(), newSpecialAnswer.getEnableHighestPriority())) {
            logDetailList.add(String.format("%s%s优先级置顶", updatePrefix(oldSpecialAnswer), BooleanUtils.isTrue(newSpecialAnswer.getEnableHighestPriority()) ? "开启" : "关闭"));
        }
    }

    private void compareLLMChat(SpecialAnswerConfigPO oldSpecialAnswer,
                                SpecialAnswerConfigPO newSpecialAnswer,
                                List<String> logDetailList,
                                DependentResourceBO resource) {
        if (!SpecialAnswerConfigPO.LLM.equals(oldSpecialAnswer.getName()) || !SpecialAnswerConfigPO.LLM.equals(newSpecialAnswer.getName())) {
            return;
        }

        // 承接话术设置
        if (BooleanUtils.isTrue(oldSpecialAnswer.getEnableGuideAnswer()) != BooleanUtils.isTrue(newSpecialAnswer.getEnableGuideAnswer())) {
            logDetailList.add(String.format("%s【%s】承接话术设置", updatePrefix(oldSpecialAnswer), BooleanUtils.isTrue(newSpecialAnswer.getEnableGuideAnswer()) ? "启用" : "停用"));
        }

        if (!Objects.equals(oldSpecialAnswer.getMinInputLength(), newSpecialAnswer.getMinInputLength())) {
            logDetailList.add(String.format("%s最小输入长度【%s】修改为【%s】", updatePrefix(oldSpecialAnswer), oldSpecialAnswer.getMinInputLength(), newSpecialAnswer.getMinInputLength()));
        }
        if (!StringUtils.equals(oldSpecialAnswer.getRoleDescription(), newSpecialAnswer.getRoleDescription())) {
            logDetailList.add(String.format("%s角色描述【%s】修改为【%s】", updatePrefix(oldSpecialAnswer), oldSpecialAnswer.getRoleDescription(), newSpecialAnswer.getRoleDescription()));
        }
        if (!StringUtils.equals(oldSpecialAnswer.getBackground(), newSpecialAnswer.getBackground())) {
            logDetailList.add(String.format("%s背景信息【%s】修改为【%s】", updatePrefix(oldSpecialAnswer), oldSpecialAnswer.getBackground(), newSpecialAnswer.getBackground()));
        }
        if (!StringUtils.equals(oldSpecialAnswer.getLlmModelName(), newSpecialAnswer.getLlmModelName())) {
            logDetailList.add(String.format("%s模型【%s】修改为【%s】", updatePrefix(oldSpecialAnswer), oldSpecialAnswer.getLlmModelName(), newSpecialAnswer.getLlmModelName()));
        }

        logDetailList.addAll(uninterruptedOperationLogService.compare(updatePrefix(oldSpecialAnswer), oldSpecialAnswer, newSpecialAnswer, resource));
    }

    private DependentResourceBO prepareResource(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        condition.step().variable().knowledge().specialAnswerConfig();
        return dependResourceService.generateByCondition(condition);
    }


    private void compareIntent(SpecialAnswerConfigPO oldItem, SpecialAnswerConfigPO newItem, List<String> logDetailList, DependentResourceBO resource) {
        String oldIntentNameList = "";
        String newIntentNameList = "";
        if (CollectionUtils.isNotEmpty(oldItem.getTriggerIntentIdList())) {
            oldIntentNameList = oldItem.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining(", "));
        }
        if (CollectionUtils.isNotEmpty(newItem.getTriggerIntentIdList())) {
            newIntentNameList = newItem.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining(", "));
        }
        if (!StringUtils.equals(oldIntentNameList, newIntentNameList)) {
            logDetailList.add(String.format("%s关联意图【%s】修改为【%s】",
                    updatePrefix(oldItem), oldIntentNameList, newIntentNameList));
        }
    }
    private void compareExcludeIntent(SpecialAnswerConfigPO oldItem,
                                      SpecialAnswerConfigPO newItem,
                                      List<String> logDetailList,
                                      DependentResourceBO resource) {
        String oldIntentNameList = "";
        String newIntentNameList = "";
        if (CollectionUtils.isNotEmpty(oldItem.getExcludeIntentIdList())) {
            oldIntentNameList = oldItem.getExcludeIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining(", "));
        }
        if (CollectionUtils.isNotEmpty(newItem.getExcludeIntentIdList())) {
            newIntentNameList = newItem.getExcludeIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining(", "));
        }
        if (!StringUtils.equals(oldIntentNameList, newIntentNameList)) {
            logDetailList.add(String.format("%s不关联意图【%s】修改为【%s】",
                    updatePrefix(oldItem), oldIntentNameList, newIntentNameList));
        }
    }

    @Override
    public void compareStatsAndCreateOperationLog(SpecialAnswerConfigPO old, EnabledStatusEnum newStats, Long userId) {
        if (Objects.isNull(old) || Objects.isNull(newStats)) {
            return;
        }

        List<String> logDetailList = new ArrayList<>();
        logDetailList.add(String.format("%s特殊语境【%s:%s】", EnabledStatusEnum.ENABLE.equals(newStats) ? "启用" : "停用", old.getLabel(), old.getName()));
        save(old.getBotId(), logDetailList, userId);
    }

    private void save(Long botId, List<String> logDetailList, Long userId) {
        if (CollectionUtils.isEmpty(logDetailList)) {
            return;
        }
        List<OperationLogDTO> logs = logDetailList.stream()
                .map(detail -> OperationLogDTO.builder()
                        .botId(botId)
                        .operatorId(userId)
                        .detail(detail)
                        .type(OperationLogTypeEnum.BOT_CONFIG)
                        .resourceType(OperationLogResourceTypeEnum.SPECIAL_ANSWER)
                        .build()
                )
                .collect(Collectors.toList());
        operationLogService.batchSave(logs);
    }

    /**
     * 比较延迟挂机设置
     */
    private void compareDelayHangup(SpecialAnswerConfigPO oldSpecialAnswer, SpecialAnswerConfigPO newSpecialAnswer, List<String> logDetailList) {
        if (!oldSpecialAnswer.isHangupDelay() || !newSpecialAnswer.isHangupDelay()) {
            return;
        }

        if (!Objects.equals(oldSpecialAnswer.getHangupDelaySeconds(), newSpecialAnswer.getHangupDelaySeconds())) {
            logDetailList.add(String.format("%s延迟挂机【%s】修改为【%s】", updatePrefix(oldSpecialAnswer), oldSpecialAnswer.getHangupDelaySeconds(), newSpecialAnswer.getHangupDelaySeconds()));
        }
    }

    private String updatePrefix(SpecialAnswerConfigPO oldSpecialAnswer) {
        return String.format("编辑特殊语境:原【%s:%s】", oldSpecialAnswer.getLabel(), oldSpecialAnswer.getName());
    }

    private void compareActionConfig(SpecialAnswerConfigPO oldSpecialAnswer, SpecialAnswerConfigPO newSpecialAnswer, List<String> logDetailList, ActionNameResourceBO actionNameResource) {
        logDetailList.addAll(IntentActionConfigCompareHelper.compareIntentActionConfigChangeLog(oldSpecialAnswer, newSpecialAnswer, actionNameResource, updatePrefix(oldSpecialAnswer)));
    }

    private void compareIntentLevel(SpecialAnswerConfigPO oldSpecialAnswer,
                                    SpecialAnswerConfigPO newSpecialAnswer,
                                    List<String> logDetailList,
                                    Map<Integer, String> intentLevelCode2NameMap) {
        String prefix = updatePrefix(oldSpecialAnswer);
        logDetailList.addAll(IntentLevelConfigCompareHelper.compareIntentLevelConfigChangeLog(oldSpecialAnswer, newSpecialAnswer, prefix, intentLevelCode2NameMap));
    }

    private void compareCustomerConcern(SpecialAnswerConfigPO oldSpecialAnswer,
                                    SpecialAnswerConfigPO newSpecialAnswer,
                                    List<String> logDetailList) {
        if (!Objects.equals(oldSpecialAnswer.getIsCustomerConcern(), newSpecialAnswer.getIsCustomerConcern())) {
            logDetailList.add(String.format("%s%s关注点", updatePrefix(oldSpecialAnswer), BooleanUtils.isTrue(newSpecialAnswer.getIsCustomerConcern()) ? "设为" : "取消"));
        }
    }
}
