package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.engine.share.LLMTokenUsageItem;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.LLMTokenStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.LLMTokenStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class LLMTokenStatsServiceImpl implements LLMTokenStatsService {

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private MongoTemplate readMongoTemplate;

    @Override
    public void saveStats(BotStatsAnalysisResult analysisResult) {
        if (Objects.isNull(analysisResult)
                || Objects.isNull(analysisResult.getLlmTokenUsageInfo())
                || Objects.isNull(analysisResult.getLlmTokenUsageInfo().getTotalToken())
                || analysisResult.getLlmTokenUsageInfo().getTotalToken() <= 0) {
            return;
        }
        if (CollectionUtils.isNotEmpty(analysisResult.getLlmTokenUsageInfo().getModelTokenUsageList())) {
            for (LLMTokenUsageItem item : analysisResult.getLlmTokenUsageInfo().getModelTokenUsageList()) {
                if (StringUtils.isNotBlank(item.getModel())
                        && Objects.nonNull(item.getToken())
                        && item.getToken() > 0) {
                    Query query = BotStatsUtil.generateCommonQuery(analysisResult);
                    query.addCriteria(Criteria.where("model").is(item.getModel()));
                    Update update = new Update();
                    update.inc("token", item.getToken());
                    if (Objects.nonNull(item.getPrice())) {
                        update.inc("price", item.getPrice());
                    }
                    update.inc("callCount", 1);
                    callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.LLM_TOKEN_STATS, query, update);
                }
            }
        }
    }

    @Override
    public List<LLMTokenStatsPO> queryAllStepStatsList(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group( "model")
                .sum("callCount").as("callCount")
                .sum("token").as("token")
                .sum("price").as("price")
                .first("model").as("model")
                .first("botId").as("botId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.LLM_TOKEN_STATS, LLMTokenStatsPO.class)
                .getMappedResults();
    }
}
