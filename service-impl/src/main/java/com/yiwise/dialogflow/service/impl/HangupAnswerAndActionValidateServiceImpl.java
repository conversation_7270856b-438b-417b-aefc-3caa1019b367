package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.JumpTypeEnum;
import com.yiwise.dialogflow.entity.enums.PostActionTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.HangupAnswerAndActionValidateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HangupAnswerAndActionValidateServiceImpl implements HangupAnswerAndActionValidateService {

    private static final Set<String> HANGUP_ANSWER_KEYWORDS = new HashSet<>(Arrays.asList("拜拜", "再见"));
    @Override
    public List<SnapshotInvalidFailItemMsg> validateNode(StepPO step, List<DialogBaseNodePO> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        // 检查是否是跳转节点
        return nodeList.stream()
                .filter(node -> node instanceof DialogJumpNodePO)
                .map(node -> (DialogJumpNodePO) node)
                .filter(node -> !JumpTypeEnum.HANG_UP.equals(node.getJumpType()))
                .filter(node -> CollectionUtils.isNotEmpty(node.getAnswerList()))
                .map(node -> {
                    return node.getAnswerList().stream()
                            .filter(this::containsHangupKeyword)
                            .findFirst()
                            .map(nodeAnswer -> SnapshotInvalidFailItemMsg
                                    .builder()
                                    .resourceType(BotResourceTypeEnum.NODE)
                                    .failMsg(String.format("节点:%s[%s]: \"回答后操作\"不为挂机, 请校验", node.getName(), node.getLabel()))
                                    .resourceId(step.getId())
                                    .resourceName(step.getName())
                                    .resourceLabel(step.getLabel())
                                    .nodeId(node.getId())
                                    .nodeLabel(node.getLabel())
                                    .nodeName(node.getName())
                                    .isWarning(true)
                                    .build())
                            .orElse(null);
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private boolean containsHangupKeyword(BaseAnswerContent answerContent) {
        if (Objects.isNull(answerContent) || StringUtils.isBlank(answerContent.getText())) {
            return false;
        }
        for (String keyword : HANGUP_ANSWER_KEYWORDS) {
            if (answerContent.getText().contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<SnapshotInvalidFailItemMsg> validateKnowledge(List<KnowledgePO> knowledgeList) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return Collections.emptyList();
        }

        List<SnapshotInvalidFailItemMsg> result = new ArrayList<>();
        for (KnowledgePO knowledge : knowledgeList) {
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                Optional<KnowledgeAnswer> firstInvalidAnswer = knowledge.getAnswerList().stream()
                        .filter(this::containsHangupKeyword)
                        .filter(answer -> !PostActionTypeEnum.HANG_UP.equals(answer.getPostAction()))
                        .findFirst();
                firstInvalidAnswer.ifPresent(answer -> result.add(SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.KNOWLEDGE)
                        .failMsg(String.format("知识库:%s[%s]: \"回答后操作\"不为挂机, 请校验", knowledge.getName(), knowledge.getLabel()))
                        .resourceId(knowledge.getId())
                        .resourceName(knowledge.getName())
                        .resourceLabel(knowledge.getLabel())
                        .isWarning(true)
                        .build()));
            }
        }
        return result;
    }

    @Override
    public List<SnapshotInvalidFailItemMsg> validateSpecialAnswer(List<SpecialAnswerConfigPO> specialAnswerList) {
        if (CollectionUtils.isEmpty(specialAnswerList)) {
            return Collections.emptyList();
        }

        List<SnapshotInvalidFailItemMsg> result = new ArrayList<>();
        for (SpecialAnswerConfigPO specialAnswer : specialAnswerList) {
            if (CollectionUtils.isNotEmpty(specialAnswer.getAnswerList())) {
                Optional<KnowledgeAnswer> firstInvalidAnswer = specialAnswer.getAnswerList().stream()
                        .filter(this::containsHangupKeyword)
                        .filter(answer -> !PostActionTypeEnum.HANG_UP.equals(answer.getPostAction()))
                        .findFirst();
                firstInvalidAnswer.ifPresent(answer -> result.add(SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.SPECIAL_ANSWER_CONFIG)
                        .failMsg(String.format("特殊语境:%s[%s]: \"回答后操作\"不为挂机, 请校验", specialAnswer.getName(), specialAnswer.getLabel()))
                        .resourceId(specialAnswer.getId())
                        .resourceName(specialAnswer.getName())
                        .resourceLabel(specialAnswer.getLabel())
                        .isWarning(true)
                        .build()));
            }
        }
        return result;
    }
}
