package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.encrypt.Md5Utils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import com.yiwise.dialogflow.entity.dto.llmchat.*;
import com.yiwise.dialogflow.reactor.Accumulator;
import com.yiwise.dialogflow.reactor.FluxAccumulator;
import com.yiwise.dialogflow.service.llm.LLMChatService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yiwise.dialogflow.common.ApplicationConstant.ALGORITHM_LLM_ANSWER_VALIDATE_URL;

@Slf4j
@Service
public class LLMChatServiceImpl implements LLMChatService {

    private static final Set<Character> PUNCTUATION_SET = Stream.of(',', '.', '?', '!', '，', '。', '？', '！', '；', ';', '~')
            .collect(Collectors.toSet());

    @Resource(name = "llmChatClient")
    private WebClient webClient;


    @Resource(name = "llmAnswerValidate")
    private WebClient llmAnswerValidateWebClient;

    @Resource
    private RedisOpsService redisOpsService;

    private static final Integer CONTEXT_LIVE_TIME = 28800;

    @Override
    public Flux<LLMChatResponse> llmStepChat(LLMChatRequest request) {
        return llmStepChat(request, new ResponseAnswerAccumulator(request));
    }

    @Override
    public Flux<LLMChatResponse> chat(LLMChatRequest request) {
        if (StringUtils.isBlank(request.getQuery())) {
            request.setQuery("");
        }
        request.setEnvName(ApplicationConstant.ALGORITHM_RAG_UPLOAD_ENV);
        String url = ApplicationConstant.ALGORITHM_LLM_CHAT_URL;
        log.debug("url:{}, param:{}", url, JsonUtils.object2String(request));
        return webClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(request)))
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnError((error) -> {
                    log.warn("[LogHub_Warn] 请求对话接口异常, url:{}", url, error);
                })
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, new ResponseAnswerAccumulator(new LLMChatRequest())))
                .transformDeferred(MdcLogIdLifterTransformer.lift());
    }

    @Override
    public <T> Flux<T> llmStepChat(LLMChatRequest request, Accumulator<String, T> accumulator) {
        if (StringUtils.isBlank(request.getQuery())) {
            request.setQuery("");
        }
        request.setEnvName(ApplicationConstant.ALGORITHM_RAG_UPLOAD_ENV);
        List<String> contextIds = getContextIds(request);
        if (CollectionUtils.isNotEmpty(contextIds)) {
            request.setContextIds(contextIds);
        }
        String url = ApplicationConstant.ALGORITHM_LLMSTEP_CHAT_URL;
        log.debug("url:{}, param:{}", url, JsonUtils.object2String(request));
        return webClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(request)))
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnError((error) -> {
                    log.warn("[LogHub_Warn] 请求对话接口异常, url:{}", url, error);
                })
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, accumulator))
                .transformDeferred(MdcLogIdLifterTransformer.lift());
    }

    public static class ResponseAnswerAccumulator implements Accumulator<String, LLMChatResponse> {
        private volatile String stashText = "";
        private volatile String stashTrackInfo = "";
        private volatile LLMChatResponse lastResponse;
        private volatile boolean complete = false;
        private volatile int totalResponseAnswerTextSize = 0;

        private final LLMChatRequest request;
        public ResponseAnswerAccumulator(LLMChatRequest request) {
            this.request = request;
        }

        @Override
        public Optional<LLMChatResponse> accumulate(String responseBody) {
            log.debug("responseBody: [{}]", responseBody);
            if (complete) {
                log.debug("Already completed, ignore response body: [{}]", responseBody);
                return Optional.empty();
            }
            if (StringUtils.isBlank(responseBody)) {
                return Optional.empty();
            }

            try {
                LLMChatResponse response = JsonUtils.string2Object(responseBody, LLMChatResponse.class);
                if (response != null && StringUtils.isNotBlank(response.getTrackInfo())) {
                    this.stashTrackInfo = this.stashTrackInfo + response.getTrackInfo();
                }
                if (response == null || response.getCode() > 0) {
                    return Optional.empty();
                }

                lastResponse = response;

                String responseText = response.getResponse();
                String fullText = stashText + responseText;
                // Find the last punctuation mark
                int index = findLastPunctuation(fullText);
                int firstSentenceMinLength = 4;
                if (request.isUsePrefillResponse()) {
                    firstSentenceMinLength = 0;
                }
                if (index > firstSentenceMinLength || (index > 0 && totalResponseAnswerTextSize > firstSentenceMinLength)) {
                    String sendText = fullText.substring(0, index + 1);
                    totalResponseAnswerTextSize += sendText.length();
                    stashText = fullText.substring(index + 1);
                    response.setResponse(sendText);
                    response.setTrackInfo(stashTrackInfo);
                    this.stashTrackInfo = "";
                    return Optional.of(response);
                } else {
                    stashText = fullText;
                }

                if (CollectionUtils.isNotEmpty(response.getTools())) {
                    response.setResponse("");
                    response.setTrackInfo("");
                    return Optional.of(response);
                }
            } catch (Exception e) {
                log.error("Failed to parse response body: [{}]", responseBody, e);
            }
            return Optional.empty();
        }

        @Override
        public Optional<LLMChatResponse> onComplete() {
            complete = true;

            String sendText = Objects.isNull(stashText) ? "" : stashText;

            if (Objects.isNull(lastResponse)) {
                return Optional.empty();
            }
            LLMChatResponse emitResponse = MyBeanUtils.copy(lastResponse, LLMChatResponse.class);
            emitResponse.setResponse(sendText);
            emitResponse.setLastResponse(true);
            emitResponse.setTrackInfo(stashTrackInfo);
            return Optional.of(emitResponse);
        }
    }

    private static int findLastPunctuation(String text) {
        for (int i = text.length() - 1; i >= 0; i--) {
            if (PUNCTUATION_SET.contains(text.charAt(i))) {
                return i;
            }
        }
        return -1;
    }

    private String getCacheKey(LLMChatRequest request) {
        return RedisKeyCenter.getLlmContextCacheKey(calculateHashValue(request));
    }

    private List<String> getContextIds(LLMChatRequest request) {
        String cacheKey = getCacheKey(request);
        String contextIds = redisOpsService.get(cacheKey);
        if (StringUtils.isNotBlank(contextIds)) {
            return Arrays.stream(contextIds.split(",")).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void asyncCache(LLMChatRequest request) {
        DynamicDataSourceApplicationExecutorHolder.execute("大模型对话缓存", () -> {
            String cacheKey = getCacheKey(request);
            if (redisOpsService.setIfAbsent(cacheKey, "", TimeUnit.SECONDS.toSeconds(10))) {
                request.setTimeToLive(CONTEXT_LIVE_TIME);

                String url = ApplicationConstant.ALGORITHM_LLM_CACHE_URL;
                String body = JsonUtils.object2String(request);
                log.info("大模型对话缓存, url={}, body={}", url, body);
                long start = System.currentTimeMillis();

                webClient.method(HttpMethod.POST)
                        .uri(url)
                        .body(BodyInserters.fromValue(body))
                        .exchange()
                        .flatMap(response -> response.bodyToMono(String.class))
                        .transformDeferred(MdcLogIdLifterTransformer.lift())
                        .doOnNext(response -> log.info("大模型对话缓存, cost={}ms, response={}", System.currentTimeMillis() - start, response))
                        .map(json -> JsonUtils.string2Object(json, LlmCacheResponse.class).getContextIds())
                        .doOnSuccess(contextIds -> redisOpsService.set(cacheKey, String.join(",", contextIds), CONTEXT_LIVE_TIME - 3600, TimeUnit.SECONDS))
                        .onErrorResume(e -> {
                            log.warn("[LogHub_Warn]大模型对话缓存失败, url={}, body={}", url, body, e);
                            redisOpsService.delete(cacheKey);
                            return Mono.empty();
                        }).block();
            }
        });
    }

    @Override
    public Mono<Boolean> validateKnowledgeAnswer(Long botId, String userInput, String answerText, List<SimpleChatHistory> history) {
        String url = ALGORITHM_LLM_ANSWER_VALIDATE_URL;
        Map<String, Object> params = new HashMap<>();
        /**
         * 参数
         * {
         *     'history': [
         *         {
         *             'role': 'user',
         *             'content': '你是哪里的'
         *         },
         *         {
         *             'role': 'assistant',
         *             'content': '哎，你好，我这边是一知智能的客服小知。'
         *         }
         *     ],
         *     'dialogFlowId': '00001',
         *     'envName': 'test'
         *     'requestId': 'abcd100129asvds'
         * }
         * 响应:
         * {
         *     "code": 0,
         *     "result": true,
         *     "request_id": "",
         *     "info": "成功"
         * }
         */
        params.put("envName", ApplicationConstant.ALGORITHM_RAG_UPLOAD_ENV);
        params.put("dialogFlowId", botId);
        params.put("requestId", MDC.get("MDC_LOG_ID"));
        params.put("history", history);
        params.put("userContent", userInput);
        params.put("answer", answerText);

        log.debug("请求接口判断答案是否合适, url:{}, param:{}", url, JsonUtils.object2String(params));

        return llmAnswerValidateWebClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(params)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("请求接口判断答案是否合适, response={}", response))
                .map(json -> {
                    Map<String, Object> result = JsonUtils.string2MapObject(json, String.class, Object.class);
                    return "true".equals(result.getOrDefault("result", true));
                })
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]请求接口判断答案是否合适失败", e);
                    return Mono.just(true);
                });
    }

    @Data
    static class LlmCacheResponse implements Serializable {

        Integer code;

        List<String> contextIds;

        String info;
    }

    private String calculateHashValue(LLMChatRequest request) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(request.getSystem())) {
            sb.append(request.getSystem());
        }
        LLMChatConfig config = request.getConfig();
        if (Objects.nonNull(config)) {
            if (StringUtils.isNotBlank(config.getBackground())) {
                sb.append(config.getBackground());
            }
            if (StringUtils.isNotBlank(config.getKnowledge())) {
                sb.append(config.getKnowledge());
            }
            if (CollectionUtils.isNotEmpty(config.getTasks())) {
                for (LLMChatTask task : config.getTasks()) {
                    if (StringUtils.isNotBlank(task.getDescription())) {
                        sb.append(task.getDescription());
                    }
                    if (CollectionUtils.isNotEmpty(task.getScripts())) {
                        for (String script : task.getScripts()) {
                            sb.append(script);
                        }
                    }
                }
           }
        }
        return Md5Utils.getMd5String(sb.toString());
    }
}
