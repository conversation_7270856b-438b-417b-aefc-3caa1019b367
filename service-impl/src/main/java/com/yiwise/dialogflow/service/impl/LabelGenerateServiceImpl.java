package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.LabelGenerateService;
import com.yiwise.dialogflow.service.BotResourceSequenceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class LabelGenerateServiceImpl implements LabelGenerateService {

    @Resource
    private BotResourceSequenceService botResourceSequenceService;

    @Override
    public void answerLabel(Long botId, List<? extends BaseAnswerContent> answerList) {
        generateLabel(botId, "answer", "", answerList);
    }

    @Override
    public void nodeLabel(Long botId, String parentLabel, List<? extends DialogBaseNodePO> nodeList) {
        String resourceType = String.format("node-%s", parentLabel);
        String prefix = String.format("%s-", parentLabel);
        generateLabel(botId, resourceType, prefix, nodeList);
    }

    @Override
    public void updateNodeLabelSeqIfNeed(Long botId, Integer newSeq, String parentLabel) {
        String resourceType = String.format("node-%s", parentLabel);
        botResourceSequenceService.updateSeqIfCurrentLtNew(botId, resourceType, newSeq);
    }

    @Override
    public void renameNodeLabelPrefix(Long botId, String oldStepLabel, String newStepLabel) {
        String oldResourceType = String.format("node-%s", oldStepLabel);
        String newResourceType = String.format("node-%s", newStepLabel);
        botResourceSequenceService.renameResourceType(botId, oldResourceType, newResourceType);
    }

    @Override
    public void mainStepLabel(Long botId, List<StepPO> mainStepList) {
        generateLabel(botId, "mainStep", "M", mainStepList);
    }

    @Override
    public void updateMainStepSeqIfNeed(Long botId, Integer newSeq) {
        botResourceSequenceService.updateSeqIfCurrentLtNew(botId, "mainStep", newSeq);
    }

    @Override
    public void independentStepLabel(Long botId, List<StepPO> independentStepList) {
        generateLabel(botId, "independentStep", "I", independentStepList);
    }

    @Override
    public void updateIndependentStepSeqIfNeed(Long botId, Integer newSeq) {
        botResourceSequenceService.updateSeqIfCurrentLtNew(botId, "independentStep", newSeq);
    }

    @Override
    public void knowledgeLabel(Long botId, List<? extends KnowledgePO> knowledgeList) {
        generateLabel(botId, "knowledge", "Q", knowledgeList);
    }

    @Override
    public void specialAnswerLabel(Long botId, List<SpecialAnswerConfigPO> specialAnswerList) {
        generateLabel(botId, "specialAnswer", "C", specialAnswerList);
    }

    private void generateLabel(Long botId, String resourceType, String prefix, List<? extends Labelled> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<? extends Labelled> filterList = list.stream()
                .filter(item -> StringUtils.isBlank(item.getLabel()))
                .collect(Collectors.toList());

        if (filterList.isEmpty()) {
            return;
        }
        List<Integer> numList = botResourceSequenceService.generate(botId, resourceType, filterList.size());
        for (int i = 0; i < numList.size(); i++) {
            Labelled answer = filterList.get(i);
            answer.setLabel(String.format("%s%s", prefix, numList.get(i)));
        }
    }

}
