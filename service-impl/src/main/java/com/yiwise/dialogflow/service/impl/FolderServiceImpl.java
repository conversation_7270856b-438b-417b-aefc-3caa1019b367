package com.yiwise.dialogflow.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.AtomicLongMap;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.po.BotFolderRelPO;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.FolderPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.folder.FolderTreeVO;
import com.yiwise.dialogflow.entity.vo.folder.FolderVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.mapper.BotFolderRelPOMapper;
import com.yiwise.dialogflow.mapper.BotPOMapper;
import com.yiwise.dialogflow.mapper.FolderPOMapper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.FolderService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.middleware.mysql.service.impl.BasicServiceImpl;
import javaslang.Tuple;
import javaslang.Tuple2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
@Service
public class FolderServiceImpl extends BasicServiceImpl<FolderPO> implements FolderService {

    private static final Integer MAX_DEPTH = 4;
    private static final Integer MAX_FOLDER = 5000;

    @Resource
    private FolderPOMapper folderPOMapper;

    @Resource
    private BotFolderRelPOMapper botFolderRelPOMapper;

    @Resource
    private BotPOMapper botPOMapper;

    @Resource
    private UserService userService;

    @Lazy
    @Resource
    private BotService botService;

    @Transactional
    @Override
    public Long create(FolderPO folderPO) {
        Long tenantId = folderPO.getTenantId();
        int depth = 0;
        if (Objects.nonNull(folderPO.getParentFolderId())) {
            FolderPO parentFolder = selectByKey(folderPO.getParentFolderId());
            Assert.notNull(parentFolder, "父文件夹不存在");
            depth = parentFolder.getDepth() + 1;
            if (depth >= MAX_DEPTH) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "新建失败，文件夹层数超出上限");
            }
        } else {
            // 如果不存在首页则创建
            FolderPO root = getRootByTenantId(tenantId);
            folderPO.setParentFolderId(root.getFolderId());
        }

        Integer count = folderPOMapper.countByDepth(tenantId, depth);
        if (count >= MAX_FOLDER) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "新建失败，文件夹个数超出上限");
        }

        Example example = new Example(FolderPO.class);
        example.createCriteria().andEqualTo("tenantId", tenantId).andEqualTo("name", folderPO.getName());
        int i = folderPOMapper.selectCountByExample(example);
        if (i > 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文件夹名称已存在");
        }

        folderPO.setDepth(depth);
        saveNotNull(folderPO);
        return folderPO.getFolderId();
    }

    @Override
    public void update(FolderPO folderPO) {
        FolderPO oldPO = selectByKey(folderPO.getFolderId());
        Assert.notNull(oldPO, "文件夹不存在");

        Example example = new Example(FolderPO.class);
        example.createCriteria()
                .andEqualTo("tenantId", folderPO.getTenantId())
                .andEqualTo("name", folderPO.getName())
                .andNotEqualTo("folderId", folderPO.getFolderId());
        int i = folderPOMapper.selectCountByExample(example);
        if (i > 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文件夹名称已存在");
        }

        oldPO.setName(folderPO.getName());
        oldPO.setUpdateUserId(folderPO.getUpdateUserId());
        folderPOMapper.updateByPrimaryKeySelective(oldPO);
    }

    @Override
    public void deleteById(Long folderId) {
        List<Long> folderIdList = subFolderIdList(folderId);
        folderIdList.forEach(val -> {
            Example example = new Example(BotFolderRelPO.class);
            example.createCriteria().andEqualTo("folderId", val);
            botFolderRelPOMapper.deleteByExample(example);
            delete(val);
        });
    }

    /**
     * 子文件夹列表（包含传入的父文件夹）
     */
    @Override
    public List<Long> subFolderIdList(Long parentFolderId) {
        FolderPO folderPO = selectByKey(parentFolderId);
        Assert.notNull(folderPO, "文件夹不存在");
        List<Long> resultList = Lists.newArrayList();
        FolderVO parentFolder = folderTreeList(folderPO.getTenantId(), parentFolderId);
        traverse(parentFolder, folderVO -> resultList.add(folderVO.getFolderId()));
        return resultList;
    }

    /**
     * 文件夹的树状视图
     */
    private FolderVO folderTreeList(Long tenantId, Long parentFolderId) {
        int depth;
        FolderPO parentFolder;
        if (Objects.nonNull(parentFolderId)) {
            parentFolder = selectByKey(parentFolderId);
            Assert.notNull(parentFolder, "父文件夹不存在");
            depth = parentFolder.getDepth();
        } else {
            depth = -1;
            parentFolder = getRootByTenantId(tenantId);
        }

        List<FolderPO> folderPOList = folderPOMapper.getByTenantIdAndDepth(tenantId, depth, Collections.emptyList());

        List<FolderVO> folderVOList = Lists.newArrayList();
        MultiValuedMap<Long, FolderVO> subFolderMap = new ArrayListValuedHashMap<>();
        folderPOList.stream().sorted(Collections.reverseOrder(Comparator.comparingInt(FolderPO::getDepth))).forEachOrdered(folderPO -> {
            Long folderId = folderPO.getFolderId();
            FolderVO folderVO = new FolderVO();
            BeanUtils.copyProperties(folderPO, folderVO);

            if (Objects.nonNull(folderPO.getParentFolderId())) {
                subFolderMap.put(folderPO.getParentFolderId(), folderVO);
            }
            Collection<FolderVO> subFolderList = subFolderMap.get(folderId);
            folderVO.setSubFolderList(Lists.newArrayList(subFolderList));

            // 顶部文件夹加入结果集
            if (folderVO.getFolderId().equals(parentFolder.getFolderId())) {
                folderVOList.add(folderVO);
            }
        });

        return folderVOList.get(0);
    }

    /**
     * 树状视图
     * - 按深度查询文件夹
     * - 查询得到的文件夹是平铺的，按深度倒序排序后自下而上组装树
     * - 用了一个MultiValuedMap来暂时的储存子文件夹列表
     * - 当每一层被遍历完，进入上一层时，可以直接从MultiValuedMap获取其子文件夹列表
     * - 计数、话术列表等操作在遍历过程中进行
     */
    @Override
    public FolderTreeVO treeList(Long tenantId, String name, Long parentFolderId, List<Long> createUserIdList, Integer botType) {
        FolderTreeVO folderTreeVO = new FolderTreeVO();
        List<FolderVO> folderVOList = Lists.newArrayList();

        int depth = 0;
        if (Objects.nonNull(parentFolderId)) {
            FolderPO parentFolder = selectByKey(parentFolderId);
            Assert.notNull(parentFolder, "父文件夹不存在");
            depth = parentFolder.getDepth();
            if (depth > 4) {
                return folderTreeVO;
            } else if (depth == -1) {
                // 兼容原先首页的查询
                depth = 0;
                parentFolderId = null;
            }
        }
        // depth 代表查询的深度，finalDepth代表自下而上构建树时返回该深度的文件夹列表（树的构建不会停止）
        // 如果有parentFolderId，需要返回该parentFolderId下级列表，即 depth + 1
        int finalDepth = depth + BooleanUtils.toInteger(Objects.nonNull(parentFolderId));

        List<IdNamePair<Long, String>> botIdNamePairList = Lists.newArrayList();
        if (Objects.isNull(parentFolderId)) {
            if (StringUtils.isEmpty(name)) {
                // 首页需要返回没有关联文件夹的话术列表
                // OPE的查询
                if (tenantId == 0) {
                    botIdNamePairList = botPOMapper.getOPENoFolderBotByTenantId(createUserIdList, botType);
                } else {
                    botIdNamePairList = botPOMapper.getNoFolderBotByTenantId(tenantId, createUserIdList, botType);
                }
            } else {
                botIdNamePairList = botPOMapper.getIdNamePairByBotIdList(Lists.newArrayList(), name, tenantId, createUserIdList, botType);
            }
        }

        List<FolderPO> folderPOList = folderPOMapper.getByTenantIdAndDepth(tenantId, depth, createUserIdList);
        List<FolderVO> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(folderPOList)) {
            Tuple2<List<FolderVO>, List<IdNamePair<Long, String>>> tuple2 = buildTree(botIdNamePairList, folderPOList, folderVOList, parentFolderId, finalDepth, createUserIdList, botType);
            folderVOList = tuple2._1;
            if (Objects.nonNull(parentFolderId)) {
                botIdNamePairList = tuple2._2;
            }
        }

        // 搜索
        if (StringUtils.isNotEmpty(name)) {
            for (int i = 0, folderVOListSize = folderVOList.size(); i < folderVOListSize; i++) {
                FolderVO folderVO = folderVOList.get(i);
                List<FolderVO> finalResultList1 = Lists.newArrayList();
                traverse(folderVO, folderVO1 -> {
                    if (StringUtils.containsIgnoreCase(folderVO1.getName(), name)) {
                        finalResultList1.add(folderVO1);
                    }
                });
                resultList.addAll(finalResultList1);
            }
            resultList.sort(Collections.reverseOrder(Comparator.comparingLong(FolderPO::getFolderId)));
            // 当搜索时，idNamePairList需要过滤为所有名称匹配的话术
            botIdNamePairList.removeIf(idNamePair -> StringUtils.isNotEmpty(name) && !StringUtils.containsIgnoreCase(idNamePair.getName(), name));
        } else {
            resultList = folderVOList;
        }

        folderTreeVO.setFolderList(resultList);
        folderTreeVO.setIdNamePairList(botIdNamePairList);
        return folderTreeVO;
    }

    private Tuple2<List<FolderVO>, List<IdNamePair<Long, String>>> buildTree(List<IdNamePair<Long, String>> idNamePairList,
                                                                             List<FolderPO> folderPOList,
                                                                             List<FolderVO> folderVOList,
                                                                             Long parentFolderId, int finalDepth,
                                                                             List<Long> createUserIdList,
                                                                             Integer botType) {
        List<Long> folderIdList = MyCollectionUtils.listToConvertList(folderPOList, FolderPO::getFolderId);

        // 关联关系
        List<BotFolderRelPO> botFolderRelPOList = botFolderRelPOMapper.listByFolderIdList(folderIdList);
        List<Long> botIdList = MyCollectionUtils.listToConvertList(botFolderRelPOList, BotFolderRelPO::getBotId);
        Map<Long, Long> botMap = MyCollectionUtils.listToMap(botFolderRelPOList, BotFolderRelPO::getBotId, BotFolderRelPO::getFolderId);

        // 话术键值对
        MultiValuedMap<Long, IdNamePair<Long, String>> botRelMap = new ArrayListValuedHashMap<>();
        if (CollectionUtils.isNotEmpty(botIdList)) {
            idNamePairList = Lists.newCopyOnWriteArrayList(botPOMapper.getIdNamePairByBotIdList(botIdList, null, null, createUserIdList, botType));
            idNamePairList.forEach(idNamePair -> {
                Long folderId = botMap.get(idNamePair.getId());
                if (Objects.nonNull(folderId)) {
                    botRelMap.put(folderId, idNamePair);
                }
            });
        }

        List<FolderVO> finalResultList = folderVOList;
        MultiValuedMap<Long, FolderVO> subFolderMap = new ArrayListValuedHashMap<>();
        folderPOList.stream().sorted(Collections.reverseOrder(Comparator.comparingInt(FolderPO::getDepth))).forEachOrdered(folderPO -> {
            Long folderId = folderPO.getFolderId();
            FolderVO folderVO = new FolderVO();
            BeanUtils.copyProperties(folderPO, folderVO);

            if (Objects.nonNull(folderPO.getParentFolderId())) {
                subFolderMap.put(folderPO.getParentFolderId(), folderVO);
            }
            Collection<FolderVO> subFolderList = subFolderMap.get(folderId);
            folderVO.setSubFolderList(subFolderList.stream().sorted(Collections.reverseOrder(Comparator.comparingLong(FolderVO::getFolderId))).collect(Collectors.toList()));
            folderVO.setIdNamePairList(Lists.newArrayList(botRelMap.get(folderId)));

            // 总话术数 = 当前文件夹下话术数 + 子文件夹下话术数
            int count = botRelMap.get(folderId).size() + subFolderList.stream().mapToInt(FolderVO::getCount).sum();
            folderVO.setCount(count);

            // 指定深度的文件夹加入结果集
            if (folderPO.getDepth() == finalDepth) {
                finalResultList.add(folderVO);
            }
        });

        if (Objects.nonNull(parentFolderId)) {
            folderVOList = folderVOList.stream().filter(folderVO -> folderVO.getParentFolderId().equals(parentFolderId)).collect(Collectors.toList());
            idNamePairList = Lists.newArrayList(botRelMap.get(parentFolderId));
            return Tuple.of(folderVOList, idNamePairList);
        }

        return Tuple.of(finalResultList, idNamePairList);
    }

    private void traverse(FolderVO root, Consumer<FolderVO> consumer){
        if (Objects.isNull(root)) {
            return;
        }
        consumer.accept(root);
        if (CollectionUtils.isNotEmpty(root.getSubFolderList())) {
            for (FolderVO folderVO : root.getSubFolderList()) {
                traverse(folderVO, consumer);
            }
        }
    }

    @Override
    public List<Long> getBotIdListByFolderId(Long folderId) {
        return botFolderRelPOMapper.getBotIdListByFolderId(folderId);
    }

    @Override
    public List<Long> getBotIdListByFolderIdList(Collection<Long> folderIdList) {
        return getBotIdListByFolderIdList(folderIdList, false);
    }

    @Override
    public List<Long> getBotIdListByFolderIdList(Collection<Long> folderIdList, boolean includeSubFolder) {
        List<Long> finalFolderIdList = new ArrayList<>(folderIdList);
        if (includeSubFolder) {
            folderIdList.stream()
                    .map(this::subFolderIdList)
                    .forEach(finalFolderIdList::addAll);
        }
        List<BotFolderRelPO> botFolderRelPOList = botFolderRelPOMapper.listByFolderIdList(finalFolderIdList);
        return MyCollectionUtils.listToConvertList(botFolderRelPOList, BotFolderRelPO::getBotId);
    }

    /**
     * 当前及子文件夹下的话术列表
     */
    @Override
    public List<Long> getSubBotIdListByFolderId(Long tenantId, Long folderId, Boolean isSearch) {
        // 首页列表为所有不在文件夹内的话术
        if (folderId == -1) {
            if (Objects.isNull(tenantId)) {
                return MyCollectionUtils.listToConvertList(botPOMapper.getOPENoFolderBotByTenantId(null, null), IdNamePair::getId);
            } else {
                return MyCollectionUtils.listToConvertList(botPOMapper.getNoFolderBotByTenantId(tenantId, null, null), IdNamePair::getId);
            }
        }
        List<Long> subFolderIdList;
        // 文件夹列表只展示当前文件夹的，文件夹搜索需要搜索到子文件夹内的
        if (BooleanUtils.isTrue(isSearch)) {
            subFolderIdList = subFolderIdList(folderId);
        } else {
            subFolderIdList = Lists.newArrayList(folderId);
        }
        return getBotIdListByFolderIdList(subFolderIdList);
    }

    @Transactional
    @Override
    public void move(Long tenantId, Long currentUserId, List<Long> botIdList, List<Long> folderIdList, Long targetFolderId) {
        // 文件夹移动
        if (CollectionUtils.isNotEmpty(folderIdList)) {
            Integer targetDepth;
            if (Objects.nonNull(targetFolderId)) {
                FolderPO targetFolderPO = selectByKey(targetFolderId);
                targetDepth = targetFolderPO.getDepth();
            } else {
                // targetFolderId为空说明移动到首页
                FolderPO rootByTenantId = getRootByTenantId(tenantId);
                targetFolderId = rootByTenantId.getFolderId();
                targetDepth = -1;
            }
            FolderVO root = folderTreeList(tenantId, null);

            List<FolderPO> updatePOList = Lists.newArrayList();
            Integer finalTargetDepth = targetDepth;
            // 待移动的子树
            List<FolderVO> srcFolderList = Lists.newArrayList();
            // 这一遍遍历只是找出目标子树
            traverse(root, item -> {
                if (folderIdList.contains(item.getFolderId())) {
                    srcFolderList.add(item);
                }
            });

            AtomicLongMap<Integer> depthMap = AtomicLongMap.create();
            Long finalTargetFolderId = targetFolderId;
            for (FolderVO subTreeRoot : srcFolderList) {
                // 表示深度的差值
                int sub = finalTargetDepth + 1 - subTreeRoot.getDepth();
                traverse(subTreeRoot, folderVO -> {
                    FolderPO updatePO = MyBeanUtils.copy(folderVO, FolderPO.class);
                    // 被移动的子树，只有根节点需要修改parentId
                    // 父文件夹不能为自身
                    if (!Objects.equals(finalTargetFolderId, folderVO.getFolderId()) && folderIdList.contains(folderVO.getFolderId())) {
                        updatePO.setParentFolderId(finalTargetFolderId);
                    }
                    updatePO.setUpdateUserId(currentUserId);
                    updatePO.setUpdateTime(LocalDateTime.now());
                    updatePO.setFolderId(folderVO.getFolderId());
                    updatePO.setDepth(folderVO.getDepth() + sub);
                    depthMap.getAndIncrement(updatePO.getDepth());
                    if (updatePO.getDepth() >= MAX_DEPTH) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "移动失败，文件夹层数超出上限");
                    }
                    updatePOList.add(updatePO);
                });
            }

            // 校验每一层的文件夹数量
            for (int i = 0; i < MAX_DEPTH; i++) {
                long count = depthMap.get(i);
                Integer dbCount = folderPOMapper.countByDepth(tenantId, i);
                if (dbCount + count >= MAX_FOLDER) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "移动失败，文件夹个数超出上限");
                }
            }

            for (FolderPO updatePO : updatePOList) {
                folderPOMapper.updateByPrimaryKey(updatePO);
            }
        }

        // 话术移动
        if (CollectionUtils.isNotEmpty(botIdList)) {
            Long finalTargetFolderId1 = targetFolderId;
            List<BotFolderRelPO> botFolderRelPOList = botIdList.stream().map(botId -> {
                BotFolderRelPO botFolderRelPO = new BotFolderRelPO();
                botFolderRelPO.setTenantId(tenantId);
                botFolderRelPO.setFolderId(finalTargetFolderId1);
                botFolderRelPO.setBotId(botId);
                botFolderRelPO.setCreateUserId(currentUserId);
                botFolderRelPO.setUpdateUserId(currentUserId);
                return botFolderRelPO;
            }).collect(Collectors.toList());

            // 先删除再插入
            Example example = new Example(BotFolderRelPO.class);
            example.createCriteria().andEqualTo("tenantId", tenantId).andIn("botId", botIdList);
            botFolderRelPOMapper.deleteByExample(example);
            if (Objects.nonNull(targetFolderId)) {
                botFolderRelPOList.forEach(botFolderRelPO -> botFolderRelPOMapper.insertSelective(botFolderRelPO));
            }
        }
    }

    /**
     * bot列表回显的文件夹路径
     * @return 文件夹ID-路径的map，其中路径按顺序组成列表返回
     */
    @Override
    public Map<Long, List<IdNamePair<Long, String>>> pathMap(Long tenantId, List<Long> folderIdList) {
        Map<Long, List<IdNamePair<Long, String>>> map = Maps.newHashMap();
        FolderVO root = folderTreeList(tenantId, null);
        List<IdNamePair<Long, String>> path = Lists.newLinkedList();
        path(root, path, map, folderIdList);
        return map;
    }

    private void path(FolderVO root, List<IdNamePair<Long, String>> path, Map<Long, List<IdNamePair<Long, String>>> map, List<Long> targetIdList) {
        if (root == null) {
            return;
        }

        path.add(new IdNamePair<>(root.getFolderId(), root.getName()));
        if (targetIdList.contains(root.getFolderId())) {
            map.put(root.getFolderId(), path);
        }

        for (FolderVO subFolder : root.getSubFolderList()) {
            path(subFolder, Lists.newLinkedList(path), map, targetIdList);
        }
    }

    @Override
    public Map<AuditStatusEnum, List<IdNamePair<Long, String>>> auditStatusCount(Long tenantId, List<Long> botIdList, List<Long> folderIdList, Set<AuditStatusEnum> auditStatusSet) {
        Map<AuditStatusEnum, List<IdNamePair<Long, String>>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(folderIdList)) {
            Set<Long> folderIdSet = Sets.newHashSet();
            folderIdList.forEach(folderId -> folderIdSet.addAll(subFolderIdList(folderId)));
            List<Long> botIdListByFolderIdList = getBotIdListByFolderIdList(folderIdSet);
            botIdList = MyCollectionUtils.merge(botIdList, botIdListByFolderIdList);
        }
        if (CollectionUtils.isNotEmpty(botIdList)) {
            List<Long> finalBotIdList = botIdList;
            auditStatusSet.forEach(auditStatus -> {
                Example example = new Example(BotPO.class);
                Example.Criteria criteria = example.createCriteria().andIn("botId", finalBotIdList);
                if (Objects.nonNull(auditStatus)) {
                    criteria.andEqualTo("auditStatus", auditStatus.getCode());
                }
                List<BotPO> botPOList = botPOMapper.selectByExample(example);
                resultMap.put(auditStatus, MyCollectionUtils.listToConvertList(botPOList, botPO -> new IdNamePair<>(botPO.getBotId(), botPO.getName())));
            });
        }
        return resultMap;
    }

    @Transactional
    @Override
    public FolderPO getRootByTenantId(Long tenantId) {
        Example example = new Example(FolderPO.class);
        example.createCriteria().andEqualTo("tenantId", tenantId).andEqualTo("depth", -1);
        FolderPO folderPO = folderPOMapper.selectOneByExample(example);
        if (Objects.isNull(folderPO)) {
            return createRootFolder(tenantId);
        }
        return folderPO;
    }

    private FolderPO createRootFolder(Long tenantId) {
        FolderPO root = new FolderPO();
        root.setTenantId(tenantId);
        root.setName("首页");
        root.setDepth(-1);
        root.setCreateUserId(0L);
        root.setUpdateUserId(0L);
        saveNotNull(root);
        return root;
    }

    /**
     * 数据订正
     */
    @Transactional
    @Override
    public void dataRevise() {
        Example example = new Example(FolderPO.class);
        example.selectProperties("tenantId").setDistinct(true);
        List<FolderPO> folderPOList = folderPOMapper.selectByExample(example);

        folderPOList.forEach(folderPO -> {
            Long tenantId = folderPO.getTenantId();
            FolderPO root = getRootByTenantId(tenantId);
            Long rootFolderId = root.getFolderId();
            Example example1 = new Example(FolderPO.class);
            example1.createCriteria().andEqualTo("tenantId", tenantId).andEqualTo("depth", 0);
            List<FolderPO> byTenantIdAndDepth = folderPOMapper.selectByExample(example1);
            byTenantIdAndDepth.forEach(folderPO1 -> {
                folderPO1.setParentFolderId(rootFolderId);
                folderPOMapper.updateByPrimaryKey(folderPO1);
            });
        });
    }

    @Override
    public PageResultObject<FolderPO> flatList(Long tenantId, String name, Integer pageNum, Integer pageSize) {
        if (Objects.isNull(tenantId)) {
            tenantId = 0L;
        }
        if (Objects.isNull(pageNum)) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 20;
        }
        PageHelper.startPage(pageNum, pageSize);
        List<FolderPO> folderList = folderPOMapper.search(tenantId, name);
        return PageResultObject.of(folderList);
    }

    /**
     * 查询所有创建者列表
     * @param tenantId 租户id
     * @return 创建人列表
     */
    @Override
    public List<IdNamePair<Long, String>> getAllCreatorList(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }

        List<Long> createUserIdList = folderPOMapper.selectCreateUserIdByTenantId(tenantId);
        if (CollectionUtils.isEmpty(createUserIdList)) {
            return Lists.newArrayList();
        }

        List<UserPO> userList = userService.getUserByIdList(createUserIdList);
        return userList.stream()
                .map(item -> new IdNamePair<>(item.getUserId(), item.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<Long, String>> getAllFolderAndBotCreatorList(Long tenantId) {
        List<IdNamePair<Long, String>> result = new ArrayList<>(getAllCreatorList(tenantId));
        Set<Long> userIdSet = result.stream().map(IdNamePair::getId).collect(Collectors.toSet());
        result.addAll(botService.listAllCreateUser(tenantId).stream()
                .filter(item -> !userIdSet.contains(item.getId()))
                .collect(Collectors.toList()));
        return result;
    }
}
