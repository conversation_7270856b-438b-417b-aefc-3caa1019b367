package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.BotResourceSequenceService;
import com.yiwise.dialogflow.service.RobotResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BotResourceSequenceServiceImpl implements BotResourceSequenceService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 不进行复制的资源序列
     */
    private static final List<String> EXCLUDE_RESOURCE_TYPE_FROM_COPY = Arrays.asList(SmsGenerateResultPO.COLLECTION_NAME);

    @Override
    public Integer generate(Long botId, String resourceType) {
        return generate(botId, resourceType, 1).get(0);
    }

    @Override
    public List<Integer> generate(Long botId, String type, int count) {
        if (count < 1) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("resourceType").is(type));
        Update update = new Update();
        update.inc("sequence", count);

        FindAndModifyOptions options = new FindAndModifyOptions();
        options.returnNew(true);
        Map result = mongoTemplate.findAndModify(query, update, options, Map.class, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
        if (Objects.isNull(result)) {
            init(botId, type);
            result = mongoTemplate.findAndModify(query, update, options, Map.class, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
        }
        int last = Integer.parseInt(result.get("sequence").toString());
        List<Integer> list = new ArrayList<>(count);
        for (int i = count - 1; i >= 0; i--) {
            list.add(last - i);
        }
        return list;
    }

    @Override
    public void updateSeqIfCurrentLtNew(Long botId, String resourceType, Integer newSeq) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("resourceType").is(resourceType))
                .addCriteria(Criteria.where("sequence").lt(newSeq));
        Update update = Update.update("sequence", newSeq);
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
    }

    @Override
    public void renameResourceType(Long botId, String oldResourceType, String newResourceType) {
        mongoTemplate.remove(
                Query.query(Criteria.where("botId").is(botId).and("resourceType").is(newResourceType)),
                MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE
        );
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("botId").is(botId).and("resourceType").is(oldResourceType)),
                Update.update("resourceType", newResourceType),
                MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE
        );
    }

    @Override
    public void copySequence(Long fromBotId, Long toBotId) {
        log.info("copy sequence from botId:{} to botId:{}", fromBotId, toBotId);
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(fromBotId).and("resourceType").nin(EXCLUDE_RESOURCE_TYPE_FROM_COPY));
        List<BotResourceSequencePO> sequenceList = mongoTemplate.find(query, BotResourceSequencePO.class, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
        if (CollectionUtils.isNotEmpty(sequenceList)) {
            sequenceList.forEach(item -> {
                item.setId(null);
                item.setBotId(toBotId);
            });
            mongoTemplate.insert(sequenceList, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
        }
    }

    @Override
    public void cleanAllSequence(Long newBotId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(newBotId));
        mongoTemplate.remove(query, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
    }

    private void init(Long botId, String resourceType) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("resourceType").is(resourceType));
        Update update = new Update();
        update.inc("sequence", 0);
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        // 不需要存到快照中
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        if (context.isCopy()) {
            copySequence(context.getSrcBotId(), context.getTargetBotId());
            // 答案label有脏数据, 还没确定是哪个地方引入的, 所以先在这里重置下answer的序列号
            List<BaseAnswerContent> allAnswerList = new ArrayList<>();
            for (DialogBaseNodePO node : context.getSnapshot().getNodeList()) {
                if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                    allAnswerList.addAll(node.getAnswerList());
                }
            }
            for (SpecialAnswerConfigPO specialAnswerConfigPO : context.getSnapshot().getSpecialAnswerConfigList()) {
                if (CollectionUtils.isNotEmpty(specialAnswerConfigPO.getAnswerList())) {
                    allAnswerList.addAll(specialAnswerConfigPO.getAnswerList());
                }
            }
            for (KnowledgePO knowledgePO : context.getSnapshot().getKnowledgeList()) {
                if (CollectionUtils.isNotEmpty(knowledgePO.getAnswerList())) {
                    allAnswerList.addAll(knowledgePO.getAnswerList());
                }
            }
            List<String> answerLabelIdList = allAnswerList.stream().map(BaseAnswerContent::getLabel).collect(Collectors.toList());
            Integer maxSequence = answerLabelIdList.stream()
                    .map(str -> {
                        try {
                            return Integer.parseInt(str);
                        } catch (Exception e) {
                            return 0;
                        }
                    }).max(Integer::compareTo).orElse(0);
            if (maxSequence > 0) {
                Query query = new Query();
                query.addCriteria(Criteria.where("botId").is(context.getTargetBotId()))
                        .addCriteria(Criteria.where("resourceType").is("answer"));
                Update update = new Update();
                update.set("sequence", maxSequence);
                mongoTemplate.upsert(query, update, MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE);
            }
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Arrays.asList(StepNodeServiceImpl.class, KnowledgeServiceImpl.class, SpecialAnswerConfigServiceImpl.class);
    }
}
