package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioDetail;
import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioElement;
import com.yiwise.dialogflow.api.dto.response.knowledge.SimpleKnowledge;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.stats.KnowledgeStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.SimpleKnowledgeStatsInfoVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.KnowledgeSyncVO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.impl.entitycollect.EntityServiceImpl;
import com.yiwise.dialogflow.service.impl.intent.IntentServiceImpl;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.operationlog.KnowledgeOperationLogService;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import javaslang.Tuple;
import com.yiwise.dialogflow.utils.BotExportUtils;
import javaslang.Tuple2;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.assertj.core.util.Sets;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeServiceImpl implements KnowledgeService, RobotResourceService {

    @Resource
    private LabelGenerateService labelGenerateService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private VariableService variableService;

    @Lazy
    @Resource
    private StepService stepService;

    @Resource
    private IntentService intentService;

    @Resource
    private DependResourceService dependResourceService;

    @Lazy
    @Resource
    private BotService botService;

    @Lazy
    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private GroupService groupService;

    @Resource
    private KnowledgeOperationLogService knowledgeOperationLogService;

    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private OperationLogService operationLogService;

    @Lazy
    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Lazy
    @Resource
    private BotConfigService botConfigService;

    @Resource
    private HangupAnswerAndActionValidateService hangupAnswerAndActionValidateService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private TtsJobService ttsJobService;

    @Override
    public Map<String, String> getNameByIdList(Collection<String> knowledgeIdList) {
        return MyCollectionUtils.listToConvertMap(getByIdList(knowledgeIdList), KnowledgePO::getId, KnowledgePO::getName);
    }

    @Override
    public String getNameById(String knowledgeId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return null;
        }
        return getNameByIdList(Collections.singletonList(knowledgeId)).get(knowledgeId);
    }

    @Override
    public List<KnowledgePO> getByIdList(Collection<String> knowledgeIdList) {
        if (CollectionUtils.isEmpty(knowledgeIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(knowledgeIdList));
        return mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    @Override
    public KnowledgeVO create(KnowledgePO knowledge, Long userId) {
        Boolean hasVarCreated = autoCreateVarIdNotExists(knowledge, userId);
        validParam(knowledge, true);
        knowledge.setCreateTime(LocalDateTime.now());
        knowledge.setUpdateTime(LocalDateTime.now());
        generateLabel(knowledge);
        mongoTemplate.insert(knowledge, KnowledgePO.COLLECTION_NAME);
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(knowledge.getBotId());
        updateDependResourceRef(knowledge, dependResourceService.generateByCondition(condition.variable()));
        updateBotToDraft(knowledge.getBotId());
        knowledgeOperationLogService.compareAndCreateOperationLog(knowledge.getBotId(), null, knowledge, userId);
        return convertPO2VO(knowledge).hasVarCreated(hasVarCreated);
    }

    private Boolean autoCreateVarIdNotExists(KnowledgePO knowledge, Long userId) {
        boolean hasVarCreated = false;
        if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
            for (KnowledgeAnswer answer : knowledge.getAnswerList()) {
                if (variableService.autoCreateIfNotExists(knowledge.getBotId(), answer.getText(), userId)) {
                    hasVarCreated = true;
                }
            }
        }
        return hasVarCreated;
    }

    @Override
    public void batchCreate(KnowledgeBatchCreateVO batchCreateVO, Long userId) {
        List<IntentPO> intentList = intentService.getByIdList(batchCreateVO.getTriggerIntentIdList());
        if (CollectionUtils.isEmpty(intentList)) {
            return;
        }
        Long botId = batchCreateVO.getBotId();
        GroupPO group = groupService.getById(batchCreateVO.getGroupId());
        Assert.isTrue(Objects.nonNull(group) && GroupTypeEnum.KNOWLEDGE.equals(group.getType()) && Objects.equals(botId, group.getBotId()), "分组不存在");
        LocalDateTime now = LocalDateTime.now();
        // 过滤掉意图同名知识
        List<String> knowledgeNameList = MyCollectionUtils.listToConvertList(getAllListByBotId(botId), KnowledgePO::getName);
        List<KnowledgePO> poList = intentList.stream()
                .filter(i -> !knowledgeNameList.contains(i.getName()))
                .map(intent -> {
                    KnowledgePO po = new KnowledgePO();
                    po.setName(intent.getName());
                    po.setBotId(botId);
                    po.setCategory(batchCreateVO.getCategory());
                    po.setIsCustomerConcern(KnowledgeCategoryEnum.BUSINESS.equals(batchCreateVO.getCategory()));
                    po.setAnswerList(Collections.emptyList());
                    po.setTriggerIntentIdList(Collections.singletonList(intent.getId()));
                    po.setGroupId(batchCreateVO.getGroupId());
                    po.setCreateTime(now);
                    po.setUpdateTime(now);
                    po.setCreateUserId(userId);
                    po.setUpdateUserId(userId);
                    return po;
                }).collect(Collectors.toList());
        generateLabel(botId, poList);
        mongoTemplate.insertAll(poList);
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        DependentResourceBO resource = dependResourceService.generateByCondition(condition.variable());
        poList.forEach(knowledge -> updateDependResourceRef(knowledge, resource));
        updateBotToDraft(botId);
    }

    private void updateBotToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    private KnowledgeVO convertPO2VO(KnowledgePO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        return convertPO2VO(po.getBotId(), Collections.singletonList(po)).get(0);
    }

    private List<KnowledgeVO> convertPO2VO(Long botId, List<KnowledgePO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<VariablePO> variableList = variableService.getListByBotId(botId);
        List<KnowledgePO> allKnowledgeList = getAllListByBotId(botId);

        Set<String> allKnowledgeIdSet = allKnowledgeList.stream().map(KnowledgePO::getId).collect(Collectors.toSet());

        List<GroupPO> groupList = groupService.getListByBotId(botId);
        boolean requireActionNameSource = poList.stream()
                .anyMatch(knowledge -> CollectionUtils.isNotEmpty(knowledge.getActionList()));

        ActionNameResourceBO nameResourceBO = requireActionNameSource ? intentRuleActionService.getSourceId2NameMapByBotId(botId) : new ActionNameResourceBO();
        Map<String, String> stepIdNameMap = MyCollectionUtils.listToConvertMap(stepList, StepPO::getId, StepPO::getName);
        Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(intentList, IntentPO::getId);
        Map<String, String> variableMap = MyCollectionUtils.listToMap(variableList, VariablePO::getId, VariablePO::getName);
        Map<String, String> groupMap = MyCollectionUtils.listToMap(groupList, GroupPO::getId, GroupPO::getPath);

        // 判断当前是否是真人录音, 如果是合成音, 则不设置是否已录音
        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        Set<String> audioAnswerTextSet = new HashSet<>();
        if (Objects.nonNull(botAudioConfig)
                && !AudioTypeEnum.COMPOSE.equals(botAudioConfig.getAudioType())
                && Objects.nonNull(botAudioConfig.getRecordUserId())) {
            List<AnswerAudioMappingPO> audioMappingList = answerAudioMappingService.listAllAvailableAudio(botId, botAudioConfig.getRecordUserId(), botAudioConfig.getAudioType(), null);
            audioAnswerTextSet.addAll(audioMappingList.stream().map(AnswerAudioMappingPO::getText).map(AnswerTextUtils::removeAnswerPrefixAndSuffixSymbols).collect(Collectors.toSet()));
        }

        return poList.stream()
                .map(po -> MyBeanUtils.copy(po, KnowledgeVO.class))
                .peek(vo -> vo.setStatsInfo(new KnowledgeStatsVO()))
                // 设置意图
                .peek(vo -> {
                    if (CollectionUtils.isNotEmpty(vo.getTriggerIntentIdList())) {
                        List<SimpleIntentVO> intentInfoList = vo.getTriggerIntentIdList()
                                .stream()
                                .filter(intentMap::containsKey)
                                .map(id -> SimpleIntentVO.createFrom(intentMap.get(id)))
                                .collect(Collectors.toList());
                        vo.setIntentInfoList(intentInfoList);
                    } else {
                        vo.setIntentInfoList(Collections.emptyList());
                    }
                })
                // 设置流程信息
                .peek(vo -> {
                    if (BooleanUtils.isTrue(vo.getEnableRepeatStep()) && CollectionUtils.isNotEmpty(vo.getRepeatStepIdList())) {
                        List<IdNamePair<String, String>> stepInfoList = vo.getRepeatStepIdList().stream()
                                .filter(stepIdNameMap::containsKey)
                                .map(id -> IdNamePair.of(id, stepIdNameMap.get(id)))
                                .collect(Collectors.toList());
                        vo.setStepInfoList(stepInfoList);
                    } else {
                        vo.setStepInfoList(Collections.emptyList());
                    }
                })
                //设置条件变量信息
                .peek(vo -> {
                    if (CollectionUtils.isNotEmpty(vo.getAnswerList())) {
                        for (KnowledgeAnswer knowledgeAnswer : vo.getAnswerList()) {
                            if (CollectionUtils.isNotEmpty(knowledgeAnswer.getConditionList())) {
                                List<List<ConditionExpressionPO>> conditionList = new ArrayList<>();
                                for (List<ConditionExpressionPO> andConditionList : knowledgeAnswer.getConditionList()) {
                                    List<ConditionExpressionPO> andConditionVOList = new ArrayList<>();
                                    for (ConditionExpressionPO condition : andConditionList) {
                                        ConditionExpressionVO conditionVO = MyBeanUtils.copy(condition, ConditionExpressionVO.class);
                                        conditionVO.setPreVarName(Optional.ofNullable(conditionVO.getPreVarId()).map(variableMap::get).orElse(null));
                                        conditionVO.setPostVarName(Optional.ofNullable(conditionVO.getPostVarId()).map(variableMap::get).orElse(null));
                                        andConditionVOList.add(conditionVO);
                                    }
                                    conditionList.add(andConditionVOList);
                                }
                                knowledgeAnswer.setConditionList(conditionList);
                            }
                        }
                    }
                })
                //设置分组路径
                .peek(vo -> vo.setGroupPath(Optional.ofNullable(vo.getGroupId()).map(groupMap::get).orElse(null)))
                // 设置动作信息
                .peek(vo -> {
                    if (CollectionUtils.isNotEmpty(vo.getActionList())) {
                        intentRuleActionService.addSourceName(vo.getActionList(), nameResourceBO);
                    }
                })
                .peek(vo -> {
                    vo.setAudioCompleted(true);
                    if (CollectionUtils.isNotEmpty(vo.getAnswerList())) {
                        Set<String> allAnswerPartionTextSet = vo.getAnswerList().stream()
                                .map(BaseAnswerContent::getText)
                                .filter(StringUtils::isNotBlank)
                                .map(text -> {
                                    AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, AudioTypeEnum.MIXTURE.equals(botAudioConfig.getAudioType()));
                                    return splitter.getTextList();
                                })
                                .flatMap(Collection::stream)
                                .filter(StringUtils::isNotBlank)
                                .map(AnswerTextUtils::removeAnswerPrefixAndSuffixSymbols)
                                .collect(Collectors.toSet());
                        vo.setAudioCompleted(audioAnswerTextSet.containsAll(allAnswerPartionTextSet));
                    }
                })
                // 处理掉脏数据
                .peek(vo -> {
                    if (CollectionUtils.isNotEmpty(vo.getAnswerList())) {
                        vo.getAnswerList().forEach(answer -> {
                            if (CollectionUtils.isNotEmpty(answer.getUninterruptedReplyStepIdList())) {
                                answer.setUninterruptedReplyStepIdList(answer.getUninterruptedReplyStepIdList().stream()
                                        .filter(stepIdNameMap::containsKey)
                                        .collect(Collectors.toList()));
                            }
                            if (CollectionUtils.isNotEmpty(answer.getUninterruptedReplyKnowledgeIdList())) {
                                answer.setUninterruptedReplyKnowledgeIdList(answer.getUninterruptedReplyKnowledgeIdList().stream()
                                        .filter(allKnowledgeIdSet::contains)
                                        .collect(Collectors.toList()));
                            }
                        });
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public KnowledgeVO update(KnowledgePO knowledge, Long userId) {
        Boolean hasVarCreated = autoCreateVarIdNotExists(knowledge, userId);
        KnowledgePO old = getById(knowledge.getBotId(), knowledge.getId());
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "知识不存在, 更新失败");
        }
        validParam(knowledge, false);
        knowledge.setUpdateTime(LocalDateTime.now());
        generateLabel(knowledge);
        saveKnowledge(knowledge);
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(knowledge.getBotId());
        updateDependResourceRef(knowledge, dependResourceService.generateByCondition(condition.variable()));
        updateBotToDraft(knowledge.getBotId());
        knowledgeOperationLogService.compareAndCreateOperationLog(knowledge.getBotId(), old, knowledge, userId);
        return convertPO2VO(knowledge).hasVarCreated(hasVarCreated);
    }

    private void saveKnowledge(KnowledgePO knowledge) {
        if (Objects.isNull(knowledge)) {
            return;
        }
        mongoTemplate.save(MyBeanUtils.copy(knowledge, KnowledgePO.class), KnowledgePO.COLLECTION_NAME);
    }

    private void generateLabel(KnowledgePO knowledge) {
        if (Objects.isNull(knowledge)) {
            return;
        }
        generateLabel(knowledge.getBotId(), Collections.singletonList(knowledge));
    }

    private void generateLabel(Long botId, List<? extends KnowledgePO> knowledgeList) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        labelGenerateService.knowledgeLabel(botId, knowledgeList);
        // 生成答案标签
        List<KnowledgeAnswer> answerList = new LinkedList<>();
        knowledgeList.forEach(knowledge -> {
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                answerList.addAll(knowledge.getAnswerList());
            }
        });
        generateAnswerLabel(botId, answerList);
    }

    private void generateAnswerLabel(Long botId, List<KnowledgeAnswer> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return;
        }
        labelGenerateService.answerLabel(botId, answerList);
    }

    @Override
    public void validAndThrow(KnowledgePO knowledge) {
        if (Objects.isNull(knowledge.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (StringUtils.isBlank(knowledge.getGroupId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分组不能为空");
        }
        if (StringUtils.isBlank(knowledge.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识标题不能为空");
        }
        if (Objects.isNull(knowledge.getCategory())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "类别不能为空");
        }
        if (CollectionUtils.isEmpty(knowledge.getAnswerList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案不能为空");
        }
        if (CollectionUtils.isEmpty(knowledge.getTriggerIntentIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图不能为空");
        }
        if (Objects.isNull(knowledge.getIsCustomerConcern())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "关注点不能为空");
        }
    }

    @Override
    public void validAndThrow(KnowledgePO knowledge,
                              DependentResourceBO dependentResource,
                              ActionNameResourceBO actionResource,
                              SnapshotValidateConfigBO validateConfig) {
        validAndThrow(knowledge);
        if (BooleanUtils.isTrue(knowledge.getEnableRepeatStep())) {
            if (CollectionUtils.isEmpty(knowledge.getRepeatStepIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "重复主流程不能为空");
            }
            // 重复意图被删除不影响对话逻辑, 暂时不处理
        }

        knowledge.getTriggerIntentIdList().forEach(intentId -> {
            if (!dependentResource.getIntentIdNameMap().containsKey(intentId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("依赖的意图不存在, 意图id=%s", intentId));
            }
        });

        if (BooleanUtils.isTrue(knowledge.getEnableIntentLevel()) && Objects.isNull(knowledge.getIntentLevelDetailCode())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未选择意向等级");
        }

        // 验证答案中依赖的变量是否存在
        for (KnowledgeAnswer answer : knowledge.getAnswerList()) {
            answer.validWithResource(dependentResource);
        }
        //校验动作配置
        if (Objects.nonNull(validateConfig)
                && BooleanUtils.isTrue(validateConfig.getRequireValidOutsideResource())) {
            if (Objects.nonNull(knowledge.getIsEnableAction()) && knowledge.getIsEnableAction()) {
                validAction(knowledge, actionResource);
            }
        }
        // 校验分组
        if (!GroupTypeEnum.KNOWLEDGE.equals(dependentResource.getGroupIdTypeMap().get(knowledge.getGroupId()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("知识[%s]分组不存在", knowledge.getName()));
        }
        // 校验变量赋值
        if (BooleanUtils.isTrue(knowledge.getEnableAssign())) {
            if (Objects.isNull(knowledge.getAssignConfig())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不能为空");
            }
            KnowledgeAssignConfigPO assignConfig = knowledge.getAssignConfig();
            if (!VariableAssignTypeEnum.isConstant(assignConfig.getAssignType())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值类型仅支持常量赋值");
            }
            if (StringUtils.isBlank(assignConfig.getConstantValue())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整");
            }
            String variableName = dependentResource.getVariableIdNameMap().get(assignConfig.getVariableId());
            if (StringUtils.isBlank(variableName)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整");
            }
            if (VariableTypeEnum.isNotDynamicVariable(dependentResource.getVarIdTypeMap().get(assignConfig.getVariableId()))) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值目标变量仅支持动态变量");
            }
        }


        // 校验和流程重复时, 不能选择最后一个流程
        if (BooleanUtils.isTrue(knowledge.getEnableRepeatStep())
                && CollectionUtils.isNotEmpty(knowledge.getRepeatStepIdList())) {
            // 最后一个流程
            Optional<StepPO> lastStep = dependentResource.getStepMap().values().stream()
                    .filter(item -> StepTypeEnum.MAIN.equals(item.getType()))
                    .max(Comparator.comparingInt(StepPO::getOrderNum));
            lastStep.ifPresent(step -> {
                if (knowledge.getRepeatStepIdList().contains(step.getId())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "不可设置与最后一个主流程重复");
                }
            });
        }
    }

    private void validAction(KnowledgePO knowledge, ActionNameResourceBO actionResource) {
        if (CollectionUtils.isNotEmpty(knowledge.getActionList())) {
            knowledge.getActionList().forEach(actionParam -> {
                if (Objects.isNull(actionParam.getActionType())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "动作类型不能为空");
                }
                if (!ActionCategoryEnum.ADD_WECHAT.equals(actionParam.getActionType()) && CollectionUtils.isEmpty(actionParam.getSourceIdList())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR,
                            String.format("%s动作配置不能为空", actionParam.getActionType().getDesc()));
                }
                switch (actionParam.getActionType()) {
                    case WHITE_LIST:
                        checkParam(knowledge, actionParam, actionResource.getGroupId2NameMap());
                        break;
                    case SEND_SMS:
                        checkParam(knowledge, actionParam, actionResource.getSmsTempId2NameMap());
                        break;
                    case ADD_TAG:
                        checkParam(knowledge, actionParam, actionResource.getTagId2NameMap());
                        break;
                    default:
                        break;
                }
            });
        }
    }

    private void checkParam(KnowledgePO knowledge, RuleActionParam actionParam, Map<Long, String> id2NameMap) {
        if (Objects.isNull(id2NameMap)) {
            return;
        }

        // 外呼任务配置的短信模板, 不校验
        if (ActionCategoryEnum.SEND_SMS.equals(actionParam.getActionType())
                && SmsTemplateSourceEnum.CALL_JOB.equals(actionParam.getSmsTemplateSource())) {
            return;
        }
        for (IdNamePair<Long, String> idNamePair : actionParam.getSourceIdList()) {
            if (id2NameMap.containsKey(idNamePair.getId())) {
                break;
            }
            if (actionParam.getSourceIdList().indexOf(idNamePair) == actionParam.getSourceIdList().size() - 1) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR,
                        String.format("%s动作配置不能为空", actionParam.getActionType().getDesc()));
            }
        }
    }

    private void updateDependResourceRef(KnowledgePO knowledge, DependentResourceBO resource) {
        // 先删除, 再添加
        sourceRefService.deleteSourceByRefId(knowledge.getBotId(), knowledge.getId());
        if (CollectionUtils.isNotEmpty(knowledge.getTriggerIntentIdList())) {
            Set<String> intentIdSet = new HashSet<>(knowledge.getTriggerIntentIdList());
            sourceRefService.saveSourceRef(buildParam(knowledge, SourceTypeEnum.INTENT, intentIdSet));
        }
        Set<String> dependVariableIdSet = knowledge.calDependVariableIdSet(resource);
        if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
            sourceRefService.saveSourceRef(buildParam(knowledge, SourceTypeEnum.VARIABLE, dependVariableIdSet));
        }
    }

    private void updateDependVariableRef(List<KnowledgePO> knowledgeList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        List<String> knowledgeIdList = knowledgeList.stream().map(KnowledgePO::getId).collect(Collectors.toList());
        sourceRefService.deleteSourceByRefIds(knowledgeList.get(0).getBotId(), knowledgeIdList, SourceTypeEnum.VARIABLE);
        List<SourceRefPO> knowledgeVarRefList = new ArrayList<>();
        knowledgeList.forEach(knowledge -> {
            Set<String> dependVariableIdSet = knowledge.calDependVariableIdSet(resource);
            knowledgeVarRefList.addAll(createVarRefList(knowledge, dependVariableIdSet));
        });
        sourceRefService.batchAddSourceRef(knowledgeVarRefList);
    }

    private List<SourceRefPO> createVarRefList(KnowledgePO knowledge, Collection<String> varIdList) {
        if (CollectionUtils.isEmpty(varIdList)) {
            return Collections.emptyList();
        }
        return varIdList.stream()
                .distinct()
                .map(varId -> {
                    SourceRefPO ref = new SourceRefPO();
                    ref.setBotId(knowledge.getBotId());
                    ref.setRefId(knowledge.getId());
                    ref.setRefLabel(knowledge.getLabel());
                    ref.setRefType(IntentRefTypeEnum.KNOWLEDGE);
                    ref.setSourceType(SourceTypeEnum.VARIABLE);
                    ref.setSourceId(varId);
                    return ref;
                }).collect(Collectors.toList());
    }

    private void batchUpdateDependResourceRef(Long botId, List<KnowledgePO> knowledgeList, DependentResourceBO resource, Long userId) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        // 先删除, 再添加
        List<String> knowledgeIdList = MyCollectionUtils.listToConvertList(knowledgeList, KnowledgePO::getId);
        sourceRefService.deleteSourceByRefIds(botId, knowledgeIdList);

        LocalDateTime now = LocalDateTime.now();
        List<SourceRefPO> sourceRefList = new ArrayList<>();
        for (KnowledgePO knowledge : knowledgeList) {
            if (CollectionUtils.isNotEmpty(knowledge.getTriggerIntentIdList())) {
                for (String intentId : knowledge.getTriggerIntentIdList()) {
                    SourceRefPO ref = new SourceRefPO();
                    ref.setBotId(botId);
                    ref.setSourceType(SourceTypeEnum.INTENT);
                    ref.setSourceId(intentId);
                    ref.setRefId(knowledge.getId());
                    ref.setRefType(IntentRefTypeEnum.KNOWLEDGE);
                    ref.setRefLabel(knowledge.getLabel());
                    ref.setCreateTime(now);
                    ref.setUpdateTime(now);
                    ref.setCreateUserId(userId);
                    ref.setUpdateUserId(userId);
                    sourceRefList.add(ref);
                }
            }
            Set<String> dependVariableIdSet = knowledge.calDependVariableIdSet(resource);
            if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
                for (String varId : dependVariableIdSet) {
                    SourceRefPO ref = new SourceRefPO();
                    ref.setBotId(botId);
                    ref.setSourceType(SourceTypeEnum.VARIABLE);
                    ref.setSourceId(varId);
                    ref.setRefId(knowledge.getId());
                    ref.setRefType(IntentRefTypeEnum.KNOWLEDGE);
                    ref.setRefLabel(knowledge.getLabel());
                    ref.setCreateTime(now);
                    ref.setUpdateTime(now);
                    ref.setCreateUserId(userId);
                    ref.setUpdateUserId(userId);
                    sourceRefList.add(ref);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(sourceRefList)) {
            sourceRefService.batchAddSourceRef(sourceRefList);
        }
    }

    private SourceRefBO buildParam(KnowledgePO knowledge, SourceTypeEnum sourceType, Set<String> sourceIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(knowledge.getBotId());
        sourceRefBO.setSourceType(sourceType);
        sourceRefBO.setSourceIdSet(sourceIdSet);
        sourceRefBO.setRefId(knowledge.getId());
        sourceRefBO.setRefLabel(knowledge.getLabel());
        sourceRefBO.setRefType(IntentRefTypeEnum.KNOWLEDGE);
        return sourceRefBO;
    }

    private void deleteSourceRef(Long botId, String knowledgeId) {
        deleteSourceRef(botId, Collections.singletonList(knowledgeId));
    }

    private void deleteSourceRef(Long botId, List<String> knowledgeIdList) {
        if (CollectionUtils.isEmpty(knowledgeIdList)) {
            return;
        }
        sourceRefService.deleteSourceByRefIdList(botId, knowledgeIdList, IntentRefTypeEnum.KNOWLEDGE);
    }

    private DependentResourceBO prepareDependentResource(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        return dependResourceService.generateByCondition(condition.intent().step().variable().group().knowledge().specialAnswerConfig());
    }

    private List<KnowledgePO> queryPOListByCondition(KnowledgeQueryVO condition) {
        if (Objects.isNull(condition.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }

        // 问答知识的过滤很麻烦, 直接全部都在内存中进行过滤了
        List<KnowledgePO> allKnowledgeList = getAllListByBotId(condition.getBotId())
                .stream().sorted(Comparator.comparing(KnowledgePO::getUpdateTime).reversed()).collect(Collectors.toList());

        Set<String> filterIntentIdSet = new HashSet<>();
        if (StringUtils.isNotBlank(condition.getSearch())) {
            IntentQuery intentQuery = IntentQuery.builder()
                    .botId(condition.getBotId())
                    .keyword(condition.getSearch())
                    .build();
            List<IntentVO> intentQueryResult = intentService.listWithoutPages(intentQuery);
            filterIntentIdSet.addAll(intentQueryResult.stream().map(IntentVO::getId).collect(Collectors.toSet()));
        }

        // 过滤分组
        List<String> groupIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getGroupId())) {
            groupIdList.addAll(groupService.listCurrentAndDescendantGroupId(condition.getGroupId()));
        }

        List<KnowledgePO> filterList = allKnowledgeList.stream()
                // 过滤类别
                .filter(knowledge -> filterKnowledgeCategory(condition, knowledge))
                // 过滤答案回答后操作
                .filter(knowledge -> filterAnswerPostAction(condition, knowledge))
                // 过滤搜索项
                .filter(knowledge -> filterSearchContent(condition, filterIntentIdSet, knowledge))
                // 过滤分组
                .filter(knowledge -> filterGroup(condition, groupIdList, knowledge))
                // 过滤意向等级
                .filter(knowledge -> filterIntentLevelDetailCode(condition, knowledge))
                // 过滤动作配置
                .filter(knowledge -> filterActionType(condition, knowledge))
                // 过滤关注点
                .filter(knowledge -> filterCustomerConcern(condition, knowledge))
                .sorted(Comparator.comparing(KnowledgePO::getUpdateTime).reversed())
                .collect(Collectors.toList());
        return filterList;
    }

    @Override
    public PageResultObject<KnowledgeVO> queryByCondition(KnowledgeQueryVO condition) {
        List<KnowledgePO> filterList = queryPOListByCondition(condition);
        int totalCount = CollectionUtils.size(filterList);

        List<KnowledgeVO> voList = convertPO2VO(condition.getBotId(), filterList);
        botStatsFacadeService.wrapKnowledgeStatsInfo(voList, condition);

        // 排序, 分页
        voList = voList.stream()
                .sorted(Comparator.comparingLong(item -> getStatsOrderValue(item, condition)))
                .skip(condition.getSkip())
                .limit(condition.getPageSize())
                .collect(Collectors.toList());
        return PageResultObject.of(voList, condition.getPageNum(), condition.getPageSize(), totalCount);
    }

    @Override
    public String exportKnowledge(KnowledgeBatchOperateRequestVO condition) {
        Query query = getQuery(condition);
        List<KnowledgePO> knowledgeList = mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
        try (SXSSFWorkbook workbook = new SXSSFWorkbook()){
            SXSSFSheet sheet = workbook.createSheet("问答知识答案");
            // 最多的答案数量
            int maxAnswerSize = knowledgeList.stream().map(KnowledgePO::getAnswerList).filter(Objects::nonNull).map(List::size).mapToInt(Integer::intValue).max().orElse(1);
            int rowNum = 0;
            sheet.setDefaultRowHeightInPoints(22);
            Map<Integer, String> titleMap = new HashMap<>();
            titleMap.put(0, "问答知识标题");
            sheet.setColumnWidth(0, 20 * 256);
            titleMap.put(1, "问答知识类型");
            sheet.setColumnWidth(1, 15 * 256);
            for (int i = 1; i <= maxAnswerSize; i++) {
                int answerIndex = i + 1;
                titleMap.put(answerIndex, "标准答案" + i);
                sheet.setColumnWidth(answerIndex, 50 * 256);
            }
            // 表头写入
            writeOneRow(sheet, rowNum++, titleMap);

            for (KnowledgePO knowledge : knowledgeList) {
                Map<Integer, String> rowMap = new HashMap<>();
                rowMap.put(0, knowledge.getName());
                rowMap.put(1, knowledge.getCategory().getDesc());
                List<KnowledgeAnswer> answerList = knowledge.getAnswerList();
                if (CollectionUtils.isNotEmpty(answerList)) {
                    for (int j = 0; j < answerList.size(); j++) {
                        rowMap.put(2 + j, answerList.get(j).getText());
                    }
                }
                // 写入单条知识
                writeOneRow(sheet, rowNum++, rowMap);
            }

            Long botId = condition.getBotId();
            String fileName = Optional.ofNullable(botService.getById(botId)).map(BotPO::getName).orElse("未知") + "-" + "知识库" + "-" + System.currentTimeMillis() + ".xlsx";
            return BotExportUtils.uploadFile(botId, fileName,
                    new Consumer<FileOutputStream>() {
                        @Override
                        @SneakyThrows
                        public void accept(FileOutputStream fileOutputStream) {
                            workbook.write(fileOutputStream);
                            IOUtils.closeQuietly(workbook, null);
                        }
                    });
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "知识库导出失败", e);
        }
    }

    private void writeOneRow(SXSSFSheet sheet, int rowNum, Map<Integer, String> rowMap) {
        SXSSFRow row = sheet.createRow(rowNum);
        rowMap.forEach((k, v) -> row.createCell(k).setCellValue(v));
    }

    private String getCellStringValue(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell);
    }

    @Override
    @SneakyThrows
    public KnowledgeImportResultVO importKnowledge(MultipartFile excel, Long botId, Long userId) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术不存在");
        }

        Workbook workbook = new XSSFWorkbook(excel.getInputStream());
        // 读取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 表头在第一行
        Row headerRow = sheet.getRow(0);
        if (Objects.isNull(headerRow)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "格式错误");
        }
        List<String> headerNameList = new ArrayList<>();
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            headerNameList.add(getCellStringValue(headerRow.getCell(i)));
        }
        log.info("headerNameList:{}", headerNameList);

        int columnSize = headerNameList.size();
        if (columnSize < 3) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "格式错误");
        }
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum == 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "数据不能为空");
        }
        if (lastRowNum > 1000) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "单次导入不能超过1000条");
        }

        Map<String, KnowledgePO> existsKnowledgeMap = MyCollectionUtils.listToMap(getAllListByBotId(botId), KnowledgePO::getName);
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        Map<String, String> intentIdNameMap = MyCollectionUtils.listToConvertMap(intentList, IntentPO::getId, IntentPO::getName);
        // 已经被引用的意图名称列表
        List<String> refIntentNameList = sourceRefService.getListBySourceType(botId, SourceTypeEnum.INTENT).stream()
                .filter(s -> IntentRefTypeEnum.STEP.equals(s.getRefType()) || IntentRefTypeEnum.KNOWLEDGE.equals(s.getRefType()) || IntentRefTypeEnum.SPECIAL_ANSWER.equals(s.getRefType()))
                .map(SourceRefPO::getSourceId).map(intentIdNameMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 新增的知识列表
        List<KnowledgeImportInfoBO> toBeCreatedKnowledgeList = new ArrayList<>();
        // 更新的知识列表
        List<KnowledgeImportInfoBO> toBeUpdatedKnowledgeList = new ArrayList<>();
        // 错误的知识列表
        List<KnowledgeImportInfoBO> errorList = new ArrayList<>();

        // 话术文案中包含的变量列表
        List<String> validVarNameList = new ArrayList<>();

        List<String> processedKnowledgeNameList = new ArrayList<>();
        for (int i = 1; i <= lastRowNum; i++) {
            KnowledgeImportInfoBO model = new KnowledgeImportInfoBO();
            model.setRowNum(i + 1);
            Row row = sheet.getRow(i);

            if (Objects.isNull(row)) {
                model.setError("数据为空");
                errorList.add(model);
                continue;
            }

            String name = StringUtils.trimToNull(getCellStringValue(row.getCell(0)));
            model.setName(name);
            String category = StringUtils.trimToNull(getCellStringValue(row.getCell(1)));
            model.setCategory(category);
            List<String> answerList = new ArrayList<>();
            for (int j = 2; j < columnSize; j++) {
                answerList.add(StringUtils.trimToNull(getCellStringValue(row.getCell(j))));
            }
            model.setAnswerList(answerList);
            // 话术文案中包含的变量集合
            List<Set<String>> variableSetList = answerList.stream()
                    .map(s -> StringUtils.isBlank(s) ? Collections.<String>emptySet() : new AnswerPlaceholderSplitter(s, false).getVariableSet())
                    .collect(Collectors.toList());
            model.setVariableSetList(variableSetList);

            String error = checkKnowledgeInfo(model, refIntentNameList, existsKnowledgeMap);
            if (Objects.nonNull(error)) {
                model.setError(error);
                errorList.add(model);
                continue;
            }
            if (processedKnowledgeNameList.contains(name)) {
                model.setError("导入文件中存在同标题问答知识");
                errorList.add(model);
                continue;
            }
            processedKnowledgeNameList.add(name);

            variableSetList.forEach(validVarNameList::addAll);

            if (existsKnowledgeMap.containsKey(name)) {
                toBeUpdatedKnowledgeList.add(model);
            } else {
                toBeCreatedKnowledgeList.add(model);
            }
        }

        // 日志
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.KNOWLEDGE, "批量导入开始", userId);

        // 批量创建变量
        if (CollectionUtils.isNotEmpty(validVarNameList)) {
            variableService.autoCreateIfNotExists(botId, validVarNameList, userId);
        }
        // 批量创建知识
        if (CollectionUtils.isNotEmpty(toBeCreatedKnowledgeList)) {
            batchCreateKnowledge(toBeCreatedKnowledgeList, botId, userId, intentList);
        }
        // 批量更新知识
        if (CollectionUtils.isNotEmpty(toBeUpdatedKnowledgeList)) {
            batchUpdate(botId, toBeUpdatedKnowledgeList, existsKnowledgeMap, userId);
        }

        // 修改话术状态
        updateBotToDraft(botId);

        // 生成错误信息的Excel
        String url = null;
        if (CollectionUtils.isNotEmpty(errorList)) {
            url = exportErrorInfoExcel(errorList, botId, bot.getName());
        }

        // 日志
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.KNOWLEDGE, "批量导入结束", userId);

        KnowledgeImportResultVO vo = new KnowledgeImportResultVO();
        vo.setSuccessNum(lastRowNum - errorList.size());
        vo.setFailNum(errorList.size());
        vo.setUrl(url);
        return vo;
    }

    private void batchUpdate(Long botId, List<KnowledgeImportInfoBO> toBeUpdatedList, Map<String, KnowledgePO> existsKnowledgeMap, Long userId) {
        List<KnowledgePO> knowledgeList = new ArrayList<>();

        for (KnowledgeImportInfoBO model : toBeUpdatedList) {
            KnowledgePO old = existsKnowledgeMap.get(model.getName());
            knowledgeList.add(old);

            old.setCategory(CodeDescEnum.getFromDescOrNull(KnowledgeCategoryEnum.class, model.getCategory()));
            List<String> newAnswerList = model.getAnswerList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<KnowledgeAnswer> oldAnswerList = old.getAnswerList();
            int newSize = newAnswerList.size();
            int oldSize = oldAnswerList.size();

            List<KnowledgeAnswer> finalAnswerList = new ArrayList<>(newSize);
            for (int i = 0; i < newSize; i++) {
                KnowledgeAnswer oldAnswer = null;
                if (i < oldSize) {
                    oldAnswer = oldAnswerList.get(i);
                }
                if (Objects.nonNull(oldAnswer)) {
                    oldAnswer.setText(newAnswerList.get(i));
                    finalAnswerList.add(oldAnswer);
                } else {
                    KnowledgeAnswer answer = new KnowledgeAnswer();
                    answer.setEnableUninterrupted(false);
                    answer.setPostAction(PostActionTypeEnum.WAIT);
                    answer.setText(newAnswerList.get(i));
                    answer.setEnableVarCondition(false);
                    finalAnswerList.add(answer);
                }
            }
            old.setAnswerList(finalAnswerList);

            generateLabel(old);
            mongoTemplate.save(old, KnowledgePO.COLLECTION_NAME);
        }

        DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable());
        batchUpdateDependResourceRef(botId, knowledgeList, dependentResource, userId);
    }


    private void batchCreateKnowledge(List<KnowledgeImportInfoBO> toBeCreatedKnowledgeList, Long botId, Long userId, List<IntentPO> intentList) {
        Map<String, String> intentNameIdMap = new HashMap<>(MyCollectionUtils.listToConvertMap(intentList, IntentPO::getName, IntentPO::getId));
        // 需要创建的问答知识名称列表
        List<String> toBeCreatedKnowledgeNameList = toBeCreatedKnowledgeList.stream().map(KnowledgeImportInfoBO::getName).distinct().collect(Collectors.toList());
        // 需要创建的意图名称列表
        List<String> toBeCreatedIntentNameList = toBeCreatedKnowledgeNameList.stream().filter(name -> !intentNameIdMap.containsKey(name)).collect(Collectors.toList());
        // 批量创建意图
        if (CollectionUtils.isNotEmpty(toBeCreatedIntentNameList)) {
            String rootIntentGroupId = groupService.getRootGroup(botId, GroupTypeEnum.SINGLE_INTENT).getId();
            List<IntentPO> createdIntentList = intentService.batchCreateSingleIntent(botId, toBeCreatedIntentNameList, rootIntentGroupId, userId);
            intentNameIdMap.putAll(MyCollectionUtils.listToMap(createdIntentList, IntentPO::getName, IntentPO::getId));
        }

        String rootKnowledgeGroupId = groupService.getRootGroup(botId, GroupTypeEnum.KNOWLEDGE).getId();
        LocalDateTime now = LocalDateTime.now();
        // 批量创建知识
        List<KnowledgePO> knowledgeList = new ArrayList<>();
        for (KnowledgeImportInfoBO model : toBeCreatedKnowledgeList) {
            KnowledgePO entity = new KnowledgePO();
            entity.setName(model.getName());
            entity.setBotId(botId);
            entity.setCategory(CodeDescEnum.getFromDescOrNull(KnowledgeCategoryEnum.class, model.getCategory()));
            List<KnowledgeAnswer> knowledgeAnswerList = model.getAnswerList().stream().filter(StringUtils::isNotBlank)
                    .map(text -> {
                        KnowledgeAnswer answer = new KnowledgeAnswer();
                        answer.setEnableUninterrupted(false);
                        answer.setPostAction(PostActionTypeEnum.WAIT);
                        answer.setText(text);
                        answer.setEnableVarCondition(false);
                        return answer;
                    }).collect(Collectors.toList());
            entity.setAnswerList(knowledgeAnswerList);
            entity.setEnableRepeatStep(false);
            entity.setTriggerIntentIdList(Collections.singletonList(intentNameIdMap.get(model.getName())));
            entity.setEnableIntentLevel(false);
            entity.setIsEnableAction(false);
            entity.setGroupId(rootKnowledgeGroupId);
            entity.setIsCustomerConcern(false);
            entity.setEnableAssign(false);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCreateUserId(userId);
            entity.setUpdateUserId(userId);
            knowledgeList.add(entity);
        }
        generateLabel(botId, knowledgeList);
        mongoTemplate.insertAll(knowledgeList);

        // 批量处理引用路径
        DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable());
        batchUpdateDependResourceRef(botId, knowledgeList, resource, userId);

        // 批量创建日志
        knowledgeOperationLogService.batchCreate(botId, knowledgeList, userId);
    }

    private String checkKnowledgeInfo(KnowledgeImportInfoBO model, List<String> refIntentNameList, Map<String, KnowledgePO> existsKnowledgeMap) {
        String name = model.getName();
        if (StringUtils.isBlank(name)) {
            return "问答知识标题不能为空，导入失败，请检查";
        }
        if (StringUtils.length(name) > 50) {
            return "问答知识标题超过50个字符，导入失败，请检查";
        }
        if (StringUtils.isBlank(model.getCategory())) {
            return "问答知识类型不能为空，导入失败，请检查";
        }
        if (Objects.isNull(CodeDescEnum.getFromDescOrNull(KnowledgeCategoryEnum.class, model.getCategory()))) {
            return "问答知识类型输入错误，仅可输入\"一般知识\"和\"业务知识\"，导入失败，请检查";
        }
        List<String> answerList = model.getAnswerList();
        if (StringUtils.isBlank(answerList.get(0))) {
            return "标准答案1不能为空，导入失败，请检查";
        }
        for (int i = 0; i < answerList.size(); i++) {
            String text = answerList.get(i);
            if (StringUtils.isBlank(text)) {
                continue;
            }
            if (StringUtils.length(text) > 500) {
                return "标准答案" + (i + 1) + "超过500个字符，导入失败，请检查";
            }
            // 校验文案中的变量是否合法
            Set<String> variableSet = model.getVariableSetList().get(i);
            if (CollectionUtils.isNotEmpty(variableSet) && !variableSet.stream().allMatch(variableService::isValidVarName)) {
                return "标准答案" + (i + 1) +"中变量命名不合规，导入失败，请检查";
            }
        }
        if (!existsKnowledgeMap.containsKey(name) && refIntentNameList.contains(name)) {
            return "同名意图已经被引用";
        }
        return null;
    }

    private String exportErrorInfoExcel(List<KnowledgeImportInfoBO> errorList, Long botId, String botName) {
        try (SXSSFWorkbook workbook = new SXSSFWorkbook()){
            SXSSFSheet sheet = workbook.createSheet("错误信息");
            int maxAnswerSize = errorList.stream().map(KnowledgeImportInfoBO::getAnswerList).filter(Objects::nonNull).map(List::size).mapToInt(Integer::intValue).max().orElse(1);
            int rowNum = 0;

            sheet.setDefaultRowHeightInPoints(22);
            Map<Integer, String> titleMap = new HashMap<>();
            titleMap.put(0, "行号");
            sheet.setColumnWidth(0, 10 * 256);
            titleMap.put(1, "问答知识标题");
            sheet.setColumnWidth(1, 20 * 256);
            titleMap.put(2, "问答知识类型");
            sheet.setColumnWidth(2, 15 * 256);
            for (int i = 1; i <= maxAnswerSize; i++) {
                int answerIndex = i + 2;
                titleMap.put(answerIndex, "标准答案" + i);
                sheet.setColumnWidth(answerIndex, 50 * 256);
            }
            int errorIndex = 3 + maxAnswerSize;
            titleMap.put(errorIndex, "错误信息");
            sheet.setColumnWidth(errorIndex, 30 * 256);
            writeOneRow(sheet, rowNum++, titleMap);

            for (KnowledgeImportInfoBO knowledge : errorList) {
                Map<Integer, String> rowMap = new HashMap<>();
                rowMap.put(0, String.valueOf(knowledge.getRowNum()));
                rowMap.put(1, knowledge.getName());
                rowMap.put(2, knowledge.getCategory());
                List<String> answerList = knowledge.getAnswerList();
                if (CollectionUtils.isNotEmpty(answerList)) {
                    for (int j = 0; j < answerList.size(); j++) {
                        rowMap.put(3 + j, answerList.get(j));
                    }
                }
                rowMap.put(errorIndex, knowledge.getError());
                writeOneRow(sheet, rowNum++, rowMap);
            }

            String fileName = botName + "-" + "知识库导入错误信息" + "-" + System.currentTimeMillis() + ".xlsx";
            return BotExportUtils.uploadFile(botId, fileName,
                    new Consumer<FileOutputStream>() {
                        @Override
                        @SneakyThrows
                        public void accept(FileOutputStream fileOutputStream) {
                            workbook.write(fileOutputStream);
                            IOUtils.closeQuietly(workbook, null);
                        }
                    });
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "知识库异常信息导出失败", e);
        }
    }

    private long getStatsOrderValue(KnowledgeVO knowledge, BaseStatsQuery condition) {
        long orderValue = getKnowledgeOrderValueByOrderBy(knowledge, condition);
        if (ApplicationConstant.SORT_DIRECTION_DESC.equals(condition.getDirection()) || StringUtils.isBlank(condition.getDirection())) {
            return -orderValue;
        } else {
            return orderValue;
        }
    }

    private long getKnowledgeOrderValueByOrderBy(KnowledgeVO knowledgeVO, BaseStatsQuery condition) {
        if (StringUtils.isBlank(condition.getOrderBy()) || StringUtils.isBlank(condition.getDirection())) {
            return knowledgeVO.getUpdateTime().toEpochSecond(ZoneOffset.ofHours(8));
        }
        if (condition.getOrderBy().contains("reachCallCount")) {
            return knowledgeVO.getStatsInfo().getReachCallCount();
        } else if (condition.getOrderBy().contains("declineCount")) {
            return knowledgeVO.getStatsInfo().getDeclineCount();
        } else if (condition.getOrderBy().contains("customerHangupCount")) {
            return knowledgeVO.getStatsInfo().getCustomerHangupCount();
        } else if (condition.getOrderBy().contains("label")) {
            String label = knowledgeVO.getLabel();
            if (StringUtils.isBlank(label)) {
                return 0L;
            } else {
                try {
                    return Long.parseLong(label.replace("Q", ""));
                } catch (NumberFormatException e) {
                    return 0L;
                }
            }
        }
        return knowledgeVO.getUpdateTime().toEpochSecond(ZoneOffset.ofHours(8));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeGroup(KnowledgeChangeGroupVO condition, Long userId) {
        Assert.notNull(condition.getBotId(), "botId不能为空");
        String targetGroupId = condition.getTargetGroupId();
        Assert.notNull(targetGroupId, "目标分组不能为空");
        GroupPO targetGroup = groupService.selectOne(targetGroupId, GroupTypeEnum.KNOWLEDGE, condition.getBotId());
        Assert.notNull(targetGroup, "目标分组不存在");
        List<KnowledgePO> filterList = mongoTemplate.find(getQuery(condition), KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(filterList)) {
            return;
        }
        for (KnowledgePO knowledgePO : filterList) {
            knowledgePO.setGroupId(targetGroupId);
            knowledgePO.setUpdateTime(LocalDateTime.now());
            knowledgePO.setUpdateUserId(userId);
            saveKnowledge(knowledgePO);
        }

        botService.updateAuditStatus(condition.getBotId(), AuditStatusEnum.DRAFT);

        operationLogService.save(condition.getBotId(), OperationLogTypeEnum.GROUP, OperationLogResourceTypeEnum.KNOWLEDGE_GROUP,
                String.format("移动知识%s至分组【%s】", filterList.stream().map(s -> String.format("【%s】", s.getName())).collect(Collectors.joining("、")), targetGroup.getPath()),
                userId);
    }

    private boolean filterSearchContent(KnowledgeQueryVO condition, Set<String> filterIntentIdSet, KnowledgePO knowledge) {
        if (StringUtils.isBlank(condition.getSearch())) {
            return true;
        }
        // 名称
        if (knowledge.getName().contains(condition.getSearch())) {
            return true;
        }
        // 答案
        Optional<KnowledgeAnswer> matchAnswer = knowledge.getAnswerList()
                .stream()
                .filter(answer -> StringUtils.isNotBlank(answer.getText()) && answer.getText().contains(condition.getSearch()))
                .findAny();
        if (matchAnswer.isPresent()) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(knowledge.getTriggerIntentIdList())) {
            return knowledge.getTriggerIntentIdList().stream()
                    .anyMatch(filterIntentIdSet::contains);
        }
        return false;
    }

    private boolean filterKnowledgeCategory(KnowledgeQueryVO condition, KnowledgePO knowledge) {
        if (Objects.nonNull(condition.getCategory())) {
            return condition.getCategory().equals(knowledge.getCategory());
        }
        return true;
    }

    private boolean filterAnswerPostAction(KnowledgeQueryVO condition, KnowledgePO knowledge) {
        if (CollectionUtils.isNotEmpty(condition.getFilterPostActionList())) {
            Set<PostActionTypeEnum> filterSet = new HashSet<>(condition.getFilterPostActionList());
            if (CollectionUtils.isEmpty(knowledge.getAnswerList())) {
                return false;
            }
            return knowledge.getAnswerList()
                    .stream()
                    .anyMatch(answer -> filterSet.contains(answer.getPostAction()));
        }
        return true;
    }

    private boolean filterGroup(KnowledgeQueryVO condition, List<String> groupIdList, KnowledgePO knowledge) {
        if (StringUtils.isNotBlank(condition.getGroupId())) {
            if (CollectionUtils.isEmpty(groupIdList)) {
                return false;
            } else {
                return groupIdList.contains(knowledge.getGroupId());
            }
        }
        return true;
    }

    private boolean filterIntentLevelDetailCode(KnowledgeQueryVO condition, KnowledgePO knowledge) {
        List<Integer> intentLevelDetailCodeList = condition.getIntentLevelDetailCodeList();
        if (CollectionUtils.isNotEmpty(intentLevelDetailCodeList)) {
            return Objects.nonNull(knowledge.getIntentLevelDetailCode()) && intentLevelDetailCodeList.contains(knowledge.getIntentLevelDetailCode());
        }
        return true;
    }

    private boolean filterActionType(KnowledgeQueryVO condition, KnowledgePO knowledge) {
        List<ActionCategoryEnum> actionTypeList = condition.getActionTypeList();
        if (CollectionUtils.isNotEmpty(actionTypeList)) {
            List<ActionCategoryEnum> currentActionTypeList = MyCollectionUtils.listToConvertList(knowledge.getActionList(), RuleActionParam::getActionType);
            return actionTypeList.stream().anyMatch(currentActionTypeList::contains);
        }
        return true;
    }

    private boolean filterCustomerConcern(KnowledgeQueryVO condition, KnowledgePO knowledge) {
        if (Objects.nonNull(condition.getIsCustomerConcern())) {
            return condition.getIsCustomerConcern().equals(knowledge.getIsCustomerConcern());
        }
        return true;
    }

    @Override
    public List<KnowledgePO> getAllListByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        return mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    @Override
    public KnowledgePO getById(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id))
                .addCriteria(Criteria.where("botId").is(botId));
        return mongoTemplate.findOne(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    @Override
    public KnowledgeVO getVOById(Long botId, String id) {
        return convertPO2VO(getById(botId, id));
    }

    @Override
    public List<KnowledgePO> getByIdList(Long botId, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(idList))
                .addCriteria(Criteria.where("botId").is(botId));
        return mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    @Override
    public KnowledgePO deleteById(Long botId, String knowledgeId, Long userId) {
        KnowledgePO old = getById(botId, knowledgeId);
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "知识不存在");
        }

        doBatchDelete(botId, Collections.singletonList(old), userId);
        return old;
    }

    @Override
    public List<KnowledgePO> batchDelete(KnowledgeBatchOperateRequestVO condition, Long userId) {
        Query query = getQuery(condition);
        List<KnowledgePO> result = mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
        doBatchDelete(condition.getBotId(), result, userId);
        return result;
    }

    @Override
    public List<KnowledgePO> deleteByBotIdsAndKnowledgeIdList(List<Long> botIdList, List<String> knowledgeIdList, Long userId) {
        if (CollectionUtils.isEmpty(botIdList) || CollectionUtils.isEmpty(knowledgeIdList)) {
            return Collections.emptyList();
        }

        List<KnowledgePO> knowledgeList = getByIdList(knowledgeIdList);
        // 按照botId分组, 并简单校验下 botId 是否超出范围了
        Map<Long, List<KnowledgePO>> botKnowledgeListMap = MyCollectionUtils.listToMapList(knowledgeList, KnowledgePO::getBotId);
        for (Long botId : botKnowledgeListMap.keySet()) {
            if (!botIdList.contains(botId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不匹配" + botId);
            }
        }

        botKnowledgeListMap.forEach((botId, list) -> doBatchDelete(botId, list, userId));
        return knowledgeList;
    }

    private void doBatchDelete(Long botId, List<KnowledgePO> deleteKnowledgeList, Long userId) {
        if (CollectionUtils.isEmpty(deleteKnowledgeList)) {
            return;
        }
        List<String> idList = deleteKnowledgeList.stream().map(KnowledgePO::getId).collect(Collectors.toList());
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(idList))
                .addCriteria(Criteria.where("botId").is(botId));
        mongoTemplate.remove(query, KnowledgePO.COLLECTION_NAME);
        deleteSourceRef(botId, idList);
        updateBotToDraft(botId);
        knowledgeOperationLogService.createDeleteOperationLog(botId, deleteKnowledgeList, userId);
    }

    private Query getQuery(KnowledgeBatchOperateRequestVO condition) {
        if (Objects.isNull(condition.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(condition.getBotId()));
        if (BooleanUtils.isTrue(condition.getSelectAll())) {
            List<KnowledgePO> allSelectedList = queryPOListByCondition(condition);
            List<String> idList = allSelectedList.stream().map(KnowledgePO::getId).collect(Collectors.toList());
            condition.setKnowledgeIdList(idList);
        } else if (CollectionUtils.isEmpty(condition.getKnowledgeIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识列表不能为空");
        }
        query.addCriteria(Criteria.where("_id").in(condition.getKnowledgeIdList()));
        return query;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<KnowledgePO> knowledgePOList = getAllListByBotId(context.getSrcBotId());
        context.getSnapshot().setKnowledgeList(knowledgePOList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        if (CollectionUtils.isEmpty(context.getSnapshot().getKnowledgeList())) {
            return;
        }

        // 校验知识的挂机话术是否回答后操作为挂机
        context.getInvalidMsgList().addAll(hangupAnswerAndActionValidateService.validateKnowledge(context.getSnapshot().getKnowledgeList()));

        context.getSnapshot().getKnowledgeList().forEach(knowledge -> {
            try {
                validAndThrow(knowledge, context.getDependentResource(), context.getActionNameResource(), context.getValidateConfig());
            } catch (ComException e) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.KNOWLEDGE)
                        .resourceId(knowledge.getId())
                        .resourceName(knowledge.getName())
                        .resourceLabel(knowledge.getLabel())
                        .failMsg(String.format("%s[%s]: %s", knowledge.getName(), knowledge.getLabel(), e.getDetailMsg()))
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        });

        // 按键采集节点关联的问答知识，回到后操作不支持配置等待用户应答
        validKeyCaptureNodeUninterruptedReplyKnowledge(context);
    }

    private void validKeyCaptureNodeUninterruptedReplyKnowledge(RobotResourceContext context) {
        RobotSnapshotPO snapshot = context.getSnapshot();
        Set<String> knowledgeIdSet = new HashSet<>();
        for (DialogBaseNodePO node : snapshot.getNodeList()) {
            if (node instanceof DialogKeyCaptureNodePO) {
                List<String> uninterruptedReplyKnowledgeIdList = node.getUninterruptedReplyKnowledgeIdList();
                if (CollectionUtils.isNotEmpty(uninterruptedReplyKnowledgeIdList)) {
                    knowledgeIdSet.addAll(uninterruptedReplyKnowledgeIdList);
                }
            }
        }
        Map<String, KnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(snapshot.getKnowledgeList(), KnowledgePO::getId);
        for (String knowledgeId : knowledgeIdSet) {
            KnowledgePO knowledge = knowledgeMap.get(knowledgeId);
            if(Objects.isNull(knowledge)) {
                continue;
            }
            List<KnowledgeAnswer> answerList = knowledge.getAnswerList();
            if (CollectionUtils.isNotEmpty(answerList) && answerList.stream().anyMatch(answer -> PostActionTypeEnum.WAIT.equals(answer.getPostAction()))) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.KNOWLEDGE)
                        .resourceId(knowledge.getId())
                        .resourceName(knowledge.getName())
                        .resourceLabel(knowledge.getLabel())
                        .failMsg(String.format("%s[%s]: %s", knowledge.getName(), knowledge.getLabel(), "被按键采集节点关联，回答后操作不支持配置[等待用户应答]"))
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        }
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long targetBotId = context.getTargetBotId();
        Long currentUserId = context.getCurrentUserId();
        List<Integer> intentTagDetailCodeList = context.getIntentTagDetailCodeList();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> stepIdMapping = mapping.getStepIdMapping();
        Map<String, String> intentIdMapping = mapping.getIntentIdMapping();
        Map<String, String> specialAnswerIdMapping = mapping.getSpecialAnswerIdMapping();
        List<KnowledgePO> knowledgeList = context.getSnapshot().getKnowledgeList();
        Map<String, String> groupIdMapping = mapping.getGroupIdMapping();

        if (context.isCopy()) {
            Map<String, String> knowledgeIdMapping = mapping.getKnowledgeIdMapping();
            knowledgeList.forEach(knowledgePO -> {
                String oldId = knowledgePO.getId();
                String newId = new ObjectId().toString();
                knowledgeIdMapping.put(oldId, newId);
            });

            for (KnowledgePO knowledge : knowledgeList) {
                knowledge.setId(knowledgeIdMapping.get(knowledge.getId()));
                knowledge.setBotId(targetBotId);
                knowledge.setCreateTime(LocalDateTime.now());
                knowledge.setUpdateTime(LocalDateTime.now());
                knowledge.setCreateUserId(currentUserId);
                knowledge.setUpdateUserId(currentUserId);

                knowledge.setGroupId(groupIdMapping.get(knowledge.getGroupId()));

                // 更新引用的意图信息
                if (CollectionUtils.isNotEmpty(knowledge.getTriggerIntentIdList())) {
                    List<String> newIntentIdList = knowledge.getTriggerIntentIdList().stream()
                            .map(intentIdMapping::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    knowledge.setTriggerIntentIdList(newIntentIdList);
                } else {
                    knowledge.setTriggerIntentIdList(Collections.emptyList());
                }

                if (CollectionUtils.isNotEmpty(knowledge.getRepeatStepIdList())) {
                    List<String> newStepIdList = knowledge.getRepeatStepIdList().stream()
                            .map(stepIdMapping::get)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    knowledge.setRepeatStepIdList(newStepIdList);
                }

                if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                    for (KnowledgeAnswer knowledgeAnswer : knowledge.getAnswerList()) {
                        knowledgeAnswer.setJumpStepId(stepIdMapping.get(knowledgeAnswer.getJumpStepId()));
                        if (CollectionUtils.isNotEmpty(knowledgeAnswer.getUninterruptedReplyStepIdList())) {
                            List<String> newStepIdList = knowledgeAnswer.getUninterruptedReplyStepIdList().stream()
                                    .map(stepIdMapping::get)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            knowledgeAnswer.setUninterruptedReplyStepIdList(newStepIdList);
                        }
                        if (CollectionUtils.isNotEmpty(knowledgeAnswer.getUninterruptedReplyKnowledgeIdList())) {
                            List<String> newKnowledgeIdList = knowledgeAnswer.getUninterruptedReplyKnowledgeIdList().stream()
                                    .map(knowledgeIdMapping::get)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            knowledgeAnswer.setUninterruptedReplyKnowledgeIdList(newKnowledgeIdList);
                        }
                        if (CollectionUtils.isNotEmpty(knowledgeAnswer.getUninterruptedReplySpecialAnswerIdList())) {
                            List<String> newIdList = knowledgeAnswer.getUninterruptedReplySpecialAnswerIdList().stream()
                                    .map(specialAnswerIdMapping::get)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            knowledgeAnswer.setUninterruptedReplySpecialAnswerIdList(newIdList);
                        }
                    }
                }

                // 更新答案列表关联的变量id
                if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                    for (KnowledgeAnswer knowledgeAnswer : knowledge.getAnswerList()) {
                        mapping.mapConditionGroupVarId(knowledgeAnswer);
                    }
                }
                //更新动作id
                if (context.isCopyFromCommonBotToMagicTemplateBot()) {
                    // 旗舰版BOT复制到模板BOT要删除所有动作配置
                    knowledge.setIsEnableAction(false);
                    knowledge.setActionList(Collections.emptyList());
                } else {
                    if (knowledge.getIsEnableAction() != null && knowledge.getIsEnableAction()
                            && CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                        if (SystemEnum.isOPE(context.getSystemEnum())) {
                            knowledge.getActionList().forEach(action -> action.setSourceIdList(null));
                        }
                    }
                }
                //更新意向等级
                if (Objects.nonNull(knowledge.getIntentLevelDetailCode())
                        && !intentTagDetailCodeList.contains(knowledge.getIntentLevelDetailCode())) {
                    knowledge.setIntentLevelDetailCode(null);
                }

                // 更新动态变量赋值
                if (BooleanUtils.isTrue(knowledge.getEnableAssign())
                        && Objects.nonNull(knowledge.getAssignConfig())) {
                    mapping.mapAssignConfig(knowledge.getAssignConfig());
                } else {
                    knowledge.setAssignConfig(null);
                }
            }

            generateLabel(targetBotId, knowledgeList);
            mongoTemplate.insert(knowledgeList, KnowledgePO.COLLECTION_NAME);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(IntentServiceImpl.class, StepServiceImpl.class, VariableServiceImpl.class, GroupServiceImpl.class, EntityServiceImpl.class, SpecialAnswerConfigServiceImpl.class);
    }

    @Override
    public void updateDependVariableName(Long botId, String variableId, String oldVariableName, String newVariableName) {
        List<KnowledgePO> knowledgeList = getAllListByBotId(botId);
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            List<KnowledgePO> updateList = knowledgeList.stream()
                    .filter(item -> {
                        AtomicBoolean atomicBoolean = new AtomicBoolean(false);
                        item.getAnswerList().forEach(answer -> {
                            if (answer.updateVariableName(oldVariableName, newVariableName)) {
                                atomicBoolean.set(true);
                            }
                        });
                        return atomicBoolean.get();
                    }).collect(Collectors.toList());

            updateList.forEach(this::saveKnowledge);
        }
    }

    @Override
    public List<KnowledgePO> getByDependStepId(Long botId, String stepId) {
        return getAllListByBotId(botId)
                .stream()
                .filter(knowledge -> isDepend(knowledge, stepId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Tuple2<KnowledgePO, List<String>>> getByDependStepIdList(Long botId, List<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }
        return getAllListByBotId(botId)
                .stream()
                .map(knowledge -> {
                    List<String> dependStepIdList = stepIdList.stream()
                            .filter(stepId -> isDepend(knowledge, stepId))
                            .collect(Collectors.toList());
                    return Tuple.of(knowledge, dependStepIdList);
                })
                .filter(tuple -> CollectionUtils.isNotEmpty(tuple._2()))
                .collect(Collectors.toList());
    }

    private static boolean isDepend(KnowledgePO knowledge, String stepId) {
        boolean answerDependsOnStep = false;
        boolean repeatDependsOnStep = false;
        if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
            answerDependsOnStep = knowledge.getAnswerList().stream()
                    .anyMatch(answer -> PostActionTypeEnum.SPECIFIED_STEP.equals(answer.getPostAction())
                            && stepId.equals(answer.getJumpStepId()));
        }
        if (BooleanUtils.isTrue(knowledge.getEnableRepeatStep())
                && CollectionUtils.isNotEmpty(knowledge.getRepeatStepIdList())) {
            repeatDependsOnStep = knowledge.getRepeatStepIdList().contains(stepId);
        }
        return answerDependsOnStep || repeatDependsOnStep;
    }

    @Override
    public void updateAnswerList(KnowledgePO knowledge) {
        if (Objects.isNull(knowledge)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识不能为空");
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(knowledge.getBotId()))
                .addCriteria(Criteria.where("_id").is(knowledge.getId()));
        // 尽量只保存必要的字段, 尽量避免同时修改带来的脏写问题
        Update update = new Update();
        update.set("answerList", knowledge.getAnswerList());
        mongoTemplate.updateFirst(query, update, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    @Override
    public void updateActionList(KnowledgePO knowledge) {
        if (Objects.isNull(knowledge)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(knowledge.getBotId()))
                .addCriteria(Criteria.where("_id").is(knowledge.getId()));
        // 尽量只保存必要的字段, 尽量避免同时修改带来的脏写问题
        Update update = new Update();
        update.set("actionList", knowledge.getActionList());
        mongoTemplate.updateFirst(query, update, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    /**
     * 知识同步
     *
     * @param syncVO
     */
    @Override
    public BotSyncResultVO sync(KnowledgeSyncVO syncVO) {
        Assert.notNull(syncVO.getTargetBotIdList(), "目标bot不能为空");
        Assert.notNull(syncVO.getSrcBotId(), "源botId不能为空");
        List<KnowledgePO> sourceKnowledgeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(syncVO.getKnowledgeQuery().getKnowledgeList())) {
            sourceKnowledgeList = getByIdList(syncVO.getKnowledgeQuery().getKnowledgeList());
        } else {
            sourceKnowledgeList = queryPOListByCondition(syncVO.getKnowledgeQuery());
        }
        Assert.notNull(sourceKnowledgeList, "待同步的知识不能为空");
        if (syncVO.getTargetBotIdList().contains(syncVO.getSrcBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标bot不能包含自己");
        }
        AtomicInteger successNum = new AtomicInteger(0);
        AtomicInteger failNum = new AtomicInteger(0);
        List<String> failBotNameList = new ArrayList<>();
        BotQuery query = new BotQuery();
        query.setBotIdList(syncVO.getTargetBotIdList());
        List<BotVO> botVOList = botService.queryListWithoutPage(query);
        Map<Long, BotVO> targetIdToBotMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(botVOList)) {
            targetIdToBotMap = botVOList.stream().collect(Collectors.toMap(BotVO::getBotId, botVO -> botVO));
        }

        //准备源bot的资源
        Map<String, List<IntentPO>> sourceNameToIntentPOMap = new HashMap<>();
        sourceKnowledgeList.forEach(sourceKnowledge -> {
            List<IntentPO> sourceIntentPOList = intentService.getByIdList(sourceKnowledge.getTriggerIntentIdList());
            sourceNameToIntentPOMap.put(sourceKnowledge.getName(), sourceIntentPOList);

        });
        BotVO sourceBotVO = botService.detail(syncVO.getSrcBotId());
        List<VariablePO> sourceVariablePOList = variableService.getListByBotId(syncVO.getSrcBotId());
        Map<String, String> SourceIdToVariableNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sourceVariablePOList)) {
            SourceIdToVariableNameMap = sourceVariablePOList.stream().collect(Collectors.toMap(VariablePO::getId, VariablePO::getName));
        }
        SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO = SourceKnowledgeSyncDTO.builder().
                sourceNameToIntentPOMap(sourceNameToIntentPOMap).
                sourceBotVO(sourceBotVO)
                .SourceIdToVariableNameMap(SourceIdToVariableNameMap)
                .sourceDependResource(prepareDependentResource(syncVO.getSrcBotId()))
                .preCreateIntetnIdList(new HashSet<>())
                .build();

        //对每个目标bot逐一同步
        Map<Long, BotVO> finalTargetIdToBotMap = targetIdToBotMap;
        List<KnowledgePO> finalSourceKnowledgeList = sourceKnowledgeList;
        List<Long> successBotIdList = new ArrayList<>();
        List<Long> failBotIdList = new ArrayList<>();
        syncVO.getTargetBotIdList().forEach(botId -> {
            try {
                List<KnowledgePO> knowledgePOList = getAllListByBotId(botId);
                //同步音频
                try {
                    if (syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.AUDIO)) {
                        answerAudioManagerService.copyAudioByAnswerSource(syncVO.getSrcBotId(), botId, finalSourceKnowledgeList, syncVO.getCurrentUserId());
                    }
                } catch (Exception e) {
                }
                //获取目标bot的知识映射(name->KnowledgePO)
                Map<String, KnowledgePO> nameKnowledgePOMap = knowledgePOList.stream().collect(Collectors.toMap(KnowledgePO::getName, knowledgePO -> knowledgePO));
                DependentResourceBO targetDependResource = prepareDependentResource(botId);
                //对需同步知识进行逐一同步
                finalSourceKnowledgeList.forEach(sourceKnowledge -> {
                    if (nameKnowledgePOMap.containsKey(sourceKnowledge.getName())) {
                        //知识名相同
                        if (SyncModeEnum.COVER.equals(syncVO.getSameKnowledge())) {
                            syncBySameName(sourceKnowledgeSyncDTO, DeepCopyUtils.copyObject(sourceKnowledge), nameKnowledgePOMap.get(sourceKnowledge.getName()), botId, syncVO, targetDependResource);
                        }
                    } else {
                        //知识名不同
                        syncByDiffName(sourceKnowledgeSyncDTO, DeepCopyUtils.copyObject(sourceKnowledge), botId, syncVO, targetDependResource);
                    }
                });
                updateBotToDraft(botId);
                successNum.getAndIncrement();
                successBotIdList.add(botId);
            } catch (Exception e) {
                log.error("同步知识至botId:{}失败:{}", botId, e);
                failNum.getAndIncrement();
                failBotNameList.add(finalTargetIdToBotMap.get(botId).getName());
                failBotIdList.add(botId);
            }
        });

        List<String> srcKnowledgeIdList = sourceKnowledgeList.stream().map(KnowledgePO::getId).collect(Collectors.toList());
        botSyncOperationLogService.knowledgeSync(syncVO, srcKnowledgeIdList, successBotIdList, failBotIdList);

        return BotSyncResultVO.builder().successNum(successNum.get()).failNum(failNum.get()).failBotNameList(failBotNameList).build();
    }

    @Override
    public void resetAllKnowledgeLabel(Long botId) {
        List<KnowledgePO> knowledgeList = getAllListByBotId(botId);
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        for (KnowledgePO knowledge : knowledgeList) {
            knowledge.setLabel(null);
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                for (BaseAnswerContent answer : knowledge.getAnswerList()) {
                    answer.setLabel(null);
                }
            }
        }
        generateLabel(botId, knowledgeList);
        for (KnowledgePO knowledge : knowledgeList) {
            saveKnowledge(knowledge);
        }
    }

    @Override
    public void resetResourceReferenceInfo(Long newBotId) {
        List<KnowledgePO> knowledgeList = getAllListByBotId(newBotId);
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        for (KnowledgePO knowledge : knowledgeList) {
            updateDependResourceRef(knowledge, prepareDependentResource(newBotId));
        }
    }

    @Override
    public void deleteByNameList(Long botId, List<String> knowledgeNameList) {
        if (Objects.isNull(botId) || CollectionUtils.isEmpty(knowledgeNameList)) {
            return;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("name").in(knowledgeNameList));
        List<KnowledgePO> knowledgeList = mongoTemplate.findAllAndRemove(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);

        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            log.info("删除知识:{}成功", knowledgeList.stream().map(KnowledgePO::getName).collect(Collectors.joining(",")));
        } else {
            log.info("未删除任何知识");
        }
    }

    @Override
    public void updateAnswerListAndVariableRefInfo(Long botId, List<KnowledgePO> updateKnowledgeList, Long userId) {
        if (CollectionUtils.isEmpty(updateKnowledgeList)) {
            return;
        }
        updateKnowledgeList.forEach(this::updateAnswerList);
        updateDependVariableRef(updateKnowledgeList, prepareDependentResource(botId));
    }

    @Override
    public List<SimpleKnowledge> getSimpleKnowledgeListByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);

        List<KnowledgePO> knowledgeList = getAllListByBotId(botId);
        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        List<AnswerAudioMappingPO> answerAudioMappingList = answerAudioMappingService.getAllByBotId(botId, botAudioConfig.getRecordUserId(), botAudioConfig.getAudioType());
        Map<String, AnswerAudioMappingPO> textAudioMap = MyCollectionUtils.listToMap(answerAudioMappingList, AnswerAudioMappingPO::getText);


        return knowledgeList.stream()
                .map(knowledge -> {
                    SimpleKnowledge simpleKnowledge = MyBeanUtils.copy(knowledge, SimpleKnowledge.class);
                    simpleKnowledge.setDialogFlowId(dialogFlowId);
                    List<AnswerAudioDetail> answerList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                        for (int i = 0; i < knowledge.getAnswerList().size(); i++) {
                            KnowledgeAnswer knowledgeAnswer = knowledge.getAnswerList().get(i);
                            AnswerAudioDetail answerAudioDetail = new AnswerAudioDetail();
                            answerAudioDetail.setIndex(i);
                            answerAudioDetail.setLabel(knowledgeAnswer.getLabel());
                            answerAudioDetail.setText(knowledgeAnswer.getText());
                            AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(knowledgeAnswer.getText(), AudioTypeEnum.MIXTURE.equals(botAudioConfig.getAudioType()));
                            List<AnswerAudioElement> answerAudioElementList =  splitter.getTextPlaceholderList().stream()
                                    .map(item -> {
                                        AnswerAudioElement answerAudioElement = MyBeanUtils.copy(item, AnswerAudioElement.class);
                                        if (TextPlaceholderTypeEnum.TEXT.equals(item.getType())) {
                                            AnswerAudioMappingPO audioMapping = textAudioMap.get(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(item.getValue()));
                                            if (Objects.nonNull(audioMapping)) {
                                                answerAudioElement.setAudioKey(audioMapping.getUrl());
                                                answerAudioElement.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(audioMapping.getUrl()));
                                            }
                                        }
                                        return answerAudioElement;
                                    }).collect(Collectors.toList());
                            answerAudioDetail.setAnswerElementList(answerAudioElementList);
                            answerList.add(answerAudioDetail);
                        }
                    }
                    simpleKnowledge.setAnswerList(answerList);
                    return simpleKnowledge;
                }).collect(Collectors.toList());
    }

    @Override
    public void updateBySimpleKnowledge(SimpleKnowledge simpleKnowledge, Long userId) {
        // 更新节点
        // 来源为开放平台那边
        if (Objects.isNull(simpleKnowledge)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识信息不能为空");
        }
        if (StringUtils.isBlank(simpleKnowledge.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "知识id不能为空");
        }
        if (Objects.isNull(simpleKnowledge.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        // 仅支持更新节点答案信息
        KnowledgePO originKnowledge = getById(simpleKnowledge.getBotId(), simpleKnowledge.getId());
        if (Objects.isNull(originKnowledge)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "知识不存在");
        }
        if (CollectionUtils.isEmpty(originKnowledge.getAnswerList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案列表不能为空");
        }
        if (CollectionUtils.size(originKnowledge.getAnswerList()) != CollectionUtils.size(simpleKnowledge.getAnswerList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不支持新增或删除答案");
        }
        List<Tuple2<String, String>> answerChangeList = new ArrayList<>();
        for (int i = 0; i < originKnowledge.getAnswerList().size(); i++) {
            KnowledgeAnswer answer = originKnowledge.getAnswerList().get(i);
            AnswerAudioDetail answerAudioDetail = simpleKnowledge.getAnswerList().get(i);
            if (StringUtils.isBlank(answerAudioDetail.getText())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案文本不能为空");
            }
            if (!StringUtils.equals(answerAudioDetail.getText(), answer.getText())) {
                answerChangeList.add(Tuple.of(answer.getText(), answerAudioDetail.getText()));
                answer.setText(answerAudioDetail.getText());
            }
        }
        if (CollectionUtils.isNotEmpty(answerChangeList)) {
            updateAnswerList(originKnowledge);
            botService.updateAuditStatus(simpleKnowledge.getBotId(), AuditStatusEnum.DRAFT);
            // 添加操作日志
            String changeDetail = answerChangeList.stream()
                    .map(item -> String.format("【%s】修改为【%s】", item._1, item._2)).collect(Collectors.joining("; "));
            String detail = String.format("编辑%s问答知识【%s:%s】话术%s",
                    originKnowledge.getCategory().getDesc(), originKnowledge.getLabel(), originKnowledge.getName(), changeDetail);
            operationLogService.save(originKnowledge.getBotId(), OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.KNOWLEDGE, detail, userId);

            // 判断是否是ai合成音, 并自动提交合成任务
            Set<String> originalTextList = answerChangeList.stream().map(Tuple2::_2).collect(Collectors.toSet());
            ttsJobService.createIfComposeAudioType(originKnowledge.getBotId(), originalTextList, userId);
        }
    }

    @Override
    public List<SimpleKnowledge> queryByGroupName(Long botId, String groupName) {
        if (StringUtils.isBlank(groupName)) {
            return Collections.emptyList();
        }

        List<GroupPO> groupList = groupService.getListByBotId(botId).stream()
                .sorted(Comparator.comparingInt(GroupPO::getLevel))
                .collect(Collectors.toList());
        Set<String> groupIdSet = new HashSet<>();
        groupList.forEach(group -> {
            if (StringUtils.equals(groupName, group.getName())) {
                groupIdSet.add(group.getId());
            }
            if (groupIdSet.contains(group.getParentId())) {
                groupIdSet.add(group.getId());
            }
        });
        if (CollectionUtils.isEmpty(groupIdSet)) {
            return Collections.emptyList();
        }

        return getAllListByBotId(botId).stream()
                .filter(knowledge -> groupIdSet.contains(knowledge.getGroupId()))
                .map(knowledge -> MyBeanUtils.copy(knowledge, SimpleKnowledge.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<KnowledgePO> searchKnowledgeTitle(Long botId, String searchText) {
        if (StringUtils.isBlank(searchText)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("name").regex(searchText));
        return mongoTemplate.find(query, KnowledgePO.class, KnowledgePO.COLLECTION_NAME);
    }

    /**
     * 知识名相同情况同步
     *
     * @param sourcePO 待同步知识
     * @param targetPO 目标bot的同名知识
     * @param botId    目标botId
     */
    private void syncBySameName(SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO,
                                KnowledgePO sourcePO,
                                KnowledgePO targetPO,
                                Long botId,
                                KnowledgeSyncVO syncVO,
                                DependentResourceBO targetDependResource) {
        //同步意图
        targetPO.setCategory(sourcePO.getCategory());
        targetPO.setUpdateTime(LocalDateTime.now());
        //删除目标知识的意图关联
        sourceRefService.deleteSourceByRefIdList(targetPO.getBotId(), Lists.newArrayList(targetPO.getId()), IntentRefTypeEnum.KNOWLEDGE);
        targetPO.setTriggerIntentIdList(Collections.emptyList());
        syncIntent(sourceKnowledgeSyncDTO, botId, syncVO, sourcePO, targetPO);
        //答案话术
        if (syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.ANSWER)) {
            syncAnswer(sourceKnowledgeSyncDTO, sourcePO, botId, targetPO, syncVO, targetDependResource);
        }

        //其他设置
        if (syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.OTHER_CONFIG)) {
            syncOtherConfig(sourceKnowledgeSyncDTO, sourcePO, botId, targetPO, syncVO);
        }

        saveKnowledge(targetPO);
    }


    /**
     * 知识名不同情况同步
     *
     * @param knowledgePO 待同步知识
     * @param botId       目标botId
     */
    private void syncByDiffName(SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO,
                                KnowledgePO knowledgePO,
                                Long botId,
                                KnowledgeSyncVO syncVO,
                                DependentResourceBO targetDependResource) {
        //新建目标知识
        KnowledgePO targetKonwledgePO = MyBeanUtils.copy(knowledgePO, KnowledgePO.class);
        targetKonwledgePO.setId(null);
        targetKonwledgePO.setIntentLevelDetailCode(null);
        targetKonwledgePO.setEnableIntentLevel(false);
        targetKonwledgePO.setEnableRepeatStep(false);
        targetKonwledgePO.setRepeatStepIdList(null);
        targetKonwledgePO.setIsEnableAction(false);
        targetKonwledgePO.setActionList(null);
        targetKonwledgePO.setTriggerIntentIdList(null);
        targetKonwledgePO.setLabel(null);
        targetKonwledgePO.setGroupId(groupService.getRootGroup(botId, GroupTypeEnum.KNOWLEDGE).getId());
        targetKonwledgePO.setAnswerList(null);
        targetKonwledgePO.setUpdateTime(LocalDateTime.now());
        generateLabel(targetKonwledgePO);
        targetKonwledgePO.setBotId(botId);
        mongoTemplate.insert(targetKonwledgePO);
        //意图同步
        syncIntent(sourceKnowledgeSyncDTO, botId, syncVO, knowledgePO, targetKonwledgePO);
        //答案话术
        if (syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.ANSWER)) {
            syncAnswer(sourceKnowledgeSyncDTO, knowledgePO, botId, targetKonwledgePO, syncVO, targetDependResource);
        }
        //其他设置
        if (syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.OTHER_CONFIG)) {
            syncOtherConfig(sourceKnowledgeSyncDTO, knowledgePO, botId, targetKonwledgePO, syncVO);
        }
        saveKnowledge(targetKonwledgePO);
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(targetKonwledgePO.getBotId());
        updateDependResourceRef(targetKonwledgePO, dependResourceService.generateByCondition(condition.variable()));
    }

    private void syncOtherConfig(SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO, KnowledgePO sourcePO, Long botId, KnowledgePO targetPO, KnowledgeSyncVO syncVO) {
        if (CollectionUtils.isNotEmpty(syncVO.getSyncScopeEnumSet()) && syncVO.getSyncScopeEnumSet().contains(SyncScopeEnum.OTHER_CONFIG)) {
            targetPO.setIsCustomerConcern(sourcePO.getIsCustomerConcern());
            // 判断意向标签组是否一致
            BotVO targetBot = botService.detail(botId);
            BotVO sourceBot = sourceKnowledgeSyncDTO.getSourceBotVO();
            targetPO.setEnableIntentLevel(sourcePO.getEnableIntentLevel());
            if (!Objects.equals(targetBot.getIntentLevelTagId(), sourceBot.getIntentLevelTagId())) {
                //不一致清空知识关联的意向标签
                targetPO.setIntentLevelDetailCode(null);
            } else {
                targetPO.setIntentLevelDetailCode(sourcePO.getIntentLevelDetailCode());
            }
            //若开启了与主流程去重，需要判断主流程是否存在，只保留存在的主流程
            if (BooleanUtils.isTrue(sourcePO.getEnableRepeatStep())) {
                List<String> sourceRepeatStepIdList = sourcePO.getRepeatStepIdList();
                List<String> targetRepeatStepIdList = new ArrayList<>();
                List<StepPO> targetBotStepList = stepService.getAllListByBotId(botId);
                Map<String, StepPO> targetBotNameToStepMap = MyCollectionUtils.listToConvertMap(targetBotStepList, StepPO::getName, stepPO -> stepPO);
                if (CollectionUtils.isNotEmpty(targetBotStepList)) {
                    if (CollectionUtils.isNotEmpty(sourceRepeatStepIdList)) {
                        sourceRepeatStepIdList = sourceRepeatStepIdList.stream().filter(sourceStepId -> {
                            String sourceStepName = stepService.getNameById(sourceStepId);
                            if (targetBotNameToStepMap.containsKey(sourceStepName)) {
                                targetRepeatStepIdList.add(targetBotNameToStepMap.get(sourceStepName).getId());
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                        targetPO.setEnableRepeatStep(true);
                        targetPO.setRepeatStepIdList(targetRepeatStepIdList);
                    }
                }
            } else {
                targetPO.setEnableRepeatStep(false);
                targetPO.setRepeatStepIdList(null);
            }
            //先清空触发动作，在绑定新的
            targetPO.setIsEnableAction(false);
            targetPO.setActionList(null);
            if (CollectionUtils.isNotEmpty(sourcePO.getActionList())) {
                List<RuleActionParam> actionList = new ArrayList<>();
                actionList = sourcePO.getActionList();
                if (SystemEnum.isOPE(syncVO.getSystemType())) {
                    //ope保留动作类型，清空具体动作
                    actionList.forEach(ruleActionParam -> {
                        ruleActionParam.setSourceIdList(null);
                    });
                }
                targetPO.setIsEnableAction(true);
                targetPO.setActionList(actionList);
            }

            // 同步动态变量赋值
            if (BooleanUtils.isTrue(sourcePO.getEnableAssign())) {
                targetPO.setEnableAssign(true);
                KnowledgeAssignConfigPO srcAssignConfig = sourcePO.getAssignConfig();
                if (Objects.nonNull(srcAssignConfig)) {
                    KnowledgeAssignConfigPO targetAssignConfig = MyBeanUtils.copy(srcAssignConfig, KnowledgeAssignConfigPO.class);
                    targetPO.setAssignConfig(targetAssignConfig);
                    targetAssignConfig.setEntityId(null);
                    // 这里也需要在同步前处理好变量的同步, 不需要在这里在复制了
                    targetAssignConfig.setVariableId(variableService.copyVariableByNameIfAbsent(sourcePO.getBotId(),
                            targetPO.getBotId(), targetAssignConfig.getVariableId(), syncVO.getCurrentUserId()));
                }
            } else {
                targetPO.setEnableAssign(false);
                targetPO.setAssignConfig(null);
            }
        }
    }

    /**
     * 答案同步
     *
     * @param sourcePO 源知识
     * @param botId    目标botId
     * @param targetPO 目标知识
     * @param syncVO   同步参数
     */
    private void syncAnswer(SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO,
                            KnowledgePO sourcePO,
                            Long botId,
                            KnowledgePO targetPO,
                            KnowledgeSyncVO syncVO,
                            DependentResourceBO targetDependResource) {

        //获取目标bot的变量集合
        List<VariablePO> targetVariableList = variableService.getListByBotId(botId);
        List<String> targetVariableNameList = MyCollectionUtils.listToConvertList(targetVariableList, VariablePO::getName);
        //获取源知识答案
        List<KnowledgeAnswer> sourceKnowledgeAnswerList = sourcePO.getAnswerList();
        //回答后操作
        if (CollectionUtils.isNotEmpty(sourceKnowledgeAnswerList)) {
            //获取目标bot的变量Map
            Map<String, VariablePO> newTargetVariableNameMap = MyCollectionUtils.listToMap(targetVariableList, VariablePO::getName);

            List<StepPO> targetBotStepList = stepService.getAllListByBotId(botId);
            if (CollectionUtils.isNotEmpty(targetBotStepList)) {
                Map<String, StepPO> targetBotStepNameMap = targetBotStepList.stream().collect(Collectors.toMap(StepPO::getName, stepPO -> stepPO));
                Set<String> targetKnowledgeDenpendVariableSet = new HashSet<>();
                sourceKnowledgeAnswerList.forEach(sourceKnowledgeAnswer -> {
                    if (PostActionTypeEnum.SPECIFIED_STEP.equals(sourceKnowledgeAnswer.getPostAction())) {
                        //如果跳转的主流程存在，则替换为目标bot对应的主流程，否则清空
                        String sourceStepName = stepService.getNameById(sourceKnowledgeAnswer.getJumpStepId());
                        if (targetBotStepNameMap.containsKey(sourceStepName)) {
                            sourceKnowledgeAnswer.setJumpStepId(targetBotStepNameMap.get(sourceStepName).getId());
                        } else {
                            sourceKnowledgeAnswer.setJumpStepId(null);
                        }
                    }
                    //绑定所需变量
                    Set<String> dependVariableIdSet = sourceKnowledgeAnswer.calDependsVariableIdSet(sourceKnowledgeSyncDTO.getSourceDependResource());
                    List<VariablePO> sourceKnowledgeDepnedVariableList = variableService.getByIdList(dependVariableIdSet);
                    Set<String> targetKnowledgeAnswerDependVariableIdList = new HashSet<>();
                    if (CollectionUtils.isNotEmpty(sourceKnowledgeDepnedVariableList)) {
                        sourceKnowledgeDepnedVariableList.forEach(sourceKnowledgeDepnedVariable -> {
                            VariablePO targetVariablePO = newTargetVariableNameMap.get(sourceKnowledgeDepnedVariable.getName());
                            if (Objects.isNull(targetVariablePO)) {
                                //自动创建不存在的变量
                                targetVariablePO = MyBeanUtils.copy(sourceKnowledgeDepnedVariable, VariablePO.class);
                                targetVariablePO.setId(null);
                                targetVariablePO.setBotId(botId);
                                targetVariablePO.setCustomerAttributeId(null);
                                mongoTemplate.insert(targetVariablePO);
                                newTargetVariableNameMap.put(targetVariablePO.getName(), targetVariablePO);
                            }
                            targetKnowledgeAnswerDependVariableIdList.add(targetVariablePO.getId());
                            targetKnowledgeDenpendVariableSet.add(targetVariablePO.getId());
                        });
                    }
                    //切换条件的变量
                    List<List<ConditionExpressionPO>> conditionList = sourceKnowledgeAnswer.getConditionList();
                    Map<String, String> sourceIdToVariableNameMap = sourceKnowledgeSyncDTO.getSourceIdToVariableNameMap();
                    if (CollectionUtils.isNotEmpty(conditionList)) {
                        for (List<ConditionExpressionPO> conditionExpressionPOS : conditionList) {
                            if (CollectionUtils.isNotEmpty(conditionExpressionPOS)) {
                                for (ConditionExpressionPO conditionExpressionPO : conditionExpressionPOS) {
                                    VariablePO targetBotPostVariablePO = newTargetVariableNameMap.get(sourceIdToVariableNameMap.get(conditionExpressionPO.getPostVarId()));
                                    if (Objects.nonNull(targetBotPostVariablePO)) {
                                        conditionExpressionPO.setPostVarId(targetBotPostVariablePO.getId());
                                    }
                                    VariablePO targetBotPreVariablePO = newTargetVariableNameMap.get(sourceIdToVariableNameMap.get(conditionExpressionPO.getPreVarId()));
                                    if (Objects.nonNull(targetBotPreVariablePO)) {
                                        conditionExpressionPO.setPreVarId(targetBotPreVariablePO.getId());
                                    }
                                }
                            }
                        }
                    }

                    /**
                     * key : src stepId, value: target stepId
                     * ...
                     */
                    Map<String, String> stepIdMapping = new HashMap<>();
                    Map<String, String> knowledgeIdMapping = new HashMap<>();
                    Map<String, String> specialAnswerIdMapping = new HashMap<>();

                    sourceKnowledgeSyncDTO.getSourceDependResource().getKnowledgeIdNameMap().forEach((srcId, name) -> {
                        String targetId = targetDependResource.getKnowledgeNameToIdMap().get(name);
                        if (StringUtils.isNotBlank(targetId)) {
                            knowledgeIdMapping.put(srcId, targetId);
                        }
                    });
                    sourceKnowledgeSyncDTO.getSourceDependResource().getStepIdNameMap().forEach((srcId, name) -> {
                        String targetId = targetDependResource.getStepNameToIdMap().get(name);
                        if (StringUtils.isNotBlank(targetId)) {
                            stepIdMapping.put(srcId, targetId);
                        }
                    });

                    sourceKnowledgeSyncDTO.getSourceDependResource().getSpecialAnswerIdNameMap().forEach((srcId, name) -> {
                        String targetId = targetDependResource.getSpecialAnswerNameToIdMap().get(name);
                        if (StringUtils.isNotBlank(targetId)) {
                            specialAnswerIdMapping.put(srcId, targetId);
                        }
                    });

                    sourceKnowledgeAnswer.mapUninterrupted(stepIdMapping, knowledgeIdMapping, specialAnswerIdMapping, Collections.emptyMap());
                });
                //将变量和知识的依赖关系绑定ref
                // todo 这里不会清空之前的绑定关系
                sourceRefService.saveSourceRef(buildParam(targetPO, SourceTypeEnum.VARIABLE, targetKnowledgeDenpendVariableSet));
            } else {
                sourceKnowledgeAnswerList.forEach(knowledgeAnswer -> {
                    knowledgeAnswer.setJumpStepId(null);
                });
            }
        }
        sourceKnowledgeAnswerList.forEach(knowledgeAnswer -> {
            knowledgeAnswer.setLabel(null);
        });
        labelGenerateService.answerLabel(botId, sourceKnowledgeAnswerList);
        targetPO.setAnswerList(sourceKnowledgeAnswerList);
    }

    /**
     * 同步意图
     *
     * @param botId    目标botId
     * @param syncVO   同步参数
     * @param sourcePO 源知识
     * @param targetPO 目标知识
     */
    private void syncIntent(SourceKnowledgeSyncDTO sourceKnowledgeSyncDTO, Long botId, KnowledgeSyncVO syncVO, KnowledgePO sourcePO, KnowledgePO targetPO) {
        List<IntentPO> sourceIntentPOList = sourceKnowledgeSyncDTO.getSourceNameToIntentPOMap().get(sourcePO.getName());
        List<String> sourceIntentPOIdList = sourceIntentPOList.stream().map(IntentPO::getId).collect(Collectors.toList());
        List<IntentPO> finalIntentPOList = new ArrayList<>();
        finalIntentPOList.addAll(sourceIntentPOList);
        if (CollectionUtils.isNotEmpty(finalIntentPOList)) {
            //添加组合意图里的单一意图
            Set<String> addIntentIdSet = Sets.newHashSet(MyCollectionUtils.listToConvertList(sourceIntentPOList, IntentPO::getId));
            Set<String> finalExtraList = new HashSet<>();
            finalIntentPOList.forEach(sourceIntentPO -> {
                if (IntentTypeEnum.COMPOSITE.equals(sourceIntentPO.getIntentType())) {
                    List<CompositeIntentCondition> compositeConditionList = sourceIntentPO.getCompositeConditionList();
                    if (CollectionUtils.isNotEmpty(compositeConditionList)) {
                        compositeConditionList.forEach(compositeIntentCondition -> {
                            List<String> intentIdList = compositeIntentCondition.getIntentIdList();
                            if (CollectionUtils.isNotEmpty(intentIdList)) {
                                addIntentIdSet.addAll(intentIdList);
                                intentIdList.forEach(id -> {
                                    if (!sourceIntentPOIdList.contains(id)) {
                                        finalExtraList.add(id);
                                    }
                                });
                            }
                        });
                    }
                }
            });
            List<IntentPO> extraIntentPOList = intentService.getByIdList(finalExtraList);
            finalIntentPOList.addAll(extraIntentPOList);
            List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.findByIntentIdIn(addIntentIdSet);
            //获取更新和新增的意图集合
            Tuple2<List<? extends IntentPO>, List<? extends IntentPO>> createAndUpdateIntentList = intentService.getCreateAndUpdateIntentList(botId, finalIntentPOList);
            //同步意图语料
            intentService.singleSync(botId, syncVO.getSameTriggerIntent(), syncVO.getCurrentUserId(), finalIntentPOList, intentCorpusPOList);
            if (CollectionUtils.isNotEmpty(createAndUpdateIntentList._1)) {
                List<String> collect = createAndUpdateIntentList._1.stream().map(IntentPO::getId).collect(Collectors.toList());
                sourceKnowledgeSyncDTO.getPreCreateIntetnIdList().addAll(collect);
            }
            //先处理更新意图集合中需要解绑的数据，并用addIntentIdList记录下更新意图中需要绑定到目标话术的意图
            Set<String> addIntentIdList = new HashSet<>();
            if (CollectionUtils.isNotEmpty(createAndUpdateIntentList._2)) {
                List<String> collect1 = sourceIntentPOList.stream().map(IntentPO::getId).collect(Collectors.toList());
                List<? extends IntentPO> collect = createAndUpdateIntentList._2.stream().filter(intentPO -> collect1.contains(intentPO.getId())).collect(Collectors.toList());
                List<IntentPO> finalCreateIntentList = new ArrayList<>();
                List<IntentPO> alreadyCreateIntentList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(sourceKnowledgeSyncDTO.getPreCreateIntetnIdList())) {
                    collect.forEach(intentPO -> {
                        if (!sourceKnowledgeSyncDTO.getPreCreateIntetnIdList().contains(intentPO.getId())) {
                            finalCreateIntentList.add(intentPO);
                        } else {
                            alreadyCreateIntentList.add(intentPO);
                        }
                    });
                } else {
                    finalCreateIntentList.addAll(collect);
                }
                if (!SyncModeEnum.COVER.equals(syncVO.getSameTriggerIntent())) {
                    //覆盖情况下intentService#singleSync会将createAndUpdateIntentList._2的意图替换成目标bot的同名意图,非覆盖情况下需要此处手动替换成目标bot的同名意图
                    List<String> nameList = MyCollectionUtils.listToConvertList(finalIntentPOList, IntentPO::getName);
                    Query query = Query.query(Criteria.where("botId").is(botId).and("name").in(nameList));
                    List<IntentPO> existsIntentPOList = mongoTemplate.find(query, IntentPO.class);
                    Map<String, String> nameMap = MyCollectionUtils.listToMap(existsIntentPOList, IntentPO::getNameAndTypeStr, IntentPO::getId);
                    finalCreateIntentList.forEach(item -> {
                        item.setId(nameMap.get(item.getNameAndTypeStr()));
                    });
                    alreadyCreateIntentList.forEach(item -> {
                        item.setId(nameMap.get(item.getNameAndTypeStr()));
                    });
                }
                alreadyCreateIntentList.forEach(intentPO -> {
                    addIntentIdList.add(intentPO.getId());
                });
                finalCreateIntentList.forEach(updatePO -> {
                    List<SourceRefPO> sourceIdList = sourceRefService.getBySourceIdList(botId, Lists.newArrayList(updatePO.getId()), SourceTypeEnum.INTENT);
                    if (CollectionUtils.isNotEmpty(sourceIdList)) {
                        sourceIdList.forEach(sourceRefPO -> {
                            Query query = Query.query(Criteria.where("botId").is(botId).and("id").is(sourceRefPO.getRefId()));
                            //如果该意图关联了非目标知识，需要删除该关联，并把该意图绑定到目标知识上
                            if (IntentRefTypeEnum.KNOWLEDGE.equals(sourceRefPO.getRefType()) && !sourceRefPO.getRefId().equals(targetPO.getId())) {
                                sourceRefService.delete(sourceRefPO.getSourceRefId());
                                //更新知识的triggerlist
                                KnowledgePO knowledgePO = mongoTemplate.findOne(query, KnowledgePO.class);
                                List<String> triggerIntentIdList = knowledgePO.getTriggerIntentIdList();
                                triggerIntentIdList.remove(sourceRefPO.getSourceId());
                                knowledgePO.setTriggerIntentIdList(triggerIntentIdList);
                                saveKnowledge(knowledgePO);
                                //绑定到目标知识
                                addIntentIdList.add(sourceRefPO.getSourceId());
                            } else if (IntentRefTypeEnum.STEP.equals(sourceRefPO.getRefType())) {
                                sourceRefService.delete(sourceRefPO.getSourceRefId());
                                //更新流程的triggerlist
                                StepPO stepPO = mongoTemplate.findOne(query, StepPO.class);
                                List<String> triggerIntentIdList = stepPO.getTriggerIntentIdList();
                                triggerIntentIdList.remove(sourceRefPO.getSourceId());
                                stepPO.setTriggerIntentIdList(triggerIntentIdList);
                                mongoTemplate.save(stepPO);
                                //绑定到目标知识
                                addIntentIdList.add(sourceRefPO.getSourceId());
                            } else if (IntentRefTypeEnum.SPECIAL_ANSWER.equals(sourceRefPO.getRefType())) {
                                sourceRefService.delete(sourceRefPO.getSourceRefId());
                                //更新特殊语境的triggerlist
                                SpecialAnswerConfigPO specialAnswerConfigPO = mongoTemplate.findOne(query, SpecialAnswerConfigPO.class);
                                List<String> triggerIntentIdList = specialAnswerConfigPO.getTriggerIntentIdList();
                                triggerIntentIdList.remove(sourceRefPO.getSourceId());
                                specialAnswerConfigPO.setTriggerIntentIdList(triggerIntentIdList);
                                mongoTemplate.save(specialAnswerConfigPO);
                                //绑定到目标知识
                                addIntentIdList.add(sourceRefPO.getSourceId());
                            } else {
                                //绑定到目标知识
                                addIntentIdList.add(sourceRefPO.getSourceId());
                            }
                        });
                    } else {
                        addIntentIdList.add(updatePO.getId());
                    }
                });
            }
            //将新增的意图和更新意图中需要绑定的目标知识的所有意图绑定到目标知识
            addIntentIdList.addAll(Sets.newHashSet(MyCollectionUtils.listToConvertList(createAndUpdateIntentList._1, IntentPO::getId)));
            if (CollectionUtils.isNotEmpty(addIntentIdList)) {
                HashSet<String> set = Sets.newHashSet(MyCollectionUtils.listToConvertList(extraIntentPOList, IntentPO::getId));
                set.forEach(id -> {
                    if (addIntentIdList.contains(id)) {
                        addIntentIdList.remove(id);
                    }
                });
                sourceRefService.saveSourceRef(buildParam(targetPO, SourceTypeEnum.INTENT, addIntentIdList));
                //更新目标知识的triggerlist
                targetPO.setTriggerIntentIdList(Lists.newArrayList(addIntentIdList));
            }
        }
    }

    private void validParam(KnowledgePO knowledge, boolean create) {
        if (!create && StringUtils.isBlank(knowledge.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id不能为空");
        }
        if (CollectionUtils.isEmpty(knowledge.getTriggerIntentIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "触发意图不能为空");
        }
        validNameDuplicate(knowledge.getBotId(), knowledge.getName(), knowledge.getId());
        SnapshotValidateConfigBO config = new SnapshotValidateConfigBO();
        validAndThrow(knowledge, prepareDependentResource(knowledge.getBotId()), null, config);
    }

    private void validNameDuplicate(Long botId, String name, String excludeId) {
        List<KnowledgePO> list = getAllListByBotId(botId);
        for (KnowledgePO item : list) {
            if (StringUtils.equals(item.getName(), name) && !StringUtils.equals(item.getId(), excludeId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("知识名称[%s]已存在", name));
            }
        }
    }

    @Override
    public List<String> listIdByBotIdAndGroupIds(Long botId, List<String> knowledgeGroupIdList) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("groupId").in(knowledgeGroupIdList));
        return mongoTemplate.findDistinct(query, "_id", KnowledgePO.COLLECTION_NAME, KnowledgePO.class, ObjectId.class)
                .stream().map(ObjectId::toString).collect(Collectors.toList());
    }

    @Override
    public List<SimpleKnowledgeStatsInfoVO> queryAllKnowledgeStatsInfo(KnowledgeQueryVO condition) {
        condition.setPageNum(1);
        condition.setPageSize(1000);
        PageResultObject<KnowledgeVO> pageInfo = queryByCondition(condition);
        return pageInfo.getContent().stream()
                .map(vo -> MyBeanUtils.copy(vo, SimpleKnowledgeStatsInfoVO.class))
                .collect(Collectors.toList());
    }
}
