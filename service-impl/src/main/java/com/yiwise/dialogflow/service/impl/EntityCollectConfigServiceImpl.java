package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.BaseEntityPO;
import com.yiwise.dialogflow.entity.po.EntityCollectConfigPO;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.impl.entitycollect.EntityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import static com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum.ENTITY_COLLECT_CONFIG;

@Slf4j
@Service
public class EntityCollectConfigServiceImpl implements EntityCollectConfigService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Lazy
    @Resource
    private SourceRefService sourceRefService;

    @Lazy
    @Resource
    private EntityService entityService;

    @Lazy
    @Resource
    private VariableService variableService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private OperationLogService operationLogService;

    @Override
    public EntityCollectConfigPO getByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Order.desc("_id")));
        EntityCollectConfigPO entityCollectConfig = mongoTemplate.findOne(query, EntityCollectConfigPO.class, EntityCollectConfigPO.COLLECTION_NAME);
        if (Objects.isNull(entityCollectConfig)) {
            init(botId);
            entityCollectConfig = mongoTemplate.findOne(query, EntityCollectConfigPO.class, EntityCollectConfigPO.COLLECTION_NAME);
        }
        return warpNameResource(entityCollectConfig);
    }

    @Override
    public void init(Long botId) {
        EntityCollectConfigPO entityCollectConfig = new EntityCollectConfigPO();
        entityCollectConfig.setBotId(botId);
        entityCollectConfig.setEnableGlobalCollect(false);
        entityCollectConfig.setEntityVariableMappingList(new ArrayList<>());
        mongoTemplate.insert(entityCollectConfig, EntityCollectConfigPO.COLLECTION_NAME);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        EntityCollectConfigPO config = context.getSnapshot().getEntityCollectConfig();
        if (Objects.nonNull(config)
                && BooleanUtils.isTrue(config.getEnableGlobalCollect())
                && CollectionUtils.isNotEmpty(config.getEntityVariableMappingList())) {
            List<BaseEntityPO> entityList = context.getSnapshot().getEntityList();
            List<VariablePO> variableList = context.getSnapshot().getVariableList();
            try {
                validate(config, entityList, variableList);
            } catch (ComException e) {
                context.getInvalidMsgList().add(
                        SnapshotInvalidFailItemMsg.builder()
                                .resourceId(config.getBotId().toString())
                                .resourceType(ENTITY_COLLECT_CONFIG)
                                .resourceName(ENTITY_COLLECT_CONFIG.getDesc())
                                .failMsg(e.getDetailMsg())
                                .build()
                );
            }
        }
    }

    private EntityCollectConfigPO warpNameResource(EntityCollectConfigPO entityCollectConfig) {
        if (Objects.isNull(entityCollectConfig)) {
            return null;
        }
        List<BaseEntityPO> entityList = entityService.getAllByBotId(entityCollectConfig.getBotId());
        List<VariablePO> variableList =variableService.getListByBotId(entityCollectConfig.getBotId());

        Map<String, String> entityIdNameMap = MyCollectionUtils.listToConvertMap(entityList, BaseEntityPO::getId, BaseEntityPO::getName);
        Map<String, String> variableIdNameMap = MyCollectionUtils.listToConvertMap(variableList, VariablePO::getId, VariablePO::getName);

        if (CollectionUtils.isNotEmpty(entityCollectConfig.getEntityVariableMappingList())) {
            for (EntityCollectConfigPO.EntityVariableMapping entityVariableMapping : entityCollectConfig.getEntityVariableMappingList()) {
                String entityId = entityVariableMapping.getEntityId();
                String variableId = entityVariableMapping.getVariableId();
                if (StringUtils.isNotBlank(entityId) && entityIdNameMap.containsKey(entityId)) {
                    entityVariableMapping.setEntityName(entityIdNameMap.get(entityId));
                }
                if (StringUtils.isNotBlank(variableId) && variableIdNameMap.containsKey(variableId)) {
                    entityVariableMapping.setVariableName(variableIdNameMap.get(variableId));
                }
            }
        }
        return entityCollectConfig;
    }

    private void validate(EntityCollectConfigPO config, List<BaseEntityPO> entityList, List<VariablePO> variableList) {
        Map<String, String> entityIdNameMap = MyCollectionUtils.listToConvertMap(entityList, BaseEntityPO::getId, BaseEntityPO::getName);
        Map<String, VariablePO> variableIdNameMap = MyCollectionUtils.listToMap(variableList, VariablePO::getId);
        if (BooleanUtils.isTrue(config.getEnableGlobalCollect()) && CollectionUtils.isNotEmpty(config.getEntityVariableMappingList())) {
            config.getEntityVariableMappingList().forEach(mapping -> {
                String entityId = mapping.getEntityId();
                String variableId = mapping.getVariableId();
                if (StringUtils.isBlank(entityId) || StringUtils.isBlank(variableId)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体或变量不能为空");
                }
                if (!entityIdNameMap.containsKey(entityId)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体不存在");
                }
                if (!variableIdNameMap.containsKey(variableId)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量不存在");
                }
                VariablePO variable = variableIdNameMap.get(variableId);
                if (!VariableTypeEnum.isDynamicVariable(variable.getType())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体【%s】赋值变量【%s】不是动态变量", entityIdNameMap.get(entityId), variable.getName()));
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(EntityCollectConfigPO update, Long userId) {
        if (Objects.isNull(update.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id不能为空");
        }
        List<BaseEntityPO> entityList = entityService.getAllByBotId(update.getBotId());
        List<VariablePO> variableList =variableService.getListByBotId(update.getBotId());
        validate(update, entityList, variableList);
        EntityCollectConfigPO old = getByBotId(update.getBotId());
        mongoTemplate.save(update, EntityCollectConfigPO.COLLECTION_NAME);

        List<SourceRefPO> refList = new ArrayList<>();
        if (BooleanUtils.isTrue(update.getEnableGlobalCollect())
                && CollectionUtils.isNotEmpty(update.getEntityVariableMappingList())) {
            update.getEntityVariableMappingList().forEach(mapping -> {
                String entityId = mapping.getEntityId();
                String variableId = mapping.getVariableId();
                SourceRefPO entityRef = new SourceRefPO();
                entityRef.setBotId(update.getBotId());
                entityRef.setSourceId(entityId);
                entityRef.setRefId(update.getId());
                entityRef.setSourceType(SourceTypeEnum.ENTITY);
                entityRef.setRefType(IntentRefTypeEnum.GLOBAL_ENTITY_COLLECT);
                entityRef.setRefLabel("全局采集");
                refList.add(entityRef);

                SourceRefPO variableRef = new SourceRefPO();
                variableRef.setBotId(update.getBotId());
                variableRef.setSourceId(variableId);
                variableRef.setRefId(update.getId());
                variableRef.setSourceType(SourceTypeEnum.VARIABLE);
                variableRef.setRefType(IntentRefTypeEnum.GLOBAL_ENTITY_COLLECT);
                variableRef.setRefLabel("全局采集");
                refList.add(variableRef);
            });
        }

        Map<String, String> entityIdNameMap = MyCollectionUtils.listToConvertMap(entityList, BaseEntityPO::getId, BaseEntityPO::getName);
        Map<String, String> variableIdNameMap = MyCollectionUtils.listToConvertMap(variableList, VariablePO::getId, VariablePO::getName);
        sourceRefService.deleteByRefType(update.getBotId(), IntentRefTypeEnum.GLOBAL_ENTITY_COLLECT);
        sourceRefService.batchAddSourceRef(refList);
        botService.onUpdateBotResource(update.getBotId());
        createOperateLog(old, update, entityIdNameMap, variableIdNameMap, userId);
    }

    private void createOperateLog(EntityCollectConfigPO old,
                                  EntityCollectConfigPO update,
                                  Map<String, String> entityIdNameMap,
                                  Map<String, String> variableIdNameMap,
                                  Long userId) {
        // 比较开启状态
        String oldInfo = render(old, entityIdNameMap, variableIdNameMap);
        String newInfo = render(update, entityIdNameMap, variableIdNameMap);

        if (!StringUtils.equals(oldInfo, newInfo)) {
            String changeLog = String.format("编辑实体库, 全局采集配置【%s】修改为【%s】", oldInfo, newInfo);
            operationLogService.save(old.getBotId(), OperationLogTypeEnum.ENTITY, OperationLogResourceTypeEnum.ENTITY, changeLog, userId);
        }
    }

    private String render(EntityCollectConfigPO config,
                          Map<String, String> entityIdNameMap,
                          Map<String, String> variableIdNameMap) {
        if (BooleanUtils.isNotTrue(config.getEnableGlobalCollect())) {
            return "未启用";
        }
        if (CollectionUtils.isEmpty(config.getEntityVariableMappingList())) {
            return "未配置";
        }
        List<String> mappingItemList = new ArrayList<>();
        for (EntityCollectConfigPO.EntityVariableMapping mapping : config.getEntityVariableMappingList()) {
            String entityName = entityIdNameMap.getOrDefault(mapping.getEntityId(), "未知");
            String variableName = variableIdNameMap.getOrDefault(mapping.getVariableId(), "未知");
            mappingItemList.add(String.format("实体:%s, 保存到动态变量:%s", entityName, variableName));
        }
        return StringUtils.join(mappingItemList, "; ");
    }

    @Override
    public void fixData() {
        // 把所有现在的bot都初始化系统实体
        List<BotVO> allBotList = botService.queryListWithoutPage(new BotQuery());
        for (int i = 0; i < allBotList.size(); i++) {
            BotVO bot = allBotList.get(i);
            EntityCollectConfigPO oldConfig = getByBotId(bot.getBotId());
            if (Objects.isNull(oldConfig)) {
                init(bot.getBotId());
            }
            log.info("botId={}的bot初始化全局实体配置完成, progress:{}/{}", bot.getBotId(), i + 1, allBotList.size());
        }
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        EntityCollectConfigPO collectConfig = getByBotId(context.getSrcBotId());
        context.getSnapshot().setEntityCollectConfig(collectConfig);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        if (context.isCopy()) {
            EntityCollectConfigPO collectConfig = context.getSnapshot().getEntityCollectConfig();
            if (Objects.nonNull(collectConfig)) {
                String oldId = collectConfig.getId();
                String newId = new ObjectId().toString();
                collectConfig.setId(newId);
                collectConfig.setBotId(context.getTargetBotId());

                // 更新引用
                if (CollectionUtils.isNotEmpty(collectConfig.getEntityVariableMappingList())) {
                    Map<String, String> variableIdMapping = context.getResourceCopyReferenceMapping().getVariableIdMapping();
                    Map<String, String> entityIdMapping = context.getResourceCopyReferenceMapping().getEntityIdMapping();
                    for (EntityCollectConfigPO.EntityVariableMapping mapping : collectConfig.getEntityVariableMappingList()) {
                        mapping.setEntityId(entityIdMapping.getOrDefault(mapping.getEntityId(), mapping.getEntityId()));
                        mapping.setVariableId(variableIdMapping.getOrDefault(mapping.getVariableId(), mapping.getVariableId()));
                        mapping.setEntityName(null);
                        mapping.setVariableName(null);
                    }
                }
                context.getResourceCopyReferenceMapping().entityConfigIdMapping.put(oldId, newId);
                mongoTemplate.save(collectConfig, EntityCollectConfigPO.COLLECTION_NAME);
            }
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(VariableServiceImpl.class, EntityServiceImpl.class);
    }
}
