package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.StepStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.StepStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StepStatsServiceImpl implements StepStatsService {

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private MongoTemplate readMongoTemplate;

    @Override
    public List<StepStatsPO> queryAllStepStatsList(Long botId, List<String> stepIdList, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        if (CollectionUtils.isNotEmpty(stepIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("stepId").in(stepIdList)));
        }
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group( "stepId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("stepId").as("stepId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.STEP_STATS, StepStatsPO.class)
                .getMappedResults();
    }

    @Override
    public void saveStepStats(BotStatsAnalysisResult analysisResult) {
        if (MapUtils.isEmpty(analysisResult.getStepCountMap())) {
            return;
        }
        analysisResult.getStepCountMap().forEach((stepId, count) -> {
            Query query = BotStatsUtil.generateCommonQuery(analysisResult);
            query.addCriteria(Criteria.where("stepId").is(stepId));
            Update update = new Update();
            update.inc("count", count.get());
            update.inc("callCount", 1);
            callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.STEP_STATS, query, update);
        });
    }
}
