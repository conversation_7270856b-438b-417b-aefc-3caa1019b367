package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.ResourceCopyReferenceMappingBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.GroupPO;
import com.yiwise.dialogflow.entity.po.PublicAudioPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.KnowledgeBatchOperateRequestVO;
import com.yiwise.dialogflow.entity.vo.group.*;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Slf4j
@Service
public class GroupServiceImpl implements GroupService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private BotService botService;
    @Resource
    private KnowledgeService knowledgeService;
    @Resource
    private IntentService intentService;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private PublicAudioService publicAudioService;

    private static final Pattern PATTERN = Pattern.compile("^未命名分组(([1-9]\\d*)?)$");

    private GroupPO initGroupOnCreateBot(Long botId, String name, GroupTypeEnum type, Long userId) {
        LocalDateTime now = LocalDateTime.now();
        GroupPO group = new GroupPO();
        group.setId(null);
        group.setName(name);
        group.setType(type);
        group.setParentId(null);
        group.setLevel(0);
        group.setBotId(botId);
        group.setPath(name);
        group.setCreateTime(now);
        group.setUpdateTime(now);
        group.setCreateUserId(userId);
        group.setUpdateUserId(userId);
        mongoTemplate.insert(group);
        return group;
    }

    @Override
    public GroupPO initAllKnowledgeGroupOnCreateBot(Long botId, Long userId) {
        return initGroupOnCreateBot(botId, ApplicationConstant.ALL_KNOWLEDGE, GroupTypeEnum.KNOWLEDGE, userId);
    }

    @Override
    public GroupPO initAllIntentGroupOnCreateBot(Long botId, Long userId) {
        return initGroupOnCreateBot(botId, ApplicationConstant.ALL_INTENT, GroupTypeEnum.SINGLE_INTENT, userId);
    }

    @Override
    public GroupPO selectOne(Long botId, String name, GroupTypeEnum type, int level, String parentId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("name").is(name)
                .and("type").is(type).and("level").is(level).and("parentId").is(parentId));
        return mongoTemplate.findOne(query, GroupPO.class, GroupPO.COLLECTION_NAME);
    }

    @Override
    public GroupPO initAllIntentGroupIfNotExists(Long botId, Long userId) {
        GroupPO allIntentGroup = selectOne(botId, ApplicationConstant.ALL_INTENT, GroupTypeEnum.SINGLE_INTENT, 0, null);
        if (allIntentGroup == null) {
            log.info("bot:{}{}分组不存在", botId, ApplicationConstant.ALL_INTENT);
            allIntentGroup = initAllIntentGroupOnCreateBot(botId, userId);

            botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
        }
        return allIntentGroup;
    }

    @Override
    public GroupPO getById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return mongoTemplate.findById(id, GroupPO.class, GroupPO.COLLECTION_NAME);
    }

    @Override
    public List<GroupPO> listByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return mongoTemplate.find(Query.query(Criteria.where("_id").in(ids)), GroupPO.class, GroupPO.COLLECTION_NAME);
    }

    @Override
    public GroupPO selectOne(String id, GroupTypeEnum type, Long botId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id).and("type").is(type).and("botId").is(botId)), GroupPO.class);
    }

    @Override
    public List<GroupPO> getListByBotId(Long botId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), GroupPO.class, GroupPO.COLLECTION_NAME);
    }

    @Override
    public GroupPO getRootGroup(Long botId, GroupTypeEnum type) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("type").is(type).and("level").is(0));
        return mongoTemplate.findOne(query, GroupPO.class);
    }

    private boolean groupNotExists(Long botId, String name, GroupTypeEnum type, Integer level, String parentGroupId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("name").is(name).and("type").is(type).and("level").is(level)
                .and("parentId").is(parentGroupId));
        return !mongoTemplate.exists(query, GroupPO.COLLECTION_NAME);
    }

    private void validParam(GroupCreateVO createVO) {
        Assert.notNull(createVO.getType(), "分组类型不能为空");
        Assert.isTrue(StringUtils.isNotBlank(createVO.getParentId()), "父级目录id不能为空");
        Assert.notNull(createVO.getBotId(), "botId不能为空");
    }

    private String generateGroupName(Long botId, String parentId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("parentId").is(parentId));
        return "未命名分组" + mongoTemplate.find(query, GroupPO.class, GroupPO.COLLECTION_NAME)
                .stream().map(GroupPO::getName).map(PATTERN::matcher)
                .filter(Matcher::matches).map(m -> m.group(1))
                .map(NumberUtils::toInt).max(Comparator.comparingInt(o -> o))
                .map(i -> i + 1).orElse(1);
    }

    @Override
    public GroupPO create(GroupCreateVO createVO) {
        validParam(createVO);
        Long botId = createVO.getBotId();
        GroupTypeEnum type = createVO.getType();
        String parentId = createVO.getParentId();
        GroupPO parentGroup = selectOne(parentId, type, botId);
        Assert.notNull(parentGroup, "上级目录不存在");

        int level = parentGroup.getLevel() + 1;
        String name = generateGroupName(botId, parentId);
        String path = parentGroup.getPath() + ApplicationConstant.GROUP_PATH_DELIMITER + name;

        GroupPO po = MyBeanUtils.copy(createVO, GroupPO.class);
        po.setLevel(level);
        po.setPath(path);
        po.setName(name);
        LocalDateTime now = LocalDateTime.now();
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setCreateUserId(createVO.getUserId());
        po.setUpdateUserId(createVO.getUserId());
        mongoTemplate.insert(po, GroupPO.COLLECTION_NAME);

        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);

        buildLogAndSave(po.getBotId(), po.getType(), String.format("新增%s分组【%s】", po.getType().getDesc(), po.getPath()), createVO.getUserId());
        return po;
    }

    private void validParam(GroupUpdateVO updateVO) {
        Assert.isTrue(StringUtils.isNotBlank(updateVO.getId()), "id不能为空");
        Assert.isTrue(StringUtils.isNotBlank(updateVO.getName()), "分组名称不能为空");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GroupUpdateVO updateVO) {
        validParam(updateVO);
        GroupPO oldGroup = getById(updateVO.getId());
        Assert.notNull(oldGroup, "分组不存在");
        if (StringUtils.equals(oldGroup.getName(), updateVO.getName())) {
            return;
        }
        Assert.isTrue(groupNotExists(oldGroup.getBotId(), updateVO.getName(), oldGroup.getType(), oldGroup.getLevel(), oldGroup.getParentId()), "分组名重复");
        String newPathPrefix = StringUtils.substring(oldGroup.getPath(), 0, StringUtils.lastIndexOf(oldGroup.getPath(), oldGroup.getName())) + updateVO.getName();
        for (GroupPO group : listDescendantGroup(oldGroup)) {
            group.setPath(group.getPath().replaceFirst(oldGroup.getPath(), newPathPrefix));
            mongoTemplate.save(group, GroupPO.COLLECTION_NAME);
        }
        GroupPO newGroup = MyBeanUtils.copy(oldGroup, GroupPO.class);
        newGroup.setPath(newPathPrefix);
        newGroup.setName(updateVO.getName());
        newGroup.setUpdateTime(LocalDateTime.now());
        newGroup.setUpdateUserId(updateVO.getUserId());
        mongoTemplate.save(newGroup, GroupPO.COLLECTION_NAME);

        botService.updateAuditStatus(oldGroup.getBotId(), AuditStatusEnum.DRAFT);

        buildLogAndSave(newGroup.getBotId(), newGroup.getType(),
                String.format("编辑%s分组：原分组【%s】名称修改为【%s】", newGroup.getType().getDesc(), oldGroup.getPath(), newGroup.getPath()),
                updateVO.getUserId());
    }

    @Override
    public List<GroupVO> list(GroupTypeEnum type, Long botId) {
        Assert.notNull(type, "type不能为空");
        if (GroupTypeEnum.PUBLIC_AUDIO.equals(type)) {
            botId = 0L;
        }

        List<GroupPO> groupList = mongoTemplate.find(Query.query(Criteria.where("botId").is(botId).and("type").is(type))
                .with(Sort.by(Sort.Order.asc("level"), Sort.Order.desc("createTime"))), GroupPO.class, GroupPO.COLLECTION_NAME);
        List<GroupVO> resultList = Lists.newArrayList();
        Map<String, GroupVO> groupMap = Maps.newHashMap();
        for (GroupPO group : groupList) {
            String id = group.getId();
            String parentId = group.getParentId();
            GroupVO vo = new GroupVO(id, group.getName(), Lists.newArrayList());
            groupMap.put(id, vo);
            GroupVO parentGroup = groupMap.get(parentId);
            if (Objects.isNull(parentGroup)) {
                resultList.add(vo);
            } else {
                parentGroup.getChildList().add(vo);
            }
        }
        return resultList;
    }

    /**
     * <分组id,当前分组递归上级分组id列表>
     */
    private Map<String, List<String>> listToGroupIdParentIdListMap(List<GroupPO> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return Collections.emptyMap();
        }
        groupList = groupList.stream().sorted(Comparator.comparing(GroupPO::getLevel)).collect(Collectors.toList());
        Map<String, List<String>> map = Maps.newHashMap();
        for (GroupPO group : groupList) {
            String groupId = group.getId();
            map.computeIfAbsent(groupId, Lists::newArrayList);
            map.get(groupId).addAll(map.getOrDefault(group.getParentId(), Collections.emptyList()));
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GroupDeleteResultVO delete(GroupDeleteVO deleteVO, Long userId) {
        Assert.notNull(deleteVO.getId(), "id不能为空");
        GroupPO group = getById(deleteVO.getId());
        Assert.notNull(group, "分组不存在");
        Assert.notNull(group.getParentId(), "不能删除根目录");
        Long botId = group.getBotId();

        GroupDeleteResultVO vo = new GroupDeleteResultVO();
        vo.setNeedConfirm(false);

        // 查询当前分组及全部子分组
        List<GroupPO> groupList = listCurrentAndDescendantGroup(group.getId());
        if (CollectionUtils.isEmpty(groupList)) {
            return vo;
        }
        Map<String, GroupPO> groupMap = MyCollectionUtils.listToMap(groupList, GroupPO::getId);
        List<String> groupIdList = MyCollectionUtils.listToConvertList(groupList, GroupPO::getId);
        Map<String, List<String>> map = listToGroupIdParentIdListMap(groupList);
        // 存在子分组时添加删除弹框确认
        if (BooleanUtils.isNotTrue(deleteVO.getForceDelete()) && groupIdList.size() != 1) {
            vo.setNeedConfirm(true);
            return vo;
        }

        // 删除知识
        if (Objects.equals(group.getType(), GroupTypeEnum.KNOWLEDGE)) {
            List<String> knowledgeIdList = knowledgeService.listIdByBotIdAndGroupIds(botId, groupIdList);
            if (CollectionUtils.isNotEmpty(knowledgeIdList)) {
                if (BooleanUtils.isNotTrue(deleteVO.getForceDelete())) {
                    vo.setNeedConfirm(true);
                    return vo;
                }
                KnowledgeBatchOperateRequestVO knowledgeBatchOperateRequestVO = new KnowledgeBatchOperateRequestVO();
                knowledgeBatchOperateRequestVO.setBotId(botId);
                knowledgeBatchOperateRequestVO.setKnowledgeIdList(knowledgeIdList);
                knowledgeService.batchDelete(knowledgeBatchOperateRequestVO, userId);
            }
        }

        // 删除单个意图
        if (Objects.equals(group.getType(), GroupTypeEnum.SINGLE_INTENT)) {
            List<String> intentIdList = intentService.listIdByBotIdAndGroupIds(botId, groupIdList);
            if (CollectionUtils.isNotEmpty(intentIdList)) {
                if (BooleanUtils.isNotTrue(deleteVO.getForceDelete())) {
                    vo.setNeedConfirm(true);
                    return vo;
                }
                IntentQuery intentQuery = IntentQuery.builder().botId(botId).intentIdList(intentIdList).intentType(IntentTypeEnum.SINGLE).build();
                intentQuery.setWithPage(false);
                List<IntentPO> notDeletedIntentList = intentService.delete(intentQuery, userId);
                if (CollectionUtils.isNotEmpty(notDeletedIntentList)) {
                    List<String> notDeletedGroupIdList = Lists.newArrayList();
                    for (IntentPO intentPO : notDeletedIntentList) {
                        notDeletedGroupIdList.addAll(map.getOrDefault(intentPO.getGroupId(), Collections.emptyList()));
                    }
                    groupIdList = groupIdList.stream().filter(id -> !notDeletedGroupIdList.contains(id)).collect(Collectors.toList());
                }
            }
        }

        // 删除公共音频
        if (Objects.equals(group.getType(), GroupTypeEnum.PUBLIC_AUDIO)) {
            Query query = Query.query(Criteria.where("groupId").in(groupIdList));
            boolean exists = mongoTemplate.exists(query, PublicAudioPO.COLLECTION_NAME);
            if (exists) {
                if (BooleanUtils.isNotTrue(deleteVO.getForceDelete())) {
                    vo.setNeedConfirm(true);
                    return vo;
                }
                mongoTemplate.remove(query, PublicAudioPO.COLLECTION_NAME);
            }
        }

        // 删除分组
        mongoTemplate.remove(Query.query(Criteria.where("_id").in(groupIdList)), GroupPO.COLLECTION_NAME);

        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);

        List<GroupPO> deletedGroupList = groupIdList.stream().map(groupMap::get).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deletedGroupList)) {
            buildLogAndSave(botId, group.getType(),
                    String.format("删除%s分组%s", group.getType().getDesc(),
                            deletedGroupList.stream().map(s -> "【" + s.getPath() + "】").collect(Collectors.joining("、"))),
                    userId);
        }
        return vo;
    }

    private OperationLogResourceTypeEnum groupType2LogResourceType(GroupTypeEnum groupType) {
        if (Objects.isNull(groupType)) {
            return null;
        }
        if (GroupTypeEnum.KNOWLEDGE.equals(groupType)) {
            return OperationLogResourceTypeEnum.KNOWLEDGE_GROUP;
        }
        if (GroupTypeEnum.SINGLE_INTENT.equals(groupType)) {
            return OperationLogResourceTypeEnum.INTENT_GROUP;
        }
        if (GroupTypeEnum.PUBLIC_AUDIO.equals(groupType)) {
            return OperationLogResourceTypeEnum.PUBLIC_AUDIO_GROUP;
        }
        return null;
    }

    private void buildLogAndSave(Long botId, GroupTypeEnum groupType, String detail, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.GROUP, groupType2LogResourceType(groupType), detail, userId);
    }

    private List<GroupPO> listDescendantGroup(GroupPO group) {
        if (Objects.isNull(group)) {
            return Collections.emptyList();
        }
        Query query = Query.query(Criteria.where("botId").is(group.getBotId())
                .and("type").is(group.getType()).and("level").gt(group.getLevel())
                .and("path").regex(Pattern.compile("^" + group.getPath() + ApplicationConstant.GROUP_PATH_DELIMITER + ".*$")));
        return mongoTemplate.find(query, GroupPO.class, GroupPO.COLLECTION_NAME);
    }

    @Override
    public List<String> listCurrentAndDescendantGroupId(String groupId) {
        return listCurrentAndDescendantGroup(groupId).stream().map(GroupPO::getId).collect(Collectors.toList());
    }

    private List<GroupPO> listCurrentAndDescendantGroup(String groupId) {
        GroupPO group = getById(groupId);
        if (Objects.isNull(group)) {
            return Collections.emptyList();
        }
        List<GroupPO> groupList = listDescendantGroup(group);
        groupList.add(group);
        return groupList;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        context.getSnapshot().setGroupList(getListByBotId(context.getSrcBotId()));
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Long currentUserId = context.getCurrentUserId();
        Long targetBotId = context.getTargetBotId();
        List<GroupPO> groupList = context.getSnapshot().getGroupList();
        if (context.isCopy()) {
            Map<String, String> groupIdMapping = mapping.getGroupIdMapping();
            groupList.forEach(group -> {
                String oldId = group.getId();
                String newId = new ObjectId().toString();
                groupIdMapping.put(oldId, newId);
            });

            for (GroupPO group : groupList) {
                group.setId(groupIdMapping.get(group.getId()));
                group.setParentId(groupIdMapping.get(group.getParentId()));
                group.setBotId(targetBotId);
                group.setCreateUserId(currentUserId);
                group.setUpdateUserId(currentUserId);
            }
            mongoTemplate.insert(groupList, GroupPO.class);
        }
    }

}
