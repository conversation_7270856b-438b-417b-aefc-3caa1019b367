package com.yiwise.dialogflow.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.share.AnswerAudioMiddleResult;
import com.yiwise.dialogflow.engine.share.BotAudioConfig;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.common.PauseSeparatorElement;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.asrmodel.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.llm.RagTextUploadRecordPO;
import com.yiwise.dialogflow.entity.po.magic.MagicActivityConfigPO;
import com.yiwise.dialogflow.entity.query.RobotSnapshotCreateRequest;
import com.yiwise.dialogflow.entity.query.RobotSnapshotQuery;
import com.yiwise.dialogflow.entity.vo.RobotSnapshotCreateResult;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrSelfLearningDetailVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.asrmodel.*;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.llm.EmbeddingService;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.dialogflow.utils.SilenceAudioUtils;
import com.yiwise.dialogflow.utils.asr.AliAsrVocabApiUtil;
import com.yiwise.dialogflow.utils.asr.TencentVocabApiUtil;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("RobotSnapshotService")
public class RobotSnapshotServiceImpl implements RobotSnapshotService {

    @Resource
    private BotResourceSequenceService botResourceSequenceService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MongoTemplate readMongoTemplate;

    @Resource
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private TrainService trainService;

    @Resource
    private AsrProviderService asrProviderService;

    @Resource
    private AsrVocabProviderRelationService asrVocabProviderRelationService;

    @Resource
    private AsrSelfLearningProviderRelationService asrSelfLearningProviderRelationService;

    @Resource
    private AsrVocabService asrVocabService;

    @Resource
    private AsrSelfLearningService asrSelfLearningService;

    @Resource
    private AsrErrorCorrectionService asrErrorCorrectionService;

    @Resource
    private EmbeddingService ragTextService;

    @Resource
    private MagicActivityConfigService magicActivityConfigService;

    @Override
    public RobotSnapshotCreateResult createAndValid(RobotSnapshotCreateRequest request) {
        return doCreate(request);
    }

    @Override
    public RobotSnapshotPO createSnapshotForExport(Long botId, Long userId) {
        // 加载所有的资源
        BotPO bot = botService.detail(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }
        // 初始化上下文
        RobotResourceContext context = RobotResourceContext.init(bot.getBotId(), bot.getBotId(), userId, null);
        context.setUsageTarget(RobotSnapshotUsageTargetEnum.TEXT_TEST);
        context.setExport(true);
        // 加载所有的资源
        RobotSnapshotPO snapshot = context.getSnapshot();
        snapshot.setBot(bot);
        snapshot.setTenantId(botRefService.getTenantIdByBotId(botId));
        snapshot.setBotId(botId);
        snapshot.setIsPublish(false);
        context.setSrcBotType(bot.getType());

        // 获取接口所有的实现类并调用save
        Map<String, RobotResourceService> beansOfType = AppContextUtils.getContext().getBeansOfType(RobotResourceService.class);
        for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
            entry.getValue().saveToSnapshot(context);
        }

        return snapshot;
    }

    @Override
    public Optional<RobotSnapshotPO> queryLastSnapshotByCondition(RobotSnapshotQuery condition) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(condition.getBotId()));
        if (BooleanUtils.isTrue(condition.getIsPublish())) {
            query.addCriteria(Criteria.where("isPublish").is(true));
        }
        if (BooleanUtils.isTrue(condition.getValidatedAudio())) {
            query.addCriteria(Criteria.where("validatedAudio").is(true));
        }
        if (BooleanUtils.isTrue(condition.getValidatedActionRule())) {
            query.addCriteria(Criteria.where("validatedActionRule").is(true));
        }
        if (BooleanUtils.isTrue(condition.getValidatedOutsideResource())) {
            query.addCriteria(Criteria.where("validatedOutsideResource").is(true));
        }
        query.with(Sort.by(Sort.Direction.DESC, "version"));
        query.limit(1);
        String collectionName = selectCollectionName(condition.getUsageTarget());
        RobotSnapshotPO snapshot = mongoTemplate.findOne(query, RobotSnapshotPO.class, collectionName);
        return Optional.ofNullable(snapshot);
    }

    @Override
    public Optional<Integer> queryLastVersionByCondition(RobotSnapshotQuery condition) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(condition.getBotId())));
        if (BooleanUtils.isTrue(condition.getValidatedAudio())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("validatedAudio").is(true)));
        }
        if (BooleanUtils.isTrue(condition.getValidatedActionRule())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("validatedActionRule").is(true)));
        }
        if (BooleanUtils.isTrue(condition.getValidatedOutsideResource())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("validatedOutsideResource").is(true)));
        }
        AggregationOperation groupOperation = Aggregation.group("botId")
                .max("version").as("maxVersion");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        String collectionName = selectCollectionName(condition.getUsageTarget());
        List<Map> list = mongoTemplate.aggregate(aggregation, collectionName, Map.class).getMappedResults();
        if (CollectionUtils.isEmpty(list) || MapUtils.isEmpty(list.get(0))) {
            return Optional.empty();
        }
        return Optional.of(Integer.valueOf(String.valueOf(list.get(0).get("maxVersion"))));
    }

    @Override
    public RobotSnapshotCreateResult create(Long botId, Long userId, Boolean ignoreWarning) {
        RobotSnapshotCreateRequest request = new RobotSnapshotCreateRequest();
        request.setBotId(botId);
        request.setUserId(userId);
        request.setUsageTarget(RobotSnapshotUsageTargetEnum.CALL_OUT);
        request.setRequireValidAudio(true);
        request.setRequireValidActionRule(true);
        request.setRequireValidOutsideResource(true);
        request.setRequireExistIntentModel(true);
        request.setIntentModelNotTraining(true);
        request.setIgnoreWarning(BooleanUtils.isTrue(ignoreWarning));
        // 忽略告警的时候, 也忽略绑定变量(这个是二阶段发布)
        request.setIgnoreRequireVariableBind(BooleanUtils.isTrue(ignoreWarning));
        return doCreate(request);
    }

    public RobotSnapshotCreateResult doCreate(RobotSnapshotCreateRequest request) {
        // 加载所有的资源
        BotPO bot = botService.detail(request.getBotId());
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }
        Long botId = bot.getBotId();
        // 初始化上下文
        RobotResourceContext context = RobotResourceContext.init(bot.getBotId(), bot.getBotId(), request.getUserId(), null);
        context.setUsageTarget(request.getUsageTarget());
        // 加载所有的资源
        RobotSnapshotPO snapshot = context.getSnapshot();
        snapshot.setBot(bot);
        snapshot.setTenantId(botRefService.getTenantIdByBotId(botId));
        snapshot.setBotId(botId);
        snapshot.setIsPublish(false);
        context.setSrcBotType(bot.getType());

        // 获取接口所有的实现类并调用save
        Map<String, RobotResourceService> beansOfType = AppContextUtils.getContext().getBeansOfType(RobotResourceService.class);
        for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
            entry.getValue().saveToSnapshot(context);
        }

        // 设置将在答案中播放的变量名称
        snapshot.setPlayableVariableNameSet(calculatePlayableVariableNameSet(snapshot));

        // 设置模板变量名称-默认值映射
        Map<String, String> templateVarNameValueMap = new HashMap<>();

        for (VariablePO variable : snapshot.getVariableList()) {
            if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
                if (StringUtils.isBlank(variable.getDefaultValue())) {
                    templateVarNameValueMap.put(variable.getName(), variable.getName());
                } else {
                    templateVarNameValueMap.put(variable.getName(), variable.getDefaultValue());
                }
            }
        }
        snapshot.setTemplateVarNameValueMap(templateVarNameValueMap);

        ActionNameResourceBO actionResource = intentRuleActionService.getSourceId2NameMapByBotId(botId);

        // 意向等级映射
        Long intentLevelTagId = bot.getIntentLevelTagId();
        Map<Integer, String> intentLevelDetailCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(intentLevelTagId);

        snapshot.setIntentLevelDetailCode2NameMap(intentLevelDetailCode2NameMap);
        if (StringUtils.isNotBlank(bot.getAsrErrorCorrectionDetailId())) {
            AsrErrorCorrectionDetailPO asrErrorCorrectionDetail = asrErrorCorrectionService.get(bot.getAsrErrorCorrectionDetailId());
            snapshot.setAsrErrorCorrectionDetail(asrErrorCorrectionDetail);
        }

        context.setDependentResource(prepareDependResource(snapshot));
        context.setActionNameResource(actionResource);
        SnapshotValidateConfigBO validateConfig = new SnapshotValidateConfigBO();
        validateConfig.setRequireValidAudio(BooleanUtils.isTrue(request.getRequireValidAudio()));
        validateConfig.setRequireValidActionRule(BooleanUtils.isTrue(request.getRequireValidActionRule()));
        validateConfig.setRequireValidOutsideResource(BooleanUtils.isTrue(request.getRequireValidOutsideResource()));
        validateConfig.setRequireExistIntentModel(BooleanUtils.isTrue(request.getRequireExistIntentModel()));
        validateConfig.setIntentModelNotTraining(BooleanUtils.isTrue(request.getIntentModelNotTraining()));

        context.setValidateConfig(validateConfig);

        RobotSnapshotCreateResult result = new RobotSnapshotCreateResult();

        // 校验答案标签唯一性
        validateAnswerLabelIsUnique(snapshot);

        // 开始对各种资源进行全面的校验
        for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
            entry.getValue().validateResource(context);
        }

        // 校验是否需要绑定变量

        // 评断是否校验通过
        boolean fail = context.getInvalidMsgList().stream()
                .anyMatch(item -> BooleanUtils.isNotTrue(item.getIsWarning()));

        // 是否包含告警信息
        boolean hasWarning = context.getInvalidMsgList().stream()
                .anyMatch(item -> BooleanUtils.isTrue(item.getIsWarning()));

        // 判断是否启用了大模型特殊语境, 启用了, 则需要上传答案文本信息
        String waitingRecordId = null;
        if (checkEnableLLMChat(snapshot)) {
            // 启用了大模型特殊语境
            log.debug("启用了大模型特殊语境");
            try {
                Optional<RagTextUploadRecordPO> lastRecord = ragTextService.getLastByBotId(snapshot.getBotId());
                boolean needUpload = ragTextService.checkNeedUpload(lastRecord.orElse(null),
                        snapshot.getNodeList(),
                        snapshot.getKnowledgeList(),
                        snapshot.getSpecialAnswerConfigList());
                if (needUpload) {
                    log.debug("需要重新上传");
                    waitingRecordId = ragTextService.uploadAll(botId, request.getUserId(),
                            snapshot.getNodeList(),
                            snapshot.getKnowledgeList(),
                            snapshot.getSpecialAnswerConfigList());
                } else {
                    // 判断前端是否超时超时
                    if (lastRecord.isPresent()
                            && Objects.isNull(lastRecord.get().getSuccess())
                            && !ragTextService.checkIsTimeout(lastRecord.get())) {
                        waitingRecordId = lastRecord.get().getId();
                    }
                }
            } catch (Exception e) {
                log.warn("上传答案文本向量失败, 原因: {}", e.getMessage());
                context.getInvalidMsgList().add(SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.RAG_DOC)
                        .failMsg("大模型对话所需话术信息上传失败")
                        .build()
                );
                fail = true;
            }
        }

        if (fail
                || (BooleanUtils.isNotTrue(request.getIgnoreWarning()) && hasWarning)
                || (BooleanUtils.isNotTrue(request.getIgnoreRequireVariableBind()) && BooleanUtils.isTrue(result.getNeedBindVariable()))) {
            result.setSuccess(false);
            result.setMsgList(context.getInvalidMsgList());
            return result;
        }

        if (StringUtils.isNotBlank(waitingRecordId)) {
            log.debug("等待rag上传结果, waitingRecordId={}", waitingRecordId);
            try {
                ragTextService.waitingResult(snapshot.getBotId(), waitingRecordId);
            } catch (Exception e) {
                log.warn("等待rag上传结果失败, waitingRecordId={}, 原因: {}", waitingRecordId, e.getMessage());
                context.getInvalidMsgList().add(SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.RAG_DOC)
                        .failMsg("大模型对话所需话术信息上传结果获取失败")
                        .build()
                );
                result.setSuccess(false);
                result.setMsgList(context.getInvalidMsgList());
                return result;
            }
        }

        // 对快照做各种校验数据计算
        snapshot.setValidatedAudio(BooleanUtils.isTrue(validateConfig.getRequireValidAudio()));
        snapshot.setValidatedActionRule(BooleanUtils.isTrue(validateConfig.getRequireValidActionRule()));
        snapshot.setValidatedOutsideResource(BooleanUtils.isTrue(validateConfig.getRequireValidOutsideResource()));

        result.setSnapshot(doSave(snapshot, request));
        result.setSuccess(true);
        result.setMsgList(context.getInvalidMsgList());
        return result;
    }

    private Set<String> calculatePlayableVariableNameSet(RobotSnapshotPO snapshot) {
        Set<String> variableNameSet = new HashSet<>();

        // 获取所有的答案列表
        List<BaseAnswerContent> allAnswerList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(snapshot.getNodeList())) {
            snapshot.getNodeList().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getAnswerList())) {
                    allAnswerList.addAll(item.getAnswerList());
                }
            });
        }
        if (CollectionUtils.isNotEmpty(snapshot.getKnowledgeList())) {
            snapshot.getKnowledgeList().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getAnswerList())) {
                    allAnswerList.addAll(item.getAnswerList());
                }
            });
        }
        if (CollectionUtils.isNotEmpty(snapshot.getSpecialAnswerConfigList())) {
            snapshot.getSpecialAnswerConfigList().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getAnswerList())) {
                    allAnswerList.addAll(item.getAnswerList());
                }
            });
        }
        for (BaseAnswerContent answer : allAnswerList) {
            AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), false);
            variableNameSet.addAll(splitter.getPlaceholderSet());
        }
        return variableNameSet;
    }

    private void validateAnswerLabelIsUnique(RobotSnapshotPO snapshot) {
        List<BaseAnswerContent> answerList = getAllAnswer(snapshot);
        Map<String, List<BaseAnswerContent>> answerLabel2AnswerTextListMap = MyCollectionUtils.listToMapList(answerList, BaseAnswerContent::getUniqueId);
        answerLabel2AnswerTextListMap.forEach((k, v) -> {

            // 这里暂时没有找到答案标签重复的问题, 先在这里对于重复的进行去重处理

            if (v.size() > 1) {
//                String answerText = v.stream().map(BaseAnswerContent::getText).distinct().collect(Collectors.joining("; "));
//                throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案标签不唯一, 标签" + k + ", 关联答案为：" + answerText);
                for (int i = 1; i < v.size(); i++) {
                    BaseAnswerContent answerContent = v.get(i);
                    answerContent.setLabel(String.format("%s_%s", answerContent.getLabel(), i));
                }
            }
        });
    }

    @Override
    public Integer createTextTestSnapshot(Long dialogFlowId) {
        return createTrainTestSnapshotByDialogFlowId(dialogFlowId, RobotSnapshotUsageTargetEnum.TEXT_TEST);
    }

    @Override
    public Integer createSpeechTestSnapshot(Long dialogFlowId) {
        return createTrainTestSnapshotByDialogFlowId(dialogFlowId, RobotSnapshotUsageTargetEnum.SPEECH_TEST);
    }

    @Override
    public RobotSnapshotCreateResult createTrainTestSnapshot(Long botId, RobotSnapshotUsageTargetEnum usageTarget, SystemEnum systemEnum) {
        RobotSnapshotCreateRequest request = new RobotSnapshotCreateRequest();
        request.setBotId(botId);
        request.setUsageTarget(usageTarget);
        request.setRequireValidAudio(!BooleanUtils.isTrue(RobotSnapshotUsageTargetEnum.TEXT_TEST.equals(usageTarget)));
        if (SystemEnum.OPE.equals(systemEnum)) {
            request.setRequireValidActionRule(true);
            request.setRequireValidOutsideResource(false);
        } else {
            request.setRequireValidActionRule(true);
            request.setRequireValidOutsideResource(true);
        }
        request.setRequireExistIntentModel(true);
        request.setIgnoreWarning(true);
        request.setIgnoreRequireVariableBind(true);
        return createTrainSnapshotIfModified(request);
    }

    @Override
    public Integer createTextSnapshotByBotId(Long botId) {
        RobotSnapshotCreateRequest request = new RobotSnapshotCreateRequest();
        request.setBotId(botId);
        request.setUsageTarget(RobotSnapshotUsageTargetEnum.TEXT_TEST);
        request.setRequireValidAudio(false);
        request.setIgnoreWarning(true);
        request.setIgnoreRequireVariableBind(true);
        return getCreatedVersionOrThrow(createTrainSnapshotIfModified(request));
    }

    private Integer getCreatedVersionOrThrow(RobotSnapshotCreateResult result) {
        if (BooleanUtils.isNotTrue(result.isSuccess())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, result.getMsgList().get(0).getFailMsg());
        }
        return result.getSnapshot().getVersion();
    }

    private RobotSnapshotCreateResult createTrainSnapshotIfModified(RobotSnapshotCreateRequest request) {
        log.info("createTrainTestSnapshot, request={}", JsonUtils.object2String(request));
        BotPO bot = botService.detail(request.getBotId());
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }
        Optional<Integer> checkRes = checkNecessaryCreateTestSnapshot(bot, request);
        if (checkRes.isPresent()) {
            log.info("和上次快照之间未更新bot, 不再创建快照");
            RobotSnapshotCreateResult result = new RobotSnapshotCreateResult();
            result.setSuccess(true);
            result.setSnapshot(getByVersion(request.getBotId(), request.getUsageTarget(), checkRes.get()));
            return result;
        }
        return createAndValid(request);
    }

    private Integer createTrainTestSnapshotByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "话术不存在");
        }
        log.info("createTrainTestSnapshot, dialogFlowId={}, botId={}, usageTarget={}", dialogFlowId, botId, usageTarget);
        return getCreatedVersionOrThrow(createTrainTestSnapshot(botId, usageTarget, SystemEnum.OPE));
    }

    private Optional<Integer> checkNecessaryCreateTestSnapshot(BotPO bot, RobotSnapshotCreateRequest request) {
        RobotSnapshotQuery condition = new RobotSnapshotQuery();
        condition.setUsageTarget(request.getUsageTarget());
        condition.setBotId(bot.getBotId());
        condition.setValidatedAudio(request.getRequireValidAudio());
        condition.setValidatedActionRule(request.getRequireValidActionRule());
        condition.setValidatedOutsideResource(request.getRequireValidOutsideResource());
        Optional<RobotSnapshotPO> lastSnapshot = queryLastSnapshotByCondition(condition);
        if (!lastSnapshot.isPresent()) {
            return Optional.empty();
        }

        if (Objects.isNull(bot.getUpdateTime()) || Objects.isNull(lastSnapshot.get().getCreateTime())) {
            return Optional.empty();
        }
        log.info("bot最后更新时间:{}, 快照创建时间:{}", bot.getUpdateTime(), lastSnapshot.get().getCreateTime());
        LocalDateTime now = LocalDateTime.now();
        if (lastSnapshot.get().getCreateTime().isAfter(bot.getUpdateTime())
                && lastSnapshot.get().getCreateTime().isAfter(now.minusMinutes(20))) {
            return Optional.of(lastSnapshot.get().getVersion());
        }

        return Optional.empty();
    }

    // 准备dependResource
    private DependentResourceBO prepareDependResource(RobotSnapshotPO snapshot) {
        return dependResourceService.generateBySnapshot(snapshot);
    }

    private RobotSnapshotPO doSave(RobotSnapshotPO snapshot, RobotSnapshotCreateRequest request) {
        String collectionName = selectCollectionName(request.getUsageTarget());
        String resourceType = collectionName;
        snapshot.setVersion(botResourceSequenceService.generate(snapshot.getBotId(), resourceType));
        snapshot.setCreateTime(LocalDateTime.now());
        snapshot.setUpdateTime(LocalDateTime.now());

        // 意图语料中的问法语料不需要存到快照中, 对话中用不到, 且增大快照空间
        if (CollectionUtils.isNotEmpty(snapshot.getIntentCorpusList()) && !request.isKeepCorpus()) {
            snapshot.setIntentCorpusList(snapshot.getIntentCorpusList().stream()
                    .map(intentCorpus -> {
                        IntentCorpusPO newCorpus = MyBeanUtils.copy(intentCorpus, IntentCorpusPO.class);
                        newCorpus.setBuildInCorpusList(Collections.emptyList());
                        newCorpus.setCorpusList(Collections.emptyList());
                        return newCorpus;
                    })
                    .collect(Collectors.toList())
            );
        }

        mongoTemplate.save(snapshot, collectionName);
        if (RobotSnapshotUsageTargetEnum.isTrainTest(request.getUsageTarget())) {
            deleteTrainSnapshotOldVersion(snapshot.getBotId(), snapshot.getVersion(), collectionName);
        }
        return snapshot;
    }

    // 文本训练测试仅保留最近5个版本吧
    private void deleteTrainSnapshotOldVersion(Long botId, Integer newestVersion, String collectionName) {
        log.info("删除无用的训练快照, botId={}, version={}, collectionName={}", botId, newestVersion, collectionName);
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("version").lt(newestVersion - 5));
        mongoTemplate.remove(query, collectionName);
    }

    @Override
    public RobotSnapshotPO getByVersion(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(version)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "version不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("version").is(version))
                .with(Sort.by(Sort.Order.desc("_id")));

        return mongoTemplate.findOne(query, RobotSnapshotPO.class, selectCollectionName(usageTarget));
    }

    @Override
    public Mono<RobotSnapshotPO> asyncGetByVersion(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(version)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "version不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("version").is(version))
                .with(Sort.by(Sort.Order.desc("_id")));

        return reactiveMongoTemplate.findOne(query, RobotSnapshotPO.class, selectCollectionName(usageTarget))
                .transformDeferred(MdcLogIdLifterTransformer.lift());
    }

    private String selectCollectionName(RobotSnapshotUsageTargetEnum usageTarget) {
        String collectionName = RobotSnapshotPO.TEXT_TRAIN_COLLECTION_NAME;
        if (RobotSnapshotUsageTargetEnum.SPEECH_TEST.equals(usageTarget)) {
            collectionName = RobotSnapshotPO.SPEECH_TRAIN_COLLECTION_NAME;
        } else if (RobotSnapshotUsageTargetEnum.CALL_OUT.equals(usageTarget)) {
            collectionName = RobotSnapshotPO.COLLECTION_NAME;
        }
        log.info("selectCollectionName, usageTarget={}, collectionName={}", usageTarget, collectionName);
        return collectionName;
    }

    @Override
    public Integer getLastVersionNumber(Long botId, RobotSnapshotUsageTargetEnum usageTarget) {
        return asyncGetLastVersionNumber(botId, usageTarget).block();
    }

    @Override
    public Mono<Integer> asyncGetLastVersionNumber(Long botId, RobotSnapshotUsageTargetEnum usageTarget) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        if (RobotSnapshotUsageTargetEnum.CALL_OUT.equals(usageTarget)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("isPublish").is(true)));
        }
        AggregationOperation groupOperation = Aggregation.group("botId")
                .max("version").as("maxVersion");
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        String collectionName = selectCollectionName(usageTarget);
        return reactiveMongoTemplate.aggregate(aggregation, collectionName, Map.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .next()
                .map(map -> {
                    if (MapUtils.isEmpty(map) || map.get("maxVersion") == null) {
                        return -1;
                    }
                    return Integer.valueOf(String.valueOf(map.get("maxVersion")));
                }).switchIfEmpty(Mono.defer(() -> Mono.just(-1)));
    }

    private Integer getLastVersionNumberForManualPublish(Long botId) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        AggregationOperation groupOperation = Aggregation.group("botId")
                .max("version").as("maxVersion");
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        String collectionName = selectCollectionName(RobotSnapshotUsageTargetEnum.CALL_OUT);
        List<Map> list = mongoTemplate.aggregate(aggregation, collectionName, Map.class).getMappedResults();
        if (CollectionUtils.isEmpty(list) || MapUtils.isEmpty(list.get(0))) {
            return -1;
        }
        return Integer.valueOf(String.valueOf(list.get(0).get("maxVersion")));
    }

    @Override
    public Integer getLastVersionByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return null;
        }
        return getLastVersionNumber(botId, usageTarget);
    }

    @Override
    public Mono<Integer> asyncGetLastVersionByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        log.info("asyncGetLastVersionByDialogFlowId, dialogFlowId={}, usageTarget={}", dialogFlowId, usageTarget);
        return botRefService.asyncGetBotId(dialogFlowId)
                .flatMap(botId -> asyncGetLastVersionNumber(botId, usageTarget))
                .switchIfEmpty(Mono.defer(() -> Mono.just(-1)));
    }

    @Override
    public BotMetaData getBotMetaData(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        RobotSnapshotPO snapshot = getByVersion(botId, usageTarget, version);
        if (Objects.isNull(snapshot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在");
        }
        return convert(snapshot, usageTarget);
    }

    @Override
    public Mono<BotMetaData> asyncGetBotMetaDataByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return botRefService.asyncGetBotId(dialogFlowId)
                .flatMap(botId -> asyncGetByVersion(botId, usageTarget, version))
                .switchIfEmpty(Mono.defer(() -> Mono.error(new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在"))))
                .map(snapshot -> convert(snapshot, usageTarget));
    }

    @Override
    public BotMetaData getBotMetaDataByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return null;
        }
        return getBotMetaData(botId, usageTarget, version);
    }

    /**
     * 因为对话中依赖部分外部资源的数据(比如热词), 所以会查询外部资源最新的数据
     * 同时需要支持技术对话术的asrProvider进行切换的时候, 无需发布审核即可生效, asrProvider的数据也是来自于实时的数据而不是快照中的数据
     * @param botId
     * @param usageTarget
     * @param version
     * @return
     */
    @Override
    public BotAsrConfig getRealtimeAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }

        RobotSnapshotPO snapshot = getByVersion(botId, usageTarget, version);
        if (Objects.isNull(snapshot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在");
        }

        return generateBotAsrConfig(snapshot, bot);
    }

    @Override
    public Mono<BotAsrConfig> asyncGetRealtimeAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return Mono.fromCallable(() -> botService.getById(botId))
                .subscribeOn(Schedulers.elastic())
                .switchIfEmpty(Mono.defer(() -> Mono.error(new ComException(ComErrorCode.NOT_EXIST, "机器人不存在"))))
                .zipWith(asyncGetByVersion(botId, usageTarget, version)
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在")))))
                .map(tuple -> generateBotAsrConfig(tuple.getT2(), tuple.getT1()));
    }

    private BotAsrConfig generateBotAsrConfig(RobotSnapshotPO snapshot, BotPO currentBotInfo) {
        BotPO botSnapshot = snapshot.getBot();
        BotPO bot = currentBotInfo;
        AsrConfigPO asrConfig = new BotPO();
        asrConfig.setAsrProviderId(bot.getAsrProviderId());
        asrConfig.setAsrLanguageId(botSnapshot.getAsrLanguageId());
        asrConfig.setAsrVocabId(botSnapshot.getAsrVocabId());
        asrConfig.setAsrSelfLearningDetailId(botSnapshot.getAsrSelfLearningDetailId());
        asrConfig.setAsrErrorCorrectionDetailId(botSnapshot.getAsrErrorCorrectionDetailId());
        asrConfig.setMaxSentenceSilence(botSnapshot.getMaxSentenceSilence());
        asrConfig.setEnableAsrOptimization(bot.getEnableAsrOptimization());
        return getAsrConfig(asrConfig);
    }

    @Override
    public RobotSnapshotPO getRealtimeResourceSnapshot(Long botId) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }
        // 初始化上下文
        RobotResourceContext context = RobotResourceContext.init(bot.getBotId(), bot.getBotId(), 0L, null);

        // 获取接口所有的实现类并调用save
        Map<String, RobotResourceService> beansOfType = AppContextUtils.getContext().getBeansOfType(RobotResourceService.class);
        for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
            entry.getValue().saveToSnapshot(context);
        }
        // 加载所有的资源
        RobotSnapshotPO snapshot = context.getSnapshot();
        snapshot.setBot(bot);
        snapshot.setBotId(botId);
        return snapshot;
    }

    @Override
    public MagicActivityConfig getMagicActivityConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId) {
        RobotSnapshotPO snapshot = getByVersion(botId, usageTarget, version);
        if (Objects.isNull(snapshot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在");
        }

        Optional<MagicActivityConfigPO> configOpt = magicActivityConfigService.getByBotIdAndCallJobId(botId, callJobId);

        MagicActivityConfigPO config = configOpt.orElseGet(() -> {
            MagicActivityConfigPO magicActivityConfig = new MagicActivityConfigPO();
            magicActivityConfig.setBotId(botId);
            magicActivityConfig.setCallJobId(callJobId);
            magicActivityConfig.setTemplateVarValueMap(new HashMap<>());
            TtsVoiceConfigPO ttsConfig = new TtsVoiceConfigPO();
            ttsConfig.setTtsSpeed(6.0f);
            ttsConfig.setTtsVolume(40f);
            // 加载快照中的音色
            if (AudioTypeEnum.COMPOSE.equals(snapshot.getBotConfig().getAudioConfig().getAudioType())) {
                if (Objects.nonNull(snapshot.getBotConfig().getAudioConfig().getTtsConfig())
                        && StringUtils.isNotBlank(snapshot.getBotConfig().getAudioConfig().getTtsConfig().getTtsVoice())) {
                    ttsConfig.setTtsVoice(snapshot.getBotConfig().getAudioConfig().getTtsConfig().getTtsVoice());
                    ttsConfig.setTtsSpeed(snapshot.getBotConfig().getAudioConfig().getTtsConfig().getTtsSpeed());
                    ttsConfig.setTtsVolume(snapshot.getBotConfig().getAudioConfig().getTtsConfig().getTtsVolume());
                }
            } else {
                if (Objects.nonNull(snapshot.getBotAudioConfig().getSpliceTtsConfig())
                        && StringUtils.isNotBlank(snapshot.getBotConfig().getAudioConfig().getSpliceTtsConfig().getTtsVoice())) {
                    ttsConfig.setTtsVoice(snapshot.getBotConfig().getAudioConfig().getSpliceTtsConfig().getTtsVoice());
                    ttsConfig.setTtsSpeed(snapshot.getBotConfig().getAudioConfig().getSpliceTtsConfig().getTtsSpeed());
                    ttsConfig.setTtsVolume(snapshot.getBotConfig().getAudioConfig().getSpliceTtsConfig().getTtsVolume());
                }
            }
            magicActivityConfig.setTtsConfig(ttsConfig);
            return magicActivityConfig;
        });


        BotAudioConfig audioConfig = new BotAudioConfig();
        audioConfig.setAudioType(snapshot.getBotAudioConfig().getAudioType().getCode());
        audioConfig.setEnableVariableAudio(snapshot.getBotAudioConfig().getEnableVariableAudio());
        audioConfig.setTtsVolume(config.getTtsConfig().getTtsVolume());
        audioConfig.setTtsSpeed(config.getTtsConfig().getTtsSpeed());
        audioConfig.setTtsVoice(config.getTtsConfig().getTtsVoice());
        MagicActivityConfig magicActivityConfig = new MagicActivityConfig();
        magicActivityConfig.setBotAudioConfig(audioConfig);
        magicActivityConfig.setTemplateVarNameValueMap(config.getTemplateVarValueMap());
        return magicActivityConfig;
    }

    @Override
    public Map<Long, Object> getLastPublishedSnapshotByBotIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        Map<Long, Object> result = new HashMap<>();
        for (Long botId : idList) {
            RobotSnapshotPO snapshot = getLastPublishRobotSnapshot(botId);
            if (Objects.nonNull(snapshot)) {
                result.put(botId, snapshot);
            }
        }
        return result;
    }

    @Override
    public JSONObject getAsrMetaData(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        AsrMetaData asrMetaData = new AsrMetaData();
        RobotSnapshotPO snapshot = getByVersion(botId, usageTarget, version);

        if (Objects.isNull(snapshot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人快照不存在");
        }

        BotPO bot = snapshot.getBot();
        asrMetaData.setBotPO(bot);
        AsrProviderPO asrProviderPO = asrProviderService.get(bot.getAsrProviderId());
        Assert.notNull(asrProviderPO, "asrProviderPO不能为空");
        asrMetaData.setAsrProviderPO(asrProviderPO);
        AsrVocabDetailPO asrVocabDetailPO = asrVocabService.getById(bot.getAsrVocabId());
        asrMetaData.setAsrVocabDetailPO(asrVocabDetailPO);
        AsrSelfLearningDetailPO asrSelfLearningDetailPO = asrSelfLearningService.getById(bot.getAsrSelfLearningDetailId());
        asrMetaData.setAsrSelfLearningDetailPO(asrSelfLearningDetailPO);
        asrMetaData.setAsrErrorCorrectionDetailPO(asrErrorCorrectionService.get(bot.getAsrErrorCorrectionDetailId()));
        AsrProviderEnum provider = asrProviderPO.getProvider();
        AsrVocabProviderRelationPO asrVocabProviderRelationPO = getAsrVocabProviderRelationPO(bot, asrVocabDetailPO, provider);
        asrMetaData.setAsrVocabProviderRelationPO(asrVocabProviderRelationPO);
        asrMetaData.setAsrSelfLearningProviderRelationPO(asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(bot.getAsrSelfLearningDetailId(), provider));
        //停用改为启用
        if (Objects.nonNull(asrVocabDetailPO) && asrVocabDetailPO.getStatus().equals(0)) {
            AsrVocabDetailVO updatePO = new AsrVocabDetailVO();
            updatePO.setAsrVocabId(asrVocabDetailPO.getAsrVocabId());
            asrVocabService.start(updatePO);
        }
        if (Objects.nonNull(asrSelfLearningDetailPO) && asrSelfLearningDetailPO.getStatus().equals(0)) {
            AsrSelfLearningDetailVO updatePO = new AsrSelfLearningDetailVO();
            updatePO.setAsrSelfLearningDetailId(asrSelfLearningDetailPO.getAsrSelfLearningDetailId());
            asrSelfLearningService.start(updatePO);
        }
        //反应灵敏度
        asrMetaData.setMaxSentenceSilence(bot.getMaxSentenceSilence());
        return JSONObject.parseObject(JsonUtils.object2String(asrMetaData));
    }

    private BotAsrConfig getAsrConfig(AsrConfigPO asrConfigSnapshot) {
        BotAsrConfig asrConfig = new BotAsrConfig();

        AsrProviderPO asrProviderPO = asrProviderService.get(asrConfigSnapshot.getAsrProviderId());
        Assert.notNull(asrProviderPO, "asrProviderPO不能为空");
        asrConfig.setAsrProvider(asrProviderPO.getProvider());
        asrConfig.setAsrAppkey(asrProviderPO.getModelId());
        asrConfig.setAsrParam(asrProviderPO.getParams());

        AsrVocabDetailPO asrVocabDetailPO = asrVocabService.getById(asrConfigSnapshot.getAsrVocabId());
        if (Objects.nonNull(asrVocabDetailPO)) {
            asrConfig.setAsrVocabDetailContentList(asrVocabDetailPO.getContent());
        }
        AsrProviderEnum provider = asrProviderPO.getProvider();
        AsrVocabProviderRelationPO asrVocabProviderRelationPO = getAsrVocabProviderRelationPO(asrConfigSnapshot, asrVocabDetailPO, provider);

        if (Objects.nonNull(asrVocabProviderRelationPO)) {
            asrConfig.setAsrVocabDetailId(asrVocabProviderRelationPO.getAsrVocabId());
            asrConfig.setAsrVocabId(asrVocabProviderRelationPO.getVocabularyId());
        }

        AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO = asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(asrConfigSnapshot.getAsrSelfLearningDetailId(), provider);
        if (Objects.nonNull(asrSelfLearningProviderRelationPO)) {
            asrConfig.setAsrSelfLearningDetailId(asrSelfLearningProviderRelationPO.getAsrSelfLearningId());
            asrConfig.setAsrSelfLearningId(asrSelfLearningProviderRelationPO.getProviderModelId());
        }

        AsrErrorCorrectionDetailPO asrErrorCorrectionDetail = asrErrorCorrectionService.get(asrConfigSnapshot.getAsrErrorCorrectionDetailId());
        if (Objects.nonNull(asrErrorCorrectionDetail)) {
            asrConfig.setAsrErrorCorrectionDetailId(asrErrorCorrectionDetail.getAsrErrorCorrectionDetailId());
        }

        //停用改为启用
        if (Objects.nonNull(asrVocabDetailPO) && asrVocabDetailPO.getStatus().equals(0)) {
            AsrVocabDetailVO updatePO = new AsrVocabDetailVO();
            updatePO.setAsrVocabId(asrVocabDetailPO.getAsrVocabId());
            asrVocabService.start(updatePO);
        }

        AsrSelfLearningDetailPO asrSelfLearningDetailPO = asrSelfLearningService.getById(asrConfigSnapshot.getAsrSelfLearningDetailId());
        if (Objects.nonNull(asrSelfLearningDetailPO) && asrSelfLearningDetailPO.getStatus().equals(0)) {
            AsrSelfLearningDetailVO updatePO = new AsrSelfLearningDetailVO();
            updatePO.setAsrSelfLearningDetailId(asrSelfLearningDetailPO.getAsrSelfLearningDetailId());
            asrSelfLearningService.start(updatePO);
        }

        //反应灵敏度
        asrConfig.setMaxSentenceSilence(asrConfigSnapshot.getMaxSentenceSilence());
        if (Objects.isNull(asrConfig.getMaxSentenceSilence())) {
            asrConfig.setMaxSentenceSilence(350);
        }

        if (StringUtils.isNotBlank(ApplicationConstant.ENABLE_ADVANCED_ASR_DELAY_START_BOT_IDS)
                && ApplicationConstant.ENABLE_ADVANCED_ASR_DELAY_START_BOT_IDS.contains(String.format(",%s,", asrConfigSnapshot.getBotId()))) {
            asrConfig.setEnableAdvancedAsrDelayStart(true);
        }

        if (BooleanUtils.isTrue(asrConfigSnapshot.getEnableAsrOptimization())
                || ApplicationConstant.ASR_OPTIMIZATION_BOT_IDS.contains(String.format(",%s,", asrConfigSnapshot.getBotId()))) {
            asrConfig.setEnableAsrOptimization(true);
        }

        asrConfig.setAsrOptimizationConfigJson(ApplicationConstant.ASR_OPTIMIZATION_CONFIG_JSON);

        return asrConfig;
    }

    private AsrVocabProviderRelationPO getAsrVocabProviderRelationPO(AsrConfigPO asrConfig, AsrVocabDetailPO asrVocabDetailPO, AsrProviderEnum provider) {
        AsrVocabProviderRelationPO asrVocabProviderRelationPO = asrVocabProviderRelationService.getByAsrVocabIdAndProvider(asrConfig.getAsrVocabId(), provider);

        //在厂商侧创建热词
        String providerAsrVocabId = "";
        try {
            if (Objects.nonNull(asrVocabDetailPO) && Objects.isNull(asrVocabProviderRelationPO)) {
                switch (provider) {
                    case ALI:
                        providerAsrVocabId = AliAsrVocabApiUtil.createAsrVocab(asrVocabDetailPO.getName(), asrVocabDetailPO.getDescription(), asrVocabDetailPO.getContent());
                        break;
                    case TENCENT:
                        providerAsrVocabId = TencentVocabApiUtil.create(asrVocabDetailPO.getName(), asrVocabDetailPO.getDescription(), asrVocabDetailPO.getContent());
                        break;
                    default:
                        break;
                }
                if (StringUtils.isNotEmpty(providerAsrVocabId)) {
                    addAsrVocabRelation(asrVocabDetailPO.getAsrVocabId(), providerAsrVocabId, provider);
                }
                asrVocabProviderRelationPO = asrVocabProviderRelationService.getByAsrVocabIdAndProvider(asrConfig.getAsrVocabId(), provider);
            }
        } catch (DuplicateKeyException e) {
            //如果有索引冲突，需要删除刚创建好的热词
            switch (provider) {
                case ALI:
                    AliAsrVocabApiUtil.deleteAsrVocab(providerAsrVocabId);
                    break;
                case TENCENT:
                    TencentVocabApiUtil.delete(providerAsrVocabId);
                    break;
                default:
                    break;
            }
            asrVocabProviderRelationPO = asrVocabProviderRelationService.getByAsrVocabIdAndProvider(asrConfig.getAsrVocabId(), provider);
        }
        return asrVocabProviderRelationPO;
    }

    private void addAsrVocabRelation(Long asrVocabId, String providerAsrVocabId, AsrProviderEnum asrProvider) {
        AsrVocabProviderRelationPO asrVocabProviderRelationPO = new AsrVocabProviderRelationPO();
        asrVocabProviderRelationPO.setProvider(asrProvider);
        asrVocabProviderRelationPO.setVocabularyId(providerAsrVocabId);
        asrVocabProviderRelationPO.setAsrVocabId(asrVocabId);
        asrVocabProviderRelationService.add(asrVocabProviderRelationPO);
    }

    @Override
    public JSONObject getAsrMetaDataByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return null;
        }
        if (Objects.isNull(version)) {
            version = getLastVersionNumber(botId, usageTarget);
        }
        return getAsrMetaData(botId, usageTarget, version);
    }

    private BotMetaData convert(RobotSnapshotPO snapshot, RobotSnapshotUsageTargetEnum usageTarget) {
        List<BaseAnswerContent> allAnswerList = getAllAnswer(snapshot);
        List<VariablePO> variableList = snapshot.getVariableList();
        Set<String> allVariableNameSet = variableList.stream().map(VariablePO::getName).collect(Collectors.toSet());

        BotConfigPO botConfig = snapshot.getBotConfig();
        BotAudioConfigPO audioConfig = botConfig == null ? snapshot.getBotAudioConfig() : botConfig.getAudioConfig();
        BotSpeechConfigPO speechConfig = botConfig == null ? null : botConfig.getSpeechConfig();
        Set<String> dynamicVariableNameSet = variableList.stream()
                .filter(v -> VariableTypeEnum.DYNAMIC.equals(v.getType()))
                .map(VariablePO::getName)
                .collect(Collectors.toSet());

        List<AnswerAudioMappingPO> audioMapping = snapshot.getAnswerAudioMappingList();
        Map<String, String> textAudioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(snapshot.getAnswerAudioMappingList())) {
            audioMapping.forEach(mapping -> {
                if (StringUtils.isNotBlank(mapping.getUrl())) {
                    textAudioMap.put(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(mapping.getText()), mapping.getUrl());
                }
            });
        }

        List<AnswerAudioMiddleResult> middleResults = allAnswerList.stream()
                .filter(answer -> StringUtils.isNotBlank(answer.getText()))
                .map(originAnswer -> convertAnswer(dynamicVariableNameSet, textAudioMap, originAnswer, audioConfig.getAudioType()))
                .collect(Collectors.toList());

        // 背景音配置
        boolean enableBackground = BooleanUtils.isTrue(audioConfig.getEnableBackground());
        AtomicReference<String> backgroundUrl = new AtomicReference<>();
        if (enableBackground && CollectionUtils.isNotEmpty(audioConfig.getBackgroundList())) {
            audioConfig.getBackgroundList().stream()
                    .filter(item -> BooleanUtils.isTrue(item.getEnable()))
                    .filter(item -> StringUtils.isNotBlank(item.getUrl()))
                    .findFirst()
                    .map(BackgroundAudioConfigPO::getUrl)
                    .ifPresent(backgroundUrl::set);
        }

        //机器人语音配置信息
        BotAudioConfig botAudioConfig = new BotAudioConfig();
        botAudioConfig.setAudioType(snapshot.getBotAudioConfig().getAudioType().getCode());
        botAudioConfig.setRecordUserId(snapshot.getBotAudioConfig().getRecordUserId());
        botAudioConfig.setEnableVariableAudio(BooleanUtils.isNotFalse(snapshot.getBotAudioConfig().getEnableVariableAudio()));
        TtsVoiceConfigPO ttsConfig = null;
        if (AudioTypeEnum.COMPOSE.equals(snapshot.getBotAudioConfig().getAudioType())) {
            ttsConfig = snapshot.getBotAudioConfig().getTtsConfig();
        } else {
            ttsConfig = snapshot.getBotAudioConfig().getSpliceTtsConfig();
        }
        if (Objects.nonNull(ttsConfig)) {
            botAudioConfig.setTtsSpeed(ttsConfig.getTtsSpeed());
            botAudioConfig.setTtsVolume(ttsConfig.getTtsVolume());
            botAudioConfig.setTtsVoice(ttsConfig.getTtsVoice());
        }

        BotMetaData result = new BotMetaData();
        // 超时设置等
        if (Objects.nonNull(speechConfig) && Objects.nonNull(speechConfig.getUserSilenceThreshold())) {
            result.setUserSilenceThreshold(speechConfig.getUserSilenceThreshold());
        }

        result.setUsageTarget(usageTarget);
        result.setVersion(snapshot.getVersion());
        result.setBotId(snapshot.getBotId());
        result.setName(snapshot.getBot().getName());
        result.setBotType(snapshot.getBot().getType());
        result.setIntentLevelTagId(snapshot.getBot().getIntentLevelTagId());
        result.setAllVariableNameSet(allVariableNameSet);
        result.setDynamicVariableNameSet(dynamicVariableNameSet);
        result.setAllAnswerList(middleResults);
        result.setBotAudioConfig(botAudioConfig);
        result.setEnableBackground(enableBackground && StringUtils.isNotBlank(backgroundUrl.get()));
        result.setBackgroundUrl(backgroundUrl.get());
        result.setVarNameInterpretTypeMap(MyCollectionUtils.listToConvertMap(variableList, VariablePO::getName, VariablePO::getInterpretType));
        result.setTemplateVarNameValueMap(snapshot.getTemplateVarNameValueMap());

        //语气词配置信息
        BotSpeechConfigPO botSpeechConfig = snapshot.getBotConfig().getSpeechConfig();
        if (botSpeechConfig != null) {
            result.setToneInterruptPercent(botSpeechConfig.getToneInterruptPercent());
            result.setEnableToneInterrupt(botSpeechConfig.getEnableToneInterrupt());
            result.setToneWordList(botSpeechConfig.getToneWordList());
            result.setEnableInputMerge(BooleanUtils.isNotFalse(botSpeechConfig.getEnableInputMerge()));
        }

        // nlp模型版本
        TrainResultPO trainResult = trainService.lastSuccess(ModelTrainKey.of(snapshot.getBotId()));
        if (Objects.nonNull(trainResult)) {
            result.setNlpModelVersion(trainResult.getModelName());
        }

        // 行业信息
        if (Objects.nonNull(snapshot.getBot().getIndustry())) {
            result.setIndustryType(snapshot.getBot().getIndustry().name());
        }
        if (Objects.nonNull(snapshot.getBot().getSubIndustry())) {
            result.setIndustrySubType(snapshot.getBot().getSubIndustry().name());
        }

        // asr配置
        result.setBotAsrConfig(getAsrConfig(snapshot.getBot()));
        result.getBotAsrConfig().setUsageTarget(usageTarget);

        // 一些配置信息
        result.setOperatorPromptAudioRegexList(ApplicationConstant.OPERATOR_PROMPT_AUDIO_REGEX_LIST);
        result.setOperatorPromptAudioPreRegexList(ApplicationConstant.OPERATOR_PROMPT_AUDIO_PRE_REGEX_LIST);
        result.setCheckPromptAudioBeforeSeconds(ApplicationConstant.OPERATOR_PROMPT_AUDIO_BEFORE_SECONDS);

        if (Objects.nonNull(snapshot.getBotConfig())
                && Objects.nonNull(snapshot.getBotConfig().getSpeechConfig())) {
            result.setDisablePromptAudioCheck(BooleanUtils.isTrue(snapshot.getBotConfig().getSpeechConfig().getDisablePromptAudioCheck()));
        }

        return result;
    }

    // 判断是否启用了大模型对话
    // 1. 启用了大模型流程
    // 2. 启用了大模型特殊语境
    private boolean checkEnableLLMChat(RobotSnapshotPO snapshot) {
        if (CollectionUtils.isNotEmpty(snapshot.getSpecialAnswerConfigList())) {
            for (SpecialAnswerConfigPO po : snapshot.getSpecialAnswerConfigList()) {
                if (SpecialAnswerConfigPO.LLM.equals(po.getName())) {
                    if (EnabledStatusEnum.ENABLE.equals(po.getEnabledStatus())) {
                        return true;
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(snapshot.getStepList())) {
            for (StepPO step : snapshot.getStepList()) {
                if (StepSubTypeEnum.isLlm(step.getSubType())) {
                    return true;
                }
            }
        }

        return false;
    }

    private AnswerAudioMiddleResult convertAnswer(Set<String> dynamicVariableNameSet,
                                                  Map<String, String> textAudioMap,
                                                  BaseAnswerContent originAnswer,
                                                  AudioTypeEnum audioType) {

        AnswerAudioMiddleResult item = new AnswerAudioMiddleResult();
        Tuple2<List<AnswerPlaceholderElement>, Set<String>> tuple2 = generateAnswerElement(originAnswer.getText(), textAudioMap, dynamicVariableNameSet, audioType);

        List<AnswerPlaceholderElement> elementList = tuple2._1();
        boolean containsVariable = CollectionUtils.isNotEmpty(tuple2._2());
        boolean containsDynamic = containsVariable && elementList.stream().anyMatch(AnswerPlaceholderElement::isDynamicVariable);
        boolean containsSeparator = elementList.stream().map(AnswerPlaceholderElement::getType).anyMatch(TextPlaceholderTypeEnum.SEPARATOR::equals);

        item.setContainsVariable(containsVariable);

        item.setContainsDynamicVariable(containsDynamic);
        item.setContainsSeparator(containsSeparator);
        item.setId(originAnswer.getUniqueId());
        item.setTemplate(StringUtils.trimToEmpty(originAnswer.getText()));
        item.setElementList(elementList);
        item.setVariableSet(tuple2._2());
        return item;
    }

    private Tuple2<List<AnswerPlaceholderElement>, Set<String>> generateAnswerElement(String text,
                                         Map<String, String> audioMap,
                                         Set<String> dynamicVariableNameSet,
                                         AudioTypeEnum audioType) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, AudioTypeEnum.MIXTURE.equals(audioType));
        List<AnswerPlaceholderElement> answerPlaceholderElementList = new ArrayList<>(splitter.getTextPlaceholderList().size());
        splitter.getTextPlaceholderList().forEach(holder -> {
            AnswerPlaceholderElement element = new AnswerPlaceholderElement();
            if (TextPlaceholderTypeEnum.TEXT.equals(holder.getType())) {
                element.setUrl(audioMap.get(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(holder.getValue())));
            } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(holder.getType())) {
                element.setDynamicVariable(dynamicVariableNameSet.contains(holder.getValue()));
            } else if (TextPlaceholderTypeEnum.TTS_SENTENCE.equals(holder.getType())) {
                // 整句合成, 需要特殊的处理, 判断这句话里面是否有动态变量
                AnswerPlaceholderSplitter subSplitter = new AnswerPlaceholderSplitter(holder.getValue(), false);
                element.setDynamicVariable(dynamicVariableNameSet.stream().anyMatch(subSplitter.getVariableSet()::contains));
            } else if (TextPlaceholderTypeEnum.SEPARATOR.equals(holder.getType()) && holder instanceof PauseSeparatorElement) {
                element.setUrl(SilenceAudioUtils.makeSilenceAudioAndUploadIfNotExists(((PauseSeparatorElement) holder).getPauseMs()));
            }
            element.setType(holder.getType());
            element.setValue(holder.getValue());
            element.setRealValue(holder.getValue());
            answerPlaceholderElementList.add(element);
        });
        return Tuple.of(answerPlaceholderElementList, splitter.getVariableSet());
    }

    private List<BaseAnswerContent> getAllAnswer(RobotSnapshotPO snapshot) {
        List<BaseAnswerContent> allAnswerList = new LinkedList<>();

        snapshot.getKnowledgeList().forEach(knowledge -> {
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                allAnswerList.addAll(knowledge.getAnswerList());
            }
        });
        snapshot.getNodeList().forEach(node -> {
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                allAnswerList.addAll(node.getAnswerList());
            }
        });
        snapshot.getSpecialAnswerConfigList().forEach(config -> {
            if (EnabledStatusEnum.ENABLE.equals(config.getEnabledStatus())
                    && CollectionUtils.isNotEmpty(config.getAnswerList())) {
                allAnswerList.addAll(config.getAnswerList());
            }
        });
        return allAnswerList;
    }

    @Override
    public RobotSnapshotPO getLastPublishRobotSnapshot(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("isPublish").is(true));
        query.with(Sort.by(Sort.Direction.DESC, "version")).limit(1);
        return mongoTemplate.findOne(query, RobotSnapshotPO.class, RobotSnapshotPO.COLLECTION_NAME);
    }

    @Override
    public void updateRobotSnapshotPublishStatus(Long botId, Integer version) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("version").is(version));
        Update update = new Update();
        update.set("isPublish", true);
        mongoTemplate.updateFirst(query, update, RobotSnapshotPO.COLLECTION_NAME);
    }

    @Override
    public void updateRobotSnapshotPublishStatus(Long botId) {
        Integer lastNumber = getLastVersionNumberForManualPublish(botId);
        if (Objects.isNull(lastNumber)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "不存在待审核的快照");
        }
        updateRobotSnapshotPublishStatus(botId, lastNumber);
    }
}
