package com.yiwise.dialogflow.service.impl.botgenerate;

import com.mongodb.client.result.UpdateResult;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.middleware.redis.service.RedisOpsService;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewriteProgressVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewriteRequestVO;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.botgenerate.BotAnswerRewriteTaskService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotAnswerRewriteTaskServiceImpl implements BotAnswerRewriteTaskService, ShutdownListener {
    private static final long MAX_WAIT_TIME_NS = 10 * 1000 * 1000 * 1000L;

    @Resource
    private BotService botService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Resource
    private StepService stepService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource(name = "longestLatencyRestTemplate")
    private RestTemplate restTemplate;

    @Resource
    private OperationLogService operationLogService;

    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    private final Thread taskExecutor = new Thread(this::run, "answerRewriteExecutor");

    @PostConstruct
    public void start() {
        if (ApplicationConstant.DIALOG_FLOW_WEB_APP_NAME.equals(ApplicationConstant.CURRENT_APPLICATION_NAME)) {
            taskExecutor.start();
        } else {
            log.info("当前应用不是dialogflow-web, 不启动bot答案改写任务轮询");
        }
    }

    /**
     * 提交一步任务, 通过数据库持久化任务
     * 考虑到后台改写可能会比较耗时, 所以需要持久化任务状态, 避免因为服务重启导致的任务中断
     */
    @Override
    public BotAnswerRewriteTaskPO submitRewriteTask(BotRewriteRequestVO request, Long userId) {
        Long botId = request.getBotId();
        Map<String, List<String>> rewritePromptMap = request.getOfferMap();
        rewritePromptMap.forEach((k, v) -> v.removeIf(StringUtils::isBlank));
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        if (BotGenerateStatusEnum.PROCESSING.equals(bot.getGenerateStatus())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "bot正在生成中, 请稍候重试");
        }

        botService.updateGenerateStatus(botId, BotGenerateStatusEnum.PROCESSING);
        // 开始改写时, 设置bot状态为改写中, 此时页面上当前bot无法再修改了
        BotAnswerRewriteTaskPO rewriteTask = new BotAnswerRewriteTaskPO();

        rewriteTask.setEnv(ApplicationConstant.CURR_ENV.name());
        rewriteTask.setSource(request.getSource());
        rewriteTask.setTemplateBotId(request.getTemplateBotId());
        rewriteTask.setBotId(botId);
        rewriteTask.setStatus(RewriteStatusEnum.QUEUING);
        rewriteTask.setProgress(RewriteProgressEnum.READY);
        rewriteTask.setCreateTime(LocalDateTime.now());
        rewriteTask.setUpdateTime(LocalDateTime.now());
        rewriteTask.setCreateUserId(userId);
        rewriteTask.setUpdateUserId(userId);
        rewriteTask.setLogId(MyRandomStringUtils.getRandomStringByLength(6));
        rewriteTask.setAllAnswerContentList(new ArrayList<>());
        rewriteTask.setSuccessAnswerContentList(new ArrayList<>());
        rewriteTask.setFailAnswerContentList(new ArrayList<>());
        rewriteTask.setRewritePromptMap(rewritePromptMap);

        deleteCancelSignal(botId);
        mongoTemplate.insert(rewriteTask, BotAnswerRewriteTaskPO.COLLECTION_NAME);
        log.info("提交bot答案改写任务成功, botId: {}, rewriteTaskId: {}", botId, rewriteTask.getId());
        LockSupport.unpark(taskExecutor);
        return rewriteTask;
    }

    @Override
    public List<BotAnswerRewriteTaskPO> cancelRewriteTask(Long botId, Long userId) {
        // 取消的逻辑就是讲取消信号设置在redis中, 同时将任务状态设置为已取消
        // 在异步改写时, 没改写完一句, 检查是否有取消信号, 如果有, 则直接退出改写

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("status").in(RewriteStatusEnum.QUEUING, RewriteStatusEnum.PROCESSING));

        List<BotAnswerRewriteTaskPO> rewriteTaskList = mongoTemplate.find(query, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(rewriteTaskList)) {
            return Collections.emptyList();
        }
        for (BotAnswerRewriteTaskPO task : rewriteTaskList) {
            task.setStatus(RewriteStatusEnum.CANCEL);
            updateTaskStatus(task);
        }

        String botGenerateCancelSignalKey = RedisKeyCenter.getBotGenerateCancelSignalKey(botId);
        redisOpsService.set(botGenerateCancelSignalKey, System.currentTimeMillis(), 1, TimeUnit.HOURS);
        log.info("redis设置bot答案改写取消信号成功, botId: {}", botId);
        return rewriteTaskList;
    }

    private Optional<Long> getCancelSignal(Long botId) {
        String botGenerateCancelSignalKey = RedisKeyCenter.getBotGenerateCancelSignalKey(botId);
        return Optional.ofNullable(redisOpsService.get(botGenerateCancelSignalKey, Long.class));
    }

    private void deleteCancelSignal(Long botId) {
        String botGenerateCancelSignalKey = RedisKeyCenter.getBotGenerateCancelSignalKey(botId);
        redisOpsService.delete(botGenerateCancelSignalKey);
    }

    private void run() {
        try {
            TimeUnit.SECONDS.sleep(60);
        } catch (Exception ignore) {}
        log.info("开始轮询获取bot答案改写任务");
        // 从数据库轮询获取任务
        String oldLogId = "BotAnswerRewriteTask";
        OperationLogService.setIgnoreSign();
        while (!shutdown.get()) {
            try {
                MDC.put(ApplicationConstant.MDC_LOG_ID, oldLogId);
                Optional<BotAnswerRewriteTaskPO> taskOptional = getAvailableTask();
                if (taskOptional.isPresent()) {
                    // 执行任务
                    // 一个服务实例同时仅处理一个任务,
                    String logId = taskOptional.get().getLogId();
                    if (StringUtils.isBlank(logId)) {
                        logId = MyRandomStringUtils.getRandomStringByLength(6);
                        taskOptional.get().setLogId(logId);
                    }
                    MDC.put(ApplicationConstant.MDC_LOG_ID, logId);
                    try {
                        executeTask(taskOptional.get());
                    } catch (Exception e) {
                        log.warn("执行bot答案改写任务失败, botId: {}, rewriteTaskId: {}", taskOptional.get().getBotId(), taskOptional.get().getId(), e);
                    } finally {
                        MDC.put(ApplicationConstant.MDC_LOG_ID, oldLogId);
                    }
                } else {
                    LockSupport.parkNanos(MAX_WAIT_TIME_NS);
                }
            } catch (Exception e) {
                log.warn("[LogHub_Warn]轮询获取bot答案改写任务失败", e);
            }
        }
        log.info("接收到挂机信号, 退出改写任务轮询");
    }

    /**
     * 在TaskExecutor线程中执行
     */
    private void executeTask(BotAnswerRewriteTaskPO rewriteTask) {
        if (Objects.isNull(rewriteTask)) {
            log.warn("任务不存在");
            return;
        }

        // 检查任务的状态
        switch (rewriteTask.getStatus()) {
            case QUEUING:
                taskStepExecuteTemplate(rewriteTask.getBotId(), onTaskCancel(rewriteTask), () -> {
                    rewriteTask.setStatus(RewriteStatusEnum.PROCESSING);
                    rewriteTask.setProgress(RewriteProgressEnum.READY);
                    rewriteTask.setExecuteHost(ServerInfoConstants.SERVER_HOSTNAME);
                    updateTaskStatus(rewriteTask);
                });
            case PROCESSING:
                taskStepExecuteTemplate(rewriteTask.getBotId(), onTaskCancel(rewriteTask), () -> processTask(rewriteTask));
                break;
            case FAIL:
            case SUCCESS:
            default:
                log.warn("任务状态异常, 任务id: {}, 状态: {}", rewriteTask.getId(), rewriteTask.getStatus());
        }
    }

    private Runnable onTaskCancel(BotAnswerRewriteTaskPO task) {
        return () -> {
            log.info("任务被取消, botId: {}", task.getBotId());
            task.setStatus(RewriteStatusEnum.CANCEL);
            updateTaskStatus(task);
        };
    }

    private void taskStepExecuteTemplate(Long botId, Runnable cancelStep, Runnable nextStep) {
        if (shutdown.get()) {
            throw new ShutdownException();
        }
        Optional<Long> cancelSignal = getCancelSignal(botId);
        if (cancelSignal.isPresent()) {
            cancelStep.run();
            throw new TaskCancelException();
        } else {
            try {
                nextStep.run();
            } catch (ShutdownException shutdownException ) {
              throw shutdownException;
            } catch (Exception e) {
                log.warn("执行任务失败, botId: {}", botId, e);
                throw new TaskExecuteException(e);
            }
        }
    }

    /**
     * 在TaskExecutor线程中执行
     * 处理任务
     */
    private void processTask(final BotAnswerRewriteTaskPO rewriteTask) {
        if (Objects.isNull(rewriteTask)) {
            log.warn("任务不存在");
            return;
        }
        if (!RewriteStatusEnum.PROCESSING.equals(rewriteTask.getStatus())) {
            log.warn("任务状态异常, 任务id: {}, 状态: {}", rewriteTask.getId(), rewriteTask.getStatus());
            return;
        }
        Long botId = rewriteTask.getBotId();
        try {
            final AtomicReference<RobotSnapshotPO> botSnapshotRef = new AtomicReference<>();
            switch (rewriteTask.getProgress()) {
                case READY:
                    taskStepExecuteTemplate(botId, onTaskCancel(rewriteTask), () -> {
                        // 获取bot的所有数据
                        botSnapshotRef.set(robotSnapshotService.getRealtimeResourceSnapshot(rewriteTask.getBotId()));
                        if (Objects.isNull(botSnapshotRef.get())) {
                            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在: 获取bot快照失败");
                        }
                        ContextAnalyzeResult contextAnalysisResult = requestAnalyzeContext(rewriteTask, botSnapshotRef.get());
                        rewriteTask.setContextAnalysisJsonResult(JsonUtils.object2String(contextAnalysisResult));
                        rewriteTask.setProgress(RewriteProgressEnum.ANALYZED);
                        updateTask(rewriteTask);
                    });
                case ANALYZED:
                    taskStepExecuteTemplate(botId, onTaskCancel(rewriteTask), () -> {
                        if (botSnapshotRef.get() == null) {
                            botSnapshotRef.set(robotSnapshotService.getRealtimeResourceSnapshot(rewriteTask.getBotId()));
                        }
                        // 获取
                        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> answerContentList = prepareAnswerList(botSnapshotRef.get());
                        rewriteTask.setAllAnswerContentList(answerContentList);
                        rewriteTask.setProgress(RewriteProgressEnum.REWRITING);
                        updateTask(rewriteTask);
                    });
                case REWRITING:
                    taskStepExecuteTemplate(botId, onTaskCancel(rewriteTask), () -> processAnswerRewrite(rewriteTask, botId));
                case REWRITE:
                    taskStepExecuteTemplate(botId, onTaskCancel(rewriteTask), () -> {
                        // 更新答案
                        BotAnswerRewriteTaskPO newestTask = getById(rewriteTask.getId());
                        if (CollectionUtils.isNotEmpty(newestTask.getFailAnswerContentList())) {
                            newestTask.setProgress(RewriteProgressEnum.APPLY);
                            newestTask.setStatus(RewriteStatusEnum.FAIL);
                            updateTaskStatus(newestTask);
                        } else {
                            replaceAllAnswer(newestTask);
                            newestTask.setProgress(RewriteProgressEnum.APPLY);
                            newestTask.setStatus(RewriteStatusEnum.SUCCESS);
                            updateTaskStatus(newestTask);
                        }
                    });
                case APPLY:
                    rewriteTask.setProgress(RewriteProgressEnum.DONE);
                    rewriteTask.setStatus(RewriteStatusEnum.SUCCESS);
                    updateTaskStatus(rewriteTask);
                    // 创建操作日志
                    createSuccessOperationLog(rewriteTask);
                default:
                    break;
            }
        } catch (ShutdownException shutdownException) {
            // 服务关机中, 保持任务状态不变, 在服务重新启动后, 继续
            log.info("服务关机中, 任务暂停, botId: {}", botId);
        } catch (Exception e) {
            log.warn("执行任务失败, botId: {}", botId, e);
            rewriteTask.setStatus(RewriteStatusEnum.FAIL);
            rewriteTask.setProgress(RewriteProgressEnum.DONE);
            rewriteTask.setFailReason(e.getMessage());
            updateTaskStatus(rewriteTask);
            // 创建失败的操作日志
            createFailOperationLog(rewriteTask);
        }
    }

    private void createFailOperationLog(BotAnswerRewriteTaskPO rewriteTask) {
        OperationLogTypeEnum type = OperationLogTypeEnum.AI_GENERATE;
        if (RewriteTaskSourceEnum.AI_REWRITE.equals(rewriteTask.getSource())
                || Objects.equals(rewriteTask.getTemplateBotId(), rewriteTask.getBotId())) {
            type = OperationLogTypeEnum.AI_REWRITE;
        }
        OperationLogService.clearIgnoreSign();
        String detail = OperationLogTypeEnum.AI_REWRITE.equals(type) ? "话术改写失败" : String.format("由话术ID%s生成本新话术, 生成失败", rewriteTask.getTemplateBotId());
        try {
            operationLogService.save(rewriteTask.getBotId(), type, OperationLogResourceTypeEnum.BOT, detail, rewriteTask.getCreateUserId());
        } finally {
            OperationLogService.setIgnoreSign();
        }
    }

    private void createSuccessOperationLog(BotAnswerRewriteTaskPO rewriteTask) {
        OperationLogTypeEnum type = OperationLogTypeEnum.AI_GENERATE;
        if (RewriteTaskSourceEnum.AI_REWRITE.equals(rewriteTask.getSource())
                || Objects.equals(rewriteTask.getTemplateBotId(), rewriteTask.getBotId())) {
            type = OperationLogTypeEnum.AI_REWRITE;
        }
        String detail = OperationLogTypeEnum.AI_REWRITE.equals(type) ? "话术进行改写" : String.format("由话术ID%s生成本新话术", rewriteTask.getTemplateBotId());
        OperationLogService.clearIgnoreSign();
        try {
            operationLogService.save(rewriteTask.getBotId(), type, OperationLogResourceTypeEnum.BOT, detail, rewriteTask.getCreateUserId());
        } finally {
            OperationLogService.setIgnoreSign();
        }
    }

    private String convertToGroupKey(AnswerSourceEnum sourceType, String resourceId) {
        return convertToGroupKey(sourceType, resourceId, 0);
    }

    private String convertToGroupKey(AnswerSourceEnum sourceType, String resourceId, Integer groupIndex) {
        switch (sourceType) {
            case STEP:
                return String.format("%s:%s:%s", sourceType.name(), resourceId, groupIndex);
            case SPECIAL_ANSWER:
            case KNOWLEDGE:
            default:
                return String.format("%s:%s", sourceType.name(), resourceId);
        }
    }

    private void processAnswerRewrite(BotAnswerRewriteTaskPO rewriteTask, Long botId) {
        // 请求rewrite接口
        log.info("开始请求rewrite接口, botId: {}", rewriteTask.getBotId());
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> allAnswerContentList = rewriteTask.getAllAnswerContentList();
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> successList = rewriteTask.getSuccessAnswerContentList();
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> failList = rewriteTask.getFailAnswerContentList();
        Set<String> processedGroupKeySet = new ConcurrentSkipListSet<>();
        successList.stream()
                .map(BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup::getGroupKey)
                .forEach(processedGroupKeySet::add);
        failList.stream()
                .map(BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup::getGroupKey)
                .forEach(processedGroupKeySet::add);
        // 未处理列表
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> unprocessedList = allAnswerContentList.stream()
                .filter(group -> !processedGroupKeySet.contains(group.getGroupKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unprocessedList)) {
            processByList(rewriteTask, botId, unprocessedList);
        }
        rewriteTask.setProgress(RewriteProgressEnum.REWRITE);
        updateTaskStatus(rewriteTask);
    }

    private void processByList(BotAnswerRewriteTaskPO rewriteTask, Long botId, List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> shardList) {
        Map<String, Object> contextAnalysis = getContextAnalysis(rewriteTask);
        for (BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup group : shardList) {
            taskStepExecuteTemplate(botId, onTaskCancel(rewriteTask), () -> {
                try {
                    rewriteAnswer(contextAnalysis, group);
                    pushSuccessList(rewriteTask.getBotId(), rewriteTask.getId(), group);
                    sendProgressMsg(rewriteTask);
                } catch (Exception e) {
                    log.warn("改写答案失败, botId: {}, groupKey: {}", rewriteTask.getBotId(), group.getGroupKey(), e);
                    group.setFailReason(e.getMessage());
                    pushFailList(rewriteTask.getBotId(), rewriteTask.getId(), group);
                    sendProgressMsg(rewriteTask);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private Map<String, Object> getContextAnalysis(BotAnswerRewriteTaskPO task) {
        if (MapUtils.isNotEmpty(task.getContextAnalysis())) {
            return task.getContextAnalysis();
        }
        if (StringUtils.isNotBlank(task.getContextAnalysisJsonResult())) {
            return JsonUtils.string2Object(task.getContextAnalysisJsonResult(), ContextAnalyzeResult.class).getContext_analysis();
        }
        return Collections.emptyMap();
    }

    /**
     * 替换所有的答案
     */
    private void replaceAllAnswer(BotAnswerRewriteTaskPO rewriteTask) {
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(rewriteTask.getBotId());
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(rewriteTask.getBotId());
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(rewriteTask.getBotId());

        Map<String, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> rewriteGroupMap = MyCollectionUtils.listToMap(rewriteTask.getSuccessAnswerContentList(),
                BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup::getGroupKey);

        // 改写流程节点
        stepNodeListMap.forEach((stepId, stepNodeList) -> {
            for (DialogBaseNodePO node : stepNodeList) {
                if (node instanceof DialogCollectNodePO) {
                    DialogCollectNodePO collectNode = (DialogCollectNodePO) node;
                    for (int answerGroupIndex = 0; answerGroupIndex < collectNode.getEntityCollectList().size(); answerGroupIndex++) {
                        CollectNodeEntityItemPO answerGroup = collectNode.getEntityCollectList().get(answerGroupIndex);
                        if (CollectionUtils.isNotEmpty(answerGroup.getAnswerList())) {
                            String groupKey = convertToGroupKey(AnswerSourceEnum.STEP, node.getId(), answerGroupIndex);
                            BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup rewriteGroup = rewriteGroupMap.get(groupKey);
                            if (Objects.nonNull(rewriteGroup)) {
                                answerGroup.setAnswerList(replaceAnswer(rewriteGroup, answerGroup.getAnswerList(), NodeAnswer::new));
                            }
                        }
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                        String groupKey = convertToGroupKey(AnswerSourceEnum.STEP, node.getId());
                        BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup rewriteGroup = rewriteGroupMap.get(groupKey);
                        if (Objects.nonNull(rewriteGroup)) {
                            node.setAnswerList(replaceAnswer(rewriteGroup, node.getAnswerList(), NodeAnswer::new));
                        }
                    }
                }
            }
            stepNodeService.autoSave(rewriteTask.getBotId(), stepId, stepNodeList, rewriteTask.getCreateUserId());
        });
        // 改写问答知识
        for (KnowledgePO knowledge : knowledgeList) {
            if ( CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                String groupKey = convertToGroupKey(AnswerSourceEnum.KNOWLEDGE, knowledge.getId());
                BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup rewriteGroup = rewriteGroupMap.get(groupKey);
                if (Objects.nonNull(rewriteGroup)) {
                    List<KnowledgeAnswer> newAnswerList = replaceAnswer(rewriteGroup, knowledge.getAnswerList(), KnowledgeAnswer::new);
                    knowledge.setAnswerList(newAnswerList);
                    newAnswerList.forEach(this::resetPostActionIfNull);
                    knowledgeService.updateAnswerList(knowledge);
                }
            }
        }
        // 改写特殊语境
        for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
            if (CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                String groupKey = convertToGroupKey(AnswerSourceEnum.SPECIAL_ANSWER, specialAnswerConfig.getId());
                BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup rewriteGroup = rewriteGroupMap.get(groupKey);
                if (Objects.nonNull(rewriteGroup)) {
                    List<KnowledgeAnswer> newAnswerList = replaceAnswer(rewriteGroup, specialAnswerConfig.getAnswerList(), KnowledgeAnswer::new);
                    specialAnswerConfig.setAnswerList(newAnswerList);
                    newAnswerList.forEach(this::resetPostActionIfNull);
                    specialAnswerConfigService.updateAnswerList(specialAnswerConfig);
                }
            }
        }
    }

    /**
     * 替换单个节点/知识的答案列表
     */
    private <T extends BaseAnswerContent> List<T> replaceAnswer(BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup group,
                                                                List<T> answerList,
                                                                Supplier<T> supplier) {
        if (CollectionUtils.isEmpty(answerList)) {
            return answerList;
        }
        List<T> result = new ArrayList<>();
        answerList = answerList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getText()))
                .collect(Collectors.toList());
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetail> finalDetailList = group.getDetailList().stream()
                .filter(item -> BooleanUtils.isNotTrue(item.isDeleteAnswer()))
                .collect(Collectors.toList());
        for (int i = 0; i < finalDetailList.size(); i++) {
            BotAnswerRewriteTaskPO.AnswerRewriteDetail detail = finalDetailList.get(i);
            Optional<T> targetAnswerOpt = getByIndex(answerList, i);
            if (!targetAnswerOpt.isPresent()) {
                T answer = supplier.get();
                answer.setText(detail.getRewriteResult());
                result.add(answer);
            } else {
                targetAnswerOpt.get().setText(detail.getRewriteResult());
                result.add(targetAnswerOpt.get());
            }
        }
        return result;
    }

    private void resetPostActionIfNull(KnowledgeAnswer knowledgeAnswer) {
        if (Objects.isNull(knowledgeAnswer.getPostAction())) {
            if (matchHangup(knowledgeAnswer.getText())) {
                knowledgeAnswer.setPostAction(PostActionTypeEnum.HANG_UP);
            } else {
                knowledgeAnswer.setPostAction(PostActionTypeEnum.WAIT);
            }
        }
    }

    private boolean matchHangup(String answerText) {
        if (StringUtils.isBlank(answerText)) {
            return false;
        }
        return answerText.contains("挂机") || answerText.contains("再见") || answerText.contains("拜拜");
    }

    private <T extends BaseAnswerContent> Optional<T> getByIndex(List<T> list, int index) {
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        if (index >= list.size()) {
            return Optional.empty();
        }
        return Optional.of(list.get(index));
    }

    private void pushFailList(Long botId, String taskId, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup answerRewriteDetail) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(taskId).and("botId").is(botId));
        Update update = new Update();
        update.push("failAnswerContentList", answerRewriteDetail);
        mongoTemplate.updateFirst(query, update, BotAnswerRewriteTaskPO.COLLECTION_NAME);
    }

    private void pushSuccessList(Long botId, String taskId, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup group) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(taskId).and("botId").is(botId));
        Update update = new Update();
        update.push("successAnswerContentList", group);
        mongoTemplate.updateFirst(query, update, BotAnswerRewriteTaskPO.COLLECTION_NAME);
    }

    /**
     * 请求算法rewrite接口
     */
    private void rewriteAnswer(Map<String, Object> contextAnalysis, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup group) {
        List<String> answerTextList = group.getDetailList().stream()
                .map(BotAnswerRewriteTaskPO.AnswerRewriteDetail::getAnswerContent)
                .map(BaseAnswerContent::getText)
                .collect(Collectors.toList());
        String label = "";
        switch (group.getGroupLocate().getAnswerSource()) {
            case KNOWLEDGE: label = group.getGroupLocate().getKnowledgeLabel(); break;
            case STEP: label = group.getGroupLocate().getNodeLabel(); break;
            case SPECIAL_ANSWER: label = group.getGroupLocate().getSpecialAnswerConfigLabel(); break;
            default: break;
        }

        List<String> rewriteResultList = requestRewrite(contextAnalysis, label, answerTextList);

        // 判断是添加答案还是删除答案
        if (rewriteResultList.size() == answerTextList.size()) {
            // 答案数量不变
            for (int i = 0; i < group.getDetailList().size(); i++) {
                group.getDetailList().get(i).setRewriteResult(rewriteResultList.get(i));
            }
        } else if (rewriteResultList.size() < answerTextList.size()) {
            // 删答案
            for (int i = 0; i < group.getDetailList().size(); i++) {
                if (i < rewriteResultList.size()) {
                    group.getDetailList().get(i).setRewriteResult(rewriteResultList.get(i));
                } else {
                    group.getDetailList().get(i).setRewriteResult("");
                    group.getDetailList().get(i).setDeleteAnswer(true);
                }
            }
        } else {
            // 新增答案
            for (int i = 0; i < rewriteResultList.size(); i++) {
                String rewriteResult = rewriteResultList.get(i);
                if (i < group.getDetailList().size()) {
                    group.getDetailList().get(i).setRewriteResult(rewriteResult);
                } else {
                    BotAnswerRewriteTaskPO.AnswerRewriteDetail detail = new BotAnswerRewriteTaskPO.AnswerRewriteDetail();
                    detail.setNewAnswer(true);
                    detail.setRewriteResult(rewriteResult);
                    group.getDetailList().add(detail);
                }
            }
        }
    }

    @Override
    public List<String> requestRewrite(Map<String, Object> contextAnalysis, String label, List<String> answerTextList) {
        return checkTokenAndExecute(5, () -> {
            Map<String, Object> param = new HashMap<>();
            param.put("text_list", answerTextList);
            param.put("node_label", label);
            param.put("context_analysis", contextAnalysis);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2String(param), headers);
            ResponseEntity<String> response = restTemplate.exchange(ApplicationConstant.BOT_REWRITE_URL, HttpMethod.POST, httpEntity, String.class);
            String json = response.getBody();
            log.info("请求rewrite接口成功, response: {}", json);
            RewriteResult rewriteResult = JsonUtils.string2Object(json, RewriteResult.class);
            updateToken(rewriteResult.getTokens_count());
            if (CollectionUtils.isNotEmpty(rewriteResult.getRewrite_list())) {
                return rewriteResult.getRewrite_list().stream().map(RewriteItem::getText).collect(Collectors.toList());
            } else {
                return Collections.emptyList();
            }
        });
    }

    private <T> T checkTokenAndExecute(int maxRetry, Supplier<T> supplier) {
        Exception exception = null;
        for (int tryCount = 0; tryCount < maxRetry;) {
            // 每分钟12万token限制
            // 先按每分钟为一个窗口期, 后面如果请求很大, 再将窗口设小
            long currentEpochSecond = getCurrentEpochSecond();
            String tokenKey = getRewriteTokenKey(currentEpochSecond);
            Boolean init = redisOpsService.setIfAbsent(tokenKey, 0, 80);
            long used = 0;
            if (BooleanUtils.isTrue(init) || (used = redisOpsService.incrementKey(tokenKey, 0))< 120000) {
                tryCount++;
                try {
                    return supplier.get();
                } catch (Exception e) {
                    exception = e;
                }
            } else {
                // token用完了, 需要阻塞
                long leftSeconds = 60 - (currentEpochSecond % 60);
                log.info("token用完了, 需要阻塞, tokenKey: {}, used: {}, sleepSeconds: {}", tokenKey, used, leftSeconds);
                try {
                    TimeUnit.SECONDS.sleep(leftSeconds);
                } catch (InterruptedException e) {
                    log.warn("sleep error", e);
                }
            }
        }
        throw new RuntimeException(exception);
    }

    private void updateToken(Long tokensCount) {
        if (Objects.isNull(tokensCount)) {
            return;
        }
        long currentEpochSecond = getCurrentEpochSecond();
        String tokenKey = getRewriteTokenKey(currentEpochSecond);
        long updated = redisOpsService.incrementKey(tokenKey, tokensCount);
        log.info("更新token, tokenKey: {}, tokensCount: {}, updated: {}", tokenKey, tokensCount, updated);
    }

    private String getRewriteTokenKey(long currentEpochSecond) {
        long currentEpochMinute = currentEpochSecond / 60;
        return RedisKeyCenter.getRewriteTokenKey(currentEpochMinute);
    }

    @Override
    public Optional<BotAnswerRewriteTaskPO> getLastTaskByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME));
    }

    @Override
    public Optional<BotAnswerRewriteTaskPO> getLastApplySuccessTaskByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                        .addCriteria(Criteria.where("status").is(RewriteStatusEnum.SUCCESS));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME));
    }

    @Override
    public Map<String, List<String>> analyzePrompt(Long botId) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        RobotSnapshotPO botSnapshot = robotSnapshotService.getRealtimeResourceSnapshot(botId);
        Map<String, List<String>> result = requestAnalyzePrompt(botSnapshot).getContext();

        // 对结果进行过滤, 主利益点，挽回利益点，链路仅支持一个答案
        result.forEach((k,v) -> {
            if ("主利益点".equals(k) || "挽回利益点".equals(k) || "链路".equals(k)) {
                if (CollectionUtils.isNotEmpty(v) && v.size() > 1) {
                    String first = v.get(0);
                    v.clear();
                    v.add(first);
                }
            }
        });

        return result;
    }

    @Override
    public List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> getRealtimeAnswerGroup(Long botId) {
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        return prepareAnswerList(stepList, nodeList, knowledgeList, specialAnswerConfigList);
    }

    @Override
    public Set<Long> getExistSuccessTaskBotIdSet(List<Long> botIdList) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").in(botIdList)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("status").is(RewriteStatusEnum.SUCCESS)));

        AggregationOperation groupOperation = Aggregation.group("botId")
                .first("botId").as("botId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        List<BotAnswerRewriteTaskPO> statsList = mongoTemplate.aggregate(aggregation, BotAnswerRewriteTaskPO.COLLECTION_NAME, BotAnswerRewriteTaskPO.class)
                .getMappedResults();

        return statsList.stream()
                .map(BotAnswerRewriteTaskPO::getBotId)
                .collect(Collectors.toSet());
    }

    public PromptAnalyzeResult requestAnalyzePrompt(RobotSnapshotPO botSnapshot) {
        return checkTokenAndExecute(5, () -> {
            Map<String, Object> param = new HashMap<>();
            param.put("version", "2.0");
            param.put("dialogue", botSnapshot);
            param.put("namespace", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2String(param), headers);
            log.info("请求analyze_prompt接口, botId: {}, requestJson: {}", botSnapshot.getBotId(), JsonUtils.object2PrettyString(param));
            ResponseEntity<String> response = restTemplate.exchange(ApplicationConstant.BOT_REWRITE_ANALYZE_PROMPT_URL, HttpMethod.POST, httpEntity, String.class);
            log.info("请求analyze_prompt接口, botId: {}, originResponse: {}", botSnapshot.getBotId(), response.getBody());
            PromptAnalyzeResult result = JsonUtils.string2Object(response.getBody(), PromptAnalyzeResult.class);
            log.info("请求analyze_prompt接口, responseJson: {}", JsonUtils.object2String(result));
            updateToken(result.tokens_count);
            return result;
        });
    }

    private long getCurrentEpochMinute() {
        return System.currentTimeMillis() / 1000 / 60;
    }

    private long getCurrentEpochSecond() {
        return System.currentTimeMillis() / 1000;
    }

    @Override
    public ContextAnalyzeResult requestAnalyzeContext(BotAnswerRewriteTaskPO rewriteTask, RobotSnapshotPO botSnapshot) {
        return checkTokenAndExecute(5, () -> {
            Map<String, Object> param = new HashMap<>();
            param.put("version", "2.0");
            param.put("context", rewriteTask.getRewritePromptMap());
            param.put("dialogue", botSnapshot);
            param.put("ope_domain", ApplicationConstant.ALGORITHM_SERVICE_OPE_DOMAIN);
            param.put("namespace", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2String(param), headers);
            log.info("请求analyze_context接口, botId: {}, requestJson: {}", rewriteTask.getBotId(), JsonUtils.object2PrettyString(param));
            ResponseEntity<String> response = restTemplate.exchange(ApplicationConstant.BOT_REWRITE_ANALYZE_CONTEXT_URL, HttpMethod.POST, httpEntity, String.class);
            log.info("请求analyze_context接口, botId: {}, originResponse: {}", rewriteTask.getBotId(), response.getBody());
            ContextAnalyzeResult result = JsonUtils.string2Object(response.getBody(), ContextAnalyzeResult.class);
            log.info("请求analyze_context接口, responseJson: {}", JsonUtils.object2String(result));
            updateToken(result.tokens_count);
            return result;
        });
    }

    private List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> prepareAnswerList(RobotSnapshotPO botSnapshot) {
        return prepareAnswerList(botSnapshot.getStepList(),
                botSnapshot.getNodeList(),
                botSnapshot.getKnowledgeList(),
                botSnapshot.getSpecialAnswerConfigList());
    }

    private List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> prepareAnswerList(List<StepPO> stepList,
                                                                                    List<DialogBaseNodePO> nodeList,
                                                                                    List<KnowledgePO> knowledgeList,
                                                                                    List<SpecialAnswerConfigPO> specialAnswerConfigList) {
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> result = new ArrayList<>();
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        nodeList.forEach(node -> {
            StepPO step = stepMap.get(node.getStepId());
            if (Objects.nonNull(step) && CollectionUtils.isNotEmpty(node.getAnswerList())) {
                AnswerLocateBO nodeLocate = AnswerLocateUtils.generate(step, node);
                if (node instanceof DialogCollectNodePO) {
                    DialogCollectNodePO collectNode = (DialogCollectNodePO) node;
                    if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                        for (int i = 0; i < collectNode.getEntityCollectList().size(); i++) {
                            CollectNodeEntityItemPO answerGroup = collectNode.getEntityCollectList().get(i);
                            result.addAll(convertAnswerList(answerGroup.getAnswerList(), nodeLocate, i));
                        }
                    }
                } else {
                    result.addAll(convertAnswerList(node.getAnswerList(), nodeLocate));
                }
            }
        });
        for (KnowledgePO knowledge : knowledgeList) {
            AnswerLocateBO knowledgeLocate = AnswerLocateUtils.generate(knowledge);
            result.addAll(convertAnswerList(knowledge.getAnswerList(), knowledgeLocate));
        }
        for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
            AnswerLocateBO knowledgeLocate = AnswerLocateUtils.generate(specialAnswerConfig);
            result.addAll(convertAnswerList(specialAnswerConfig.getAnswerList(), knowledgeLocate));
        }
        return result;
    }

    private static List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> convertAnswerList(List<? extends BaseAnswerContent> answerList,
                                                                                           AnswerLocateBO parentLocate) {
        return convertAnswerList(answerList, parentLocate, 0);
    }

    private static List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> convertAnswerList(List<? extends BaseAnswerContent> answerList,
                                                                                           AnswerLocateBO parentLocate,
                                                                                           int nodeAnswerGroupIndex) {
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(answerList)) {
            BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup group = new BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup();
            group.setDetailList(new ArrayList<>());
            result.add(group);
            AtomicInteger index = new AtomicInteger(0);
            for (BaseAnswerContent answer : answerList) {
                if (StringUtils.isNotBlank(answer.getText())) {
                    AnswerLocateBO locate = MyBeanUtils.copy(parentLocate, AnswerLocateBO.class);
                    locate.setAnswerId(answer.getUniqueId());
                    locate.setAnswerLabel(answer.getLabel());
                    locate.setIndex(index.getAndIncrement());
                    locate.setGroupIndex(nodeAnswerGroupIndex);

                    if (Objects.isNull(group.getGroupLocate())) {
                        group.setGroupLocate(locate);
                    }
                    group.getDetailList().add(BotAnswerRewriteTaskPO.AnswerRewriteDetail.builder()
                            .answerContent(answer)
                            .locate(locate)
                            .build());
                }
            }
        }
        return result;
    }

    // 获取第一个可执行的任务
    private Optional<BotAnswerRewriteTaskPO> getAvailableTask() {
        // 1. 先获取当前服务未处理完成的任务
        Optional<BotAnswerRewriteTaskPO> ownProcessingTask = getOwnProcessingTask();
        if (ownProcessingTask.isPresent()) {
            return ownProcessingTask;
        }
        // 获取派对中的任务
        Optional<BotAnswerRewriteTaskPO> processingTask = getFirstQueuedTask();
        while (processingTask.isPresent()) {
            BotAnswerRewriteTaskPO task = processingTask.get();
            // 2. 尝试获取任务锁
            if (tryLockTask(task)) {
                return Optional.of(task);
            }
            // 3. 获取锁失败, 重新获取任务
            processingTask = getFirstQueuedTask();
        }
        return Optional.empty();
    }

    private boolean tryLockTask(BotAnswerRewriteTaskPO task) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(task.getId()))
                .addCriteria(Criteria.where("executeHost").is(null));
        Update update = new Update();
        update.set("executeHost", ServerInfoConstants.SERVER_HOSTNAME);
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, BotAnswerRewriteTaskPO.COLLECTION_NAME);
        boolean success = updateResult.getModifiedCount() > 0;
        if (success) {
            task.setExecuteHost(ServerInfoConstants.SERVER_HOSTNAME);
        }
        return success;
    }

    private Optional<BotAnswerRewriteTaskPO> getOwnProcessingTask() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").in(RewriteStatusEnum.QUEUING, RewriteStatusEnum.PROCESSING))
                .addCriteria(Criteria.where("executeHost").is(ServerInfoConstants.SERVER_HOSTNAME))
                .with(Sort.by(Sort.Order.asc("_id")));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME));
    }

    private Optional<BotAnswerRewriteTaskPO> getFirstQueuedTask() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(RewriteStatusEnum.QUEUING))
                .addCriteria(Criteria.where("env").is(ApplicationConstant.CURR_ENV.name()))
                .with(Sort.by(Sort.Order.asc("_id")));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME));
    }

    @Override
    public BotAnswerRewriteTaskPO getById(String taskId) {
        return mongoTemplate.findById(taskId, BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME);
    }

    /**
     * 更新任务的进度, 异步改写时调用
     */
    private void updateTaskStatus(BotAnswerRewriteTaskPO task) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(task.getId()));
        Update update = new Update();
        update.set("status", task.getStatus());
        update.set("progress", task.getProgress());
        if (StringUtils.isNotBlank(task.getFailReason())) {
            update.set("failReason", task.getFailReason());
        }
        mongoTemplate.updateFirst(query, update, BotAnswerRewriteTaskPO.COLLECTION_NAME);

        RewriteStatusEnum status = task.getStatus();
        Long botId = task.getBotId();
        // 判断任务状态状态是否完成
        if (RewriteStatusEnum.FAIL.equals(status)
                || RewriteStatusEnum.SUCCESS.equals(status)
                || RewriteStatusEnum.CANCEL.equals(status)) {
            if (RewriteStatusEnum.FAIL.equals(status)) {
                if (RewriteTaskSourceEnum.AI_REWRITE.equals(task.getSource())) {
                    // bot改写取消生成的状态
                    botService.updateGenerateStatus(botId, BotGenerateStatusEnum.NONE);
                } else {
                    botService.updateGenerateStatus(botId, BotGenerateStatusEnum.FAIL);
                }
            } else if (RewriteStatusEnum.SUCCESS.equals(status)) {
                botService.updateGenerateStatus(botId, BotGenerateStatusEnum.SUCCESS);
            } else {
                // cancel
                botService.updateGenerateStatus(botId, BotGenerateStatusEnum.FAIL);
            }
        }
        sendProgressMsg(task);
    }

    /**
     * 更新任务的进度, 异步改写时调用
     */
    private BotAnswerRewriteTaskPO updateTask(BotAnswerRewriteTaskPO task) {
        mongoTemplate.save(task);
        return mongoTemplate.findById(task.getId(), BotAnswerRewriteTaskPO.class, BotAnswerRewriteTaskPO.COLLECTION_NAME);
    }

    private void calculateSuccessCount(BotAnswerRewriteTaskPO task, BotRewriteProgressVO progress) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("botId").is(task.getBotId()).and("_id").is(task.getId())),
                Aggregation.project("_id").and("allAnswerContentList").size().as("totalCount")
                                .and("successAnswerContentList").size().as("successCount")
                                .and("failAnswerContentList").size().as("failCount"),
                Aggregation.sort(Sort.Direction.ASC, "_id")
        );

        AggregationResults<BotRewriteProgressVO> result = mongoTemplate.aggregate(aggregation, BotAnswerRewriteTaskPO.COLLECTION_NAME, BotRewriteProgressVO.class);
        BotRewriteProgressVO uniqueMappedResult = result.getUniqueMappedResult();
        if (Objects.nonNull(uniqueMappedResult)) {
            progress.setSuccessCount(uniqueMappedResult.getSuccessCount());
            progress.setFailCount(uniqueMappedResult.getFailCount());
            progress.setTotalCount(uniqueMappedResult.getTotalCount());
        }
    }

    private void sendProgressMsg(BotAnswerRewriteTaskPO task) {
        BotRewriteProgressVO progressMsg = BotRewriteProgressVO.builder()
                .botId(task.getBotId())
                .taskId(task.getId())
                .failReason(task.getFailReason())
                .taskStatus(task.getStatus())
                .taskProgress(task.getProgress())
                .build();
        calculateSuccessCount(task, progressMsg);
        BasicMsg<BotRewriteProgressVO> msg = new BasicMsg<>();
        msg.setInfo(progressMsg);
        msg.setMsg("bot改写进度");
        log.info("推送bot改写进度, msg={}", JsonUtils.object2String(progressMsg));
        AliMessageQueueHelper.sendWebSocketMessage(ApplicationConstant.WEBSOCKET_BOT_REWRITE_SUBSCRIPT_URL, msg, "botRewrite");
    }

    @Override
    public void beforeShutdown() {
        shutdown.set(true);
    }

    @Data
    public static class ContextAnalyzeResult {
        private Map<String, Object> context_analysis;
        private Long tokens_count;
    }

    @Data
    public static class PromptAnalyzeResult {
        private Map<String, List<String>> context;
        private Long tokens_count;
    }

    @Data
    public static class RewriteResult {
        private List<RewriteItem> rewrite_list;
        private Long tokens_count;
    }

    @Data
    public static class RewriteItem {
        String extra_info;
        String method;
        String text;
    }

    public static class ShutdownException extends RuntimeException {
        public ShutdownException() {
            super("shutdown");
        }
    }

    /**
     * 任务取消异常
     */
    public static class TaskCancelException extends RuntimeException {
        public TaskCancelException() {
            super("shutdown");
        }
    }

    /**
     * 任务执行中异常
     */
    public static class TaskExecuteException extends RuntimeException {
        public TaskExecuteException(Throwable throwable) {
            super(throwable);
        }
    }

}