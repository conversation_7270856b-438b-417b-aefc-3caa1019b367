package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.service.llm.LlmTagService;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LlmTagServiceImpl implements LlmTagService {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public List<String> list() {
        String url = ApplicationConstant.ALGORITHM_BUILTIN_LLM_TAG_REQUEST_URL;
        log.info("调用大模型内置标签列表接口, url={}", url);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        log.info("调用大模型内置标签列表接口, res={}", response);
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            Res res = JsonUtils.string2Object(response.getBody(), Res.class);
            if (res.isSuccess()) {
                return res.getTags().stream().map(Tag::getName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class Res implements Serializable {

        String status;

        List<Tag> tags;

        public boolean isSuccess() {
            return "success".equals(status) && CollectionUtils.isNotEmpty(tags);
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class Tag implements Serializable {

        String name;
    }
}