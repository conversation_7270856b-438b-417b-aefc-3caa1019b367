package com.yiwise.dialogflow.service.impl.intent;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.intent.IntentCorpusExportService;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;

@Slf4j
@Service
public class IntentCorpusExportServiceImpl implements IntentCorpusExportService {

    @Resource
    private BotService botService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private IntentService intentService;

    @Override
    public String exportAllAsCsv() {
        List<BotVO> botList = botService.queryListWithoutPage(new BotQuery());
        List<String> result = new ArrayList<>();
        result.add("领域, botId, 意图, 是否自定义, 意图属性, 语料");

        for (int i = 0; i < botList.size(); i++) {
            BotVO botVO = botList.get(i);
            String domainName = botVO.getDomainName();
            if (StringUtils.isBlank(domainName)) {
                domainName = "无";
            }
            IntentCorpusQuery intentCorpusQuery = IntentCorpusQuery.builder()
                    .botId(botVO.getBotId())
                    .build();
            List<IntentPO> allIntentList = intentService.getAllByBotId(botVO.getBotId());
            Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(allIntentList, IntentPO::getId);
            List<IntentCorpusPO> corpusList = intentCorpusService.scroll(intentCorpusQuery);
            if (CollectionUtils.isNotEmpty(corpusList)) {
                for (IntentCorpusPO corpus : corpusList) {
                    IntentPO intent = intentMap.get(corpus.getIntentId());
                    if (Objects.isNull(intent)) {
                        continue;
                    }
                    Set<String> buildInCorpusSet = new HashSet<>();
                    if (CollectionUtils.isNotEmpty(corpus.getBuildInCorpusList())) {
                        buildInCorpusSet.addAll(corpus.getBuildInCorpusList());
                    }
                    if (CollectionUtils.isNotEmpty(corpus.getCorpusList()) && StringUtils.isNotBlank(corpus.getName())) {
                        for (String s : corpus.getCorpusList()) {
                            if (buildInCorpusSet.contains(s)) {
                                continue;
                            }
                            result.add(String.format("%s, %s, %s, %s, %s, %s",
                                    domainName,
                                    botVO.getBotId(),
                                    corpus.getName(),
                                    CorpusTypeEnum.CUSTOMIZED.equals(intent.getCorpusType()),
                                    intent.getIntentProperties(),
                                    s.replace(",", "，")));
                        }
                    }
                }
            }
            log.info("exportAllAsCsv: {} / {}", i + 1, botList.size());
        }

        String key = OssKeyCenter.getAllCorpusExportKey();
        return objectStorageHelper.upload(key, String.join("\n", result).getBytes());
    }
}
