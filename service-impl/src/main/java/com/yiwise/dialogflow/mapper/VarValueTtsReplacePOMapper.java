package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.VarValueTtsReplacePO;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceQueryVO;
import com.yiwise.dialogflow.entity.vo.VarValueTtsReplaceVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface VarValueTtsReplacePOMapper extends Mapper<VarValueTtsReplacePO> {

    List<VarValueTtsReplaceVO> queryByCondition(@Param("query") VarValueTtsReplaceQueryVO query);

    void deleteByIdList(@Param("tenantId") Long tenantId,
                        @Param("idList") List<Long> idList,
                        @Param("updateUserId") Long updateUserId);

    void deleteByTenantId(@Param("tenantId") Long tenantId,
                          @Param("updateUserId") Long updateUserId);

    void batchUpsert(@Param("list") List<VarValueTtsReplacePO> importList);

    VarValueTtsReplacePO queryByVariableNameAndValue(@Param("tenantId") Long tenantId,
                                                     @Param("providerCode") Integer providerCode,
                                                     @Param("varName") String varName,
                                                     @Param("varValue") String varValue,
                                                     @Param("excludeId") Long excludeId);

    List<VarValueTtsReplacePO> queryByTenantId(@Param("tenantId") Long tenantId);

    List<VarValueTtsReplacePO> queryByTenantIdAndIdList(@Param("tenantId") Long tenantId,
                                                         @Param("idList") List<Long> idList);
}
