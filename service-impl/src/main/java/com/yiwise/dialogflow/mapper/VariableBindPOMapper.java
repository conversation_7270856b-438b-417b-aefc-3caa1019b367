package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.VariableBindPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface VariableBindPOMapper extends Mapper<VariableBindPO> {

    List<VariableBindPO> queryByBotId(@Param("tenantId") Long tenantId, @Param("botId") Long botId);

    void batchCreate(@Param("list") List<VariableBindPO> list);

    void deleteByBotIdAndVariableId(@Param("botId") Long botId,
                                    @Param("tenantId") Long tenantId,
                                    @Param("variableId") String variableId);

}
