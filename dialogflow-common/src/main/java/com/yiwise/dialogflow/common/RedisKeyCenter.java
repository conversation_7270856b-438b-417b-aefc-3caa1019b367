package com.yiwise.dialogflow.common;

import com.yiwise.base.model.enums.SystemEnum;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
public class RedisKeyCenter {

    /**
     * 阿里热词组调用次数
     *
     * @return
     */
    public static String getAliVocabCountKey() {
        return "AliVocab:";
    }

    /**
     * 阿里自学习模型调用次数
     *
     * @return
     */
    public static String getAliSelfCountKey() {
        return "AliSelf:";
    }

    /**
     * 腾讯热词组调用次数
     *
     * @return
     */
    public static String getTencentVocabCountKey() {
        return "TencentVocab:";
    }

    public static String getAsrSourceScheduleLockKey() {
        return "AsrSourceScheduleLockKey:";
    }

    public static String getBotGenerateTimeoutLockKey() {
        return "BotGenerateTimeoutLock";
    }

    public static String getAnalyzeTaskLockKey() {
        return "AnalyzeTaskLock";
    }

    public static String getAnalyzeTaskLlmCountKey(Integer month) {
        return "AnalyzeTaskLlmCount:" + month;
    }

    public static String getLlmContextCacheKey(String hash) {
        return "LLM_CONTEXT_CACHE:" + hash;
    }

    /**
     * 腾讯自学习模型调用次数
     *
     * @return
     */
    public static String getTencentSelfCountKey() {
        return "TencentSelf:";
    }

    /**
     * 语料训练信号
     */
    public static String getTrainDataSignalRedisKey() {
        return String.format("TrainDataSignal:%s", ApplicationConstant.ALGORITHM_REGISTER_ENV);
    }

    public static String getTrainDataSignalLock(Long tenantId, String robotId, Object snapshotType, Object trainType) {
        return String.format("TID_%s:RID_%s:%s:%s", tenantId, robotId, snapshotType, trainType);
    }

    @Deprecated
    public static String getTrainButtonEnabledRedisKey(Long tenantId, String botId, String trainType) {
        return String.format("TrainButtonEnabled:TID_%s:BID_%s:%s", tenantId, botId, trainType);
    }

    public static String getTrainingStatusRedisKey(Long tenantId, String botId, String trainType) {
        return String.format("TrainingStatus:TID_%s:BID_%s:%s", tenantId, botId, trainType);
    }

    /**
     * token保存用户信息
     *
     * @return
     */
    public static String getUserLoginInfoRedisKey(SystemEnum system, String token) {
        return String.format("user_token:user_login_info:%s_%s", system, token);
    }

    /**
     * token的ZSet
     *
     * @return
     */
    public static String getTokenZsetRedisKey(SystemEnum system) {
        return String.format("user_token:user_login_info:%s", system);
    }

    /**
     * 单点时候用户保存的token
     *
     * @return
     */
    public static String getUserLoginTokenRedisKey(SystemEnum system, Long userId) {
        return String.format("user_token:user_login_token:%s_%s", system, userId);
    }

    public static String getDoorOpeUserIdRedisKey(String opeToken) {
        return String.format("Door:opeUserId:%s", opeToken);
    }

    /**
     * 用户表主键redisKey
     */
    public static String getUserPrimaryRedisKey(Number userId) {
        return String.format("User:userId:%s", userId);
    }

    /**
     * 客户表主键redisKey
     */
    public static String getTenantPrimaryRedisKey(Number tenantId) {
        return String.format("Tenant:tenantId:%s", tenantId);
    }

    /**
     * 用户赛道
     */
    public static String getCustomerTrackTypeRedisKey(Integer customerTrackTypeId) {
        return String.format("CustomerTrackType:CustomerTrackTypeId:%s", customerTrackTypeId);
    }

    /**
     * 话术场景
     */
    public static String getCustomerSceneNameRedisKey(Integer customerSceneId) {
        return String.format("CustomerScene:CustomerSceneId:%s", customerSceneId);
    }

    /**
     * 意向标签
     */
    public static String getIntentLevelTagRedisKey(Long intentLevelTagId) {
        return String.format("IntentLevelTag:IntentLevelTagId:%s", intentLevelTagId);
    }

    public static String getIpWhiteListRedisKey() {
        return "WhiteList:IPWhiteList:Mask";
    }

    public static String getOpeUserLoginAiccRedisKey(Long opeUserId) {
        return String.format("OpeUserLoginAicc:%s", opeUserId);
    }

    public static String getIntentConfigRedisKey(Long botId) {
        return String.format("IntentConfig:botId:%s", botId);
    }

    public static String getSessionContextRedisKey(Long botId, String sessionId) {
        return String.format("SessionId:%s:%s", botId, sessionId);
    }

    public static String getTtsJobUniqueKey(Long botId) {
        return String.format("BotTtsJob:%s", botId);
    }

    public static Object getBotAudioCompleteProgressKey(Long botId) {
        return String.format("BotAudioCompleteProgress:%s", botId);
    }

    /**
     * 技术预警是否沉默
     */
    public static String getTechAlertSilence(String prefix) {
        return "TechAlertSilence:" +  prefix;
    }

    /**
     * 飞书access_token
     */
    public static String getFeishuAccessToken() {
        return "FeishuAccessToken";
    }

    /**
     * 飞书open_id
     */
    public static String getFeishuOpenId() {
        return "FeishuOpenId";
    }

    public static String getBotGenerateCancelSignalKey(Long botId) {
        return String.format("BotGenerateCancelSignal:%s", botId);
    }

    public static String getRewriteTokenKey(long currentEpochMinute) {
        return String.format("RewriteToken:%s", currentEpochMinute);
    }

    public static String getBotResourceLockKey(Long botId) {
        return String.format("BotResourceLock:%s", botId);
    }

    public static String getBotRecommendLockKey() {
        return "BotRecommendLock";
    }

    public static String getCustomWechatCPAccessToken(String corpid, String secret) {
        return String.format("Bot:CustomWechatCPAccessToken:%s_%s", corpid, secret);
    }

    public static String getQixinbaoAccessToken(String appId, String appKey, String secret) {
        return String.format("Bot:CustomQixinbaoccessToken:%s_%s_%s", appId, appKey, secret);
    }
}
