package com.yiwise.dialogflow.aop;


import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import reactor.core.publisher.Mono;

@Slf4j
public class CommonAsyncWebLogAspect {

    protected Object doRestControllerAroundMethod(ProceedingJoinPoint pig) throws Throwable {
        MethodSignature methodSign = (MethodSignature) pig.getSignature();

        Class<?> targetClass = pig.getTarget().getClass();
        String methodSignName = methodSign.getMethod().getName();
        String targetClassName = targetClass.getSimpleName();

        long start = System.nanoTime();
        log.info("【1】====start==== {}.{} ====start====", targetClassName, methodSignName);
        Object result = pig.proceed();
        if (result instanceof Mono) {
            Mono<?> monoResult = (Mono<?>) result;
            return monoResult.doOnNext(o -> {
                        long end = System.nanoTime();
                        log.info("【2】{}执行{}结束, 总耗时{}ns", targetClassName, methodSignName, end - start);
                    });
        } else {
            long end = System.nanoTime();
            log.info("【2】{}执行{}结束, 总耗时{}ns", targetClassName, methodSignName, end - start);
        }
        return result;
    }
}
