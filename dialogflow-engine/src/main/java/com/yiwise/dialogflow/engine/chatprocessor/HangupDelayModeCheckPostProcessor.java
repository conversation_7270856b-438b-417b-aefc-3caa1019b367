package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;

/**
 * 检查响应动作是否有延迟挂机, 如果有的话, 则切换状态进入延迟挂机状态
 * <AUTHOR>
 */
@Slf4j
public class HangupDelayModeCheckPostProcessor extends AbstractPostProcessor {

    private final boolean enable;

    public HangupDelayModeCheckPostProcessor(RobotRuntimeResource resource) {
        this.enable = BooleanUtils.isTrue(resource.getHangupDelay()) && Objects.nonNull(resource.getHangupDelayMs());
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "延迟挂机状态检测";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        if (!enable) {
            return Optional.of(chatResponse);
        }
        log.info("开启延迟挂机, 检测是否有挂机指令");

        // 如果是已经在延迟挂机模式下了, 且命中了新的流程, 这个时候最好是退出延迟挂机状态
        if (SessionContextHelper.isHangupDelayMode(sessionContext) && Objects.nonNull(sessionContext.getActiveManagerInfo())) {
            if (ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent())
                    && ActiveTypeEnum.STEP.equals(sessionContext.getActiveManagerInfo().getActiveType())) {
                sessionContext.getSpecialChatModes().remove(SpecialChatModeEnum.DELAY_HANGUP);
                sessionContext.getHangupDelayContext().setDelayCount(0);
                log.info("命中新的流程, 退出延迟挂机状态");
                return Optional.of(chatResponse);
            }
        }

        if (ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent()) && SessionContextHelper.isHangupDelayMode(sessionContext)) {
            sessionContext.getHangupDelayContext().setDelayCount(sessionContext.getHangupDelayContext().getDelayCount() + 1);
        }

        if (CollectionUtils.isNotEmpty(chatResponse.getActionList())) {
            boolean delayHangup = chatResponse.getActionList().stream()
                    .anyMatch(action -> {
                        if (action instanceof HangupAction) {
                            HangupAction hangupAction = (HangupAction) action;
                            return BooleanUtils.isTrue(hangupAction.getHangupDelay())
                                    && Objects.nonNull(hangupAction.getHangupDelayMs());
                        }
                        return false;
                    });
            if (delayHangup) {
                log.info("检测到延迟挂机指令, 进入到延迟挂机模式, 指令来源={}", chatResponse.getAnswerLocate());
                sessionContext.getSpecialChatModes().add(SpecialChatModeEnum.DELAY_HANGUP);
            }
        }
        return Optional.of(chatResponse);
    }

}
