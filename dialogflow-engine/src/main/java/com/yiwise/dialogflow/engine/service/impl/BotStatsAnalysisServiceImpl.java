package com.yiwise.dialogflow.engine.service.impl;

import com.yiwise.dialogflow.engine.analysis.EventLog;
import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.domain.NodeJumpInfo;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.service.BotStatsAnalysisService;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.IntentLevelRuleMatchInfo;
import com.yiwise.dialogflow.engine.share.IntentRuleActionResult;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.entity.bo.stats.*;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.NodeJumpTargetTypeEnum;
import com.yiwise.dialogflow.entity.enums.RuleActionSourceEnum;
import com.yiwise.dialogflow.entity.enums.RuleActionTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class BotStatsAnalysisServiceImpl implements BotStatsAnalysisService {

    @Override
    public BotStatsAnalysisResult analysis(RobotRuntimeResource resource,
                                           SessionInfo sessionInfo,
                                           CallDataInfo callDataInfo,
                                           OriginChatData originChatData,
                                           IntentLevelAnalysisResult intentLevelAnalysisResult) {
        BotStatsAnalysisResult result = new BotStatsAnalysisResult();

        result.setCallJobId(callDataInfo.getCallJobId() == null ? 0: callDataInfo.getCallJobId());
        result.setTenantId(callDataInfo.getTenantId() == null ? 0: callDataInfo.getTenantId());
        result.setMaFlowId(callDataInfo.getMaFlowId() == null ? 0 : callDataInfo.getMaFlowId());
        result.setNewCallJobId(callDataInfo.getNewCallJobId() == null ? 0: callDataInfo.getNewCallJobId());
        result.setCallInJobId(callDataInfo.getCallInJobId() == null ? 0 : callDataInfo.getCallInJobId());
        result.setMaFlowName(callDataInfo.getMaFlowName());
        result.setBotId(sessionInfo.getBotId());
        result.setCallRecordId(callDataInfo.getCallRecordId());
        result.setCallEndTime(LocalDateTime.now());
        result.setVersion(sessionInfo.getVersion() == null ? 0: sessionInfo.getVersion());


        result.setLlmTokenUsageInfo(intentLevelAnalysisResult.getLlmTokenUsageInfo());

        // 统计流程到达次数
        result.setStepCountMap(calculateStepReachCount(originChatData.getLogList()));

        // 统计节点分支命中
        Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeBranchReachCountMap = calculateNodeBranchReachCount(originChatData.getLogList());
        // 节点跳转问答知识
        Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToKnowledgeCountMap = calculateNodeJumpToKnowledgeCount(resource, originChatData.getLogList());
        // 节点跳转特殊语境
        Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToSpecialAnswerCountMap = calculateNodeJumpToSpecialAnswerCount(resource, originChatData.getLogList());
        // 节点跳转流程
        Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToStepCountMap = calculateNodeJumpToStepCount(resource, originChatData.getLogList());


        // 意图触发次数

        // 统计问答知识到达次数
        Map<IntentTriggerUniqueKey, AtomicInteger> knowledgeReachCountMap = calculateKnowledgeReachCount(originChatData.getLogList());
        // 统计特殊语境到达次数
        Map<IntentTriggerUniqueKey, AtomicInteger> specialAnswerReachCountMap = calculateSpecialAnswerReachCount(originChatData.getLogList());
        Map<IntentTriggerUniqueKey, AtomicInteger> intentTriggerStepCountMap = calculateIntentTriggerStepCount(nodeJumpToStepCountMap);

        result.getIntentTriggerCountMap().putAll(knowledgeReachCountMap);
        result.getIntentTriggerCountMap().putAll(specialAnswerReachCountMap);
        result.getIntentTriggerCountMap().putAll(intentTriggerStepCountMap);

        // 统计答案拒绝
        result.setAnswerDeclineCountMap(calculateAnswerDeclineCount(resource, originChatData.getLogList()));

        // 统计规则到达率
        Map<IntentRuleUniqueKey, AtomicInteger> intentLevelRuleCountMap = calculateIntentLevelRuleReachCount(intentLevelAnalysisResult);
        Map<IntentRuleUniqueKey, AtomicInteger> intentActionRuleCountMap = calculateIntentActionRuleReachCount(intentLevelAnalysisResult);
        result.getRuleCountMap().putAll(intentLevelRuleCountMap);
        result.getRuleCountMap().putAll(intentActionRuleCountMap);

        result.setAnswerHangupCountMap(calculateAnswerHangupCount(resource, callDataInfo));

        // 对数据合并
        // 合并节点跳转流程, 跳转问答知识, 特殊语境, 意图
        result.setNodeJumpCountMap(merge(nodeBranchReachCountMap,
                nodeJumpToKnowledgeCountMap,
                nodeJumpToSpecialAnswerCountMap,
                nodeJumpToStepCountMap));

        return result;
    }

    private Map<IntentTriggerUniqueKey, AtomicInteger> calculateIntentTriggerStepCount(Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToStepCountMap) {
        if (MapUtils.isEmpty(nodeJumpToStepCountMap)) {
            return Collections.emptyMap();
        }

        Map<IntentTriggerUniqueKey, AtomicInteger> result = new HashMap<>();
        nodeJumpToStepCountMap.forEach((key, value) -> {
            if (NodeJumpTargetTypeEnum.STEP.equals(key.getTargetType())) {
                result.computeIfAbsent(IntentTriggerUniqueKey.of(AnswerSourceEnum.STEP, key.getTargetId(), key.getTargetLabel()), k -> new AtomicInteger()).addAndGet(value.get());
            }
        });

        return result;
    }

    private Map<AnswerProgressUniqueKey, AtomicInteger> calculateAnswerHangupCount(RobotRuntimeResource resource, CallDataInfo callDataInfo) {
        if (StringUtils.isBlank(callDataInfo.getLastAnswerId()) || BooleanUtils.isNotTrue(callDataInfo.getUserHangup())) {
            return Collections.emptyMap();
        }

        String lastAnswerId = callDataInfo.getLastAnswerId();
        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(lastAnswerId);
        if (Objects.isNull(locate)) {
            return Collections.emptyMap();
        }
        Optional<String> answerTemplateOpt = getAnswerTemplate(locate, resource);
        return answerTemplateOpt
                .map(s -> Collections.singletonMap(AnswerProgressUniqueKey.of(locate, s, callDataInfo.getLastAnswerPlayProgress()), new AtomicInteger(1)))
                .orElse(Collections.emptyMap());
    }

    private Optional<String> getAnswerTemplate(AnswerLocateBO answerLocate, RobotRuntimeResource resource) {
        List<? extends BaseAnswerContent> answerList = Collections.emptyList();
        switch (answerLocate.getAnswerSource()) {
            case KNOWLEDGE:
                KnowledgePO knowledge = resource.getKnowledgeIdMap().get(answerLocate.getKnowledgeId());
                if (Objects.nonNull(knowledge)) {
                    answerList = knowledge.getAnswerList();
                }
                break;
            case STEP:
                DialogBaseNodePO node = resource.getNodeIdMap().get(answerLocate.getNodeId()).origin;
                if (Objects.nonNull(node)) {
                    answerList = node.getAnswerList();
                }
                break;
            case SPECIAL_ANSWER:
                SpecialAnswerConfigPO specialAnswer = resource.getSpecialAnswerIdMap().get(answerLocate.getSpecialAnswerConfigId());
                if (Objects.nonNull(specialAnswer)) {
                    answerList = specialAnswer.getAnswerList();
                }
                break;
            default:
                return Optional.empty();
        }

        if (CollectionUtils.isEmpty(answerList)) {
            return Optional.empty();
        }
        return answerList.stream()
                .filter(answer -> answer.getUniqueId().equals(answerLocate.getAnswerId()))
                .map(BaseAnswerContent::getText).findFirst();
    }

    private Map<NodeJumpTargetUniqueKey, AtomicInteger> merge(Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeBranchReachCountMap,
                                                              Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToKnowledgeCountMap,
                                                              Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToSpecialAnswerCountMap,
                                                              Map<NodeJumpTargetUniqueKey, AtomicInteger> nodeJumpToStepCountMap) {
        Map<NodeJumpTargetUniqueKey, AtomicInteger> result = new HashMap<>();

        result.putAll(nodeBranchReachCountMap);
        result.putAll(nodeJumpToKnowledgeCountMap);
        result.putAll(nodeJumpToSpecialAnswerCountMap);
        result.putAll(nodeJumpToStepCountMap);
        return result;
    }

    private Map<IntentRuleUniqueKey, AtomicInteger> calculateIntentLevelRuleReachCount(IntentLevelAnalysisResult intentLevelAnalysisResult) {
        if (Objects.isNull(intentLevelAnalysisResult)
                || (Objects.isNull(intentLevelAnalysisResult.getIntentLevelRuleMatchInfo()))) {
            return Collections.emptyMap();
        }
        IntentLevelRuleMatchInfo matchInfo = intentLevelAnalysisResult.getIntentLevelRuleMatchInfo();
        IntentRuleUniqueKey key = null;
        if (StringUtils.isNotBlank(matchInfo.getRuleId())) {
            key = IntentRuleUniqueKey.builder()
                    .ruleId(matchInfo.getRuleId())
                    .source(RuleActionSourceEnum.RULE)
                    .ruleType(RuleActionTypeEnum.INTENT_LEVEL)
                    .build();
        } else if (StringUtils.isNotBlank(matchInfo.getKnowledgeId())) {
            key = IntentRuleUniqueKey.builder()
                    .knowledgeId(matchInfo.getKnowledgeId())
                    .source(RuleActionSourceEnum.KNOWLEDGE)
                    .ruleType(RuleActionTypeEnum.INTENT_LEVEL)
                    .build();
        } else if (StringUtils.isNotBlank(matchInfo.getSpecialAnswerConfigId())) {
            key = IntentRuleUniqueKey.builder()
                    .specialAnswerConfigId(matchInfo.getSpecialAnswerConfigId())
                    .source(RuleActionSourceEnum.SPECIAL_ANSWER)
                    .ruleType(RuleActionTypeEnum.INTENT_LEVEL)
                    .build();
        } else if (StringUtils.isNotBlank(matchInfo.getStepId()) && StringUtils.isNotBlank(matchInfo.getNodeId())) {
            key = IntentRuleUniqueKey.builder()
                    .stepId(matchInfo.getStepId())
                    .nodeId(matchInfo.getNodeId())
                    .source(RuleActionSourceEnum.STEP)
                    .ruleType(RuleActionTypeEnum.INTENT_LEVEL)
                    .build();
        }
        Map<IntentRuleUniqueKey, AtomicInteger> result = new HashMap<>();
        if (Objects.nonNull(key)) {
            result.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
        }
        return result;
    }

    private Map<IntentRuleUniqueKey, AtomicInteger> calculateIntentActionRuleReachCount(IntentLevelAnalysisResult intentLevelAnalysisResult) {
        if (Objects.isNull(intentLevelAnalysisResult) || CollectionUtils.isEmpty(intentLevelAnalysisResult.getIntentRuleActionResultList())) {
            return Collections.emptyMap();
        }
        Map<IntentRuleUniqueKey, AtomicInteger> result = new HashMap<>();
        for (IntentRuleActionResult item : intentLevelAnalysisResult.getIntentRuleActionResultList()) {
            IntentRuleUniqueKey key = null;
            if (StringUtils.isNotBlank(item.getRuleId())) {
                key = IntentRuleUniqueKey.builder()
                        .ruleId(item.getRuleId())
                        .source(RuleActionSourceEnum.RULE)
                        .ruleType(RuleActionTypeEnum.INTENT_ACTION)
                        .build();
            } else if (StringUtils.isNotBlank(item.getKnowledgeId())) {
                key = IntentRuleUniqueKey.builder()
                        .knowledgeId(item.getKnowledgeId())
                        .source(RuleActionSourceEnum.KNOWLEDGE)
                        .ruleType(RuleActionTypeEnum.INTENT_ACTION)
                        .build();
            } else if (StringUtils.isNotBlank(item.getSpecialAnswerConfigId())) {
                key = IntentRuleUniqueKey.builder()
                        .specialAnswerConfigId(item.getSpecialAnswerConfigId())
                        .source(RuleActionSourceEnum.SPECIAL_ANSWER)
                        .ruleType(RuleActionTypeEnum.INTENT_ACTION)
                        .build();
            } else if (StringUtils.isNotBlank(item.getNodeId()) && StringUtils.isNotBlank(item.getStepId())) {
                key = IntentRuleUniqueKey.builder()
                        .stepId(item.getStepId())
                        .nodeId(item.getNodeId())
                        .source(RuleActionSourceEnum.STEP)
                        .ruleType(RuleActionTypeEnum.INTENT_ACTION)
                        .build();
            }
            if (Objects.nonNull(key)) {
                result.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
            }
        }
        return result;
    }

    /**
     * 节点跳转问答知识, 在播放节点答案后, 后续用户输入命中了问答知识, 则认为是节点跳转到了问答知识
     * @param logList
     * @return
     */
    private Map<NodeJumpTargetUniqueKey, AtomicInteger> calculateNodeJumpToKnowledgeCount(RobotRuntimeResource resource, List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<NodeJumpTargetUniqueKey, AtomicInteger> result = new HashMap<>();
        AnswerLocateBO preNodeAnswerLocate = null;
        for (EventLog eventLog : logList) {
            if (checkAnswerSourceMatch(AnswerSourceEnum.STEP, eventLog)) {
                preNodeAnswerLocate = eventLog.getAnswerLocate();
                continue;
            }
            Optional<IntentPO> matchIntent = getMatchIntent(resource, eventLog);
            if (checkAnswerSourceMatch(AnswerSourceEnum.KNOWLEDGE, eventLog)
                    && matchIntent.isPresent()) {
                AnswerLocateBO knowledgeAnswerLocate = eventLog.getAnswerLocate();

                if (Objects.nonNull(preNodeAnswerLocate) && Objects.nonNull(knowledgeAnswerLocate)) {
                    NodeJumpTargetUniqueKey uniqueKey = NodeJumpTargetUniqueKey
                            .builder()
                            .stepId(preNodeAnswerLocate.getStepId())
                            .nodeId(preNodeAnswerLocate.getNodeId())
                            .nodeLabel(preNodeAnswerLocate.getNodeLabel())
                            .intentId(matchIntent.get().getId())
                            .targetType(NodeJumpTargetTypeEnum.KNOWLEDGE)
                            .targetId(knowledgeAnswerLocate.getKnowledgeId())
                            .targetLabel(knowledgeAnswerLocate.getKnowledgeLabel())
                            .build();
                    result.computeIfAbsent(uniqueKey, k -> new AtomicInteger(0)).incrementAndGet();
                }
            }
        }
        return result;
    }

    private Optional<IntentPO> getMatchIntent(RobotRuntimeResource resource, EventLog eventLog) {
        if (Objects.isNull(eventLog)
                || Objects.isNull(eventLog.getPredictResult())) {
            return Optional.empty();
        }
        return Optional.ofNullable(resource.getIntentMap().get(eventLog.getPredictResult().getIntentId()));
    }

    /**
     * 节点跳转特殊语境
     * @param logList
     * @return
     */
    private Map<NodeJumpTargetUniqueKey, AtomicInteger> calculateNodeJumpToSpecialAnswerCount(RobotRuntimeResource resource, List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<NodeJumpTargetUniqueKey, AtomicInteger> result = new HashMap<>();
        AnswerLocateBO preNodeAnswerLocate = null;
        for (EventLog eventLog : logList) {
            if (checkAnswerSourceMatch(AnswerSourceEnum.STEP, eventLog) && !checkReceptionAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, eventLog)) {
                preNodeAnswerLocate = eventLog.getAnswerLocate();
                continue;
            }
            // ai重复上一句等答案来源是流程等, 但是需要判定为跳转了
            if (checkAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, eventLog)) {
                AnswerLocateBO knowledgeAnswerLocate = eventLog.getAnswerLocate();
                if (Objects.nonNull(preNodeAnswerLocate)) {
                    String intentId = null;

                    // 有些特殊语境不是通过意图触发的, 所以可能没有意图
                    if (getMatchIntent(resource, eventLog).isPresent()) {
                        intentId = getMatchIntent(resource, eventLog).get().getId();
                    }

                    NodeJumpTargetUniqueKey uniqueKey = NodeJumpTargetUniqueKey
                            .builder()
                            .stepId(preNodeAnswerLocate.getStepId())
                            .nodeId(preNodeAnswerLocate.getNodeId())
                            .nodeLabel(preNodeAnswerLocate.getNodeLabel())
                            .targetType(NodeJumpTargetTypeEnum.SPECIAL_ANSWER)
                            .targetId(knowledgeAnswerLocate.getSpecialAnswerConfigId())
                            .targetLabel(knowledgeAnswerLocate.getSpecialAnswerConfigLabel())
                            .intentId(intentId)
                            .build();
                    result.computeIfAbsent(uniqueKey, k -> new AtomicInteger(0)).incrementAndGet();
                }
            } else if (checkReceptionAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, eventLog)
                    && StringUtils.isNotBlank(eventLog.getReceptionInfo().getTargetId())
                    && Objects.nonNull(preNodeAnswerLocate)) {
                String intentId = null;

                // 有些特殊语境不是通过意图触发的, 所以可能没有意图
                if (getMatchIntent(resource, eventLog).isPresent()) {
                    intentId = getMatchIntent(resource, eventLog).get().getId();
                }
                NodeJumpTargetUniqueKey uniqueKey = NodeJumpTargetUniqueKey
                        .builder()
                        .stepId(preNodeAnswerLocate.getStepId())
                        .nodeId(preNodeAnswerLocate.getNodeId())
                        .nodeLabel(preNodeAnswerLocate.getNodeLabel())
                        .targetType(NodeJumpTargetTypeEnum.SPECIAL_ANSWER)
                        .targetId(eventLog.getReceptionInfo().getTargetId())
                        .targetLabel(eventLog.getReceptionInfo().getTargetLabel())
                        .intentId(intentId)
                        .build();
                result.computeIfAbsent(uniqueKey, k -> new AtomicInteger(0)).incrementAndGet();

            }
        }
        return result;
    }

    /**
     * 节点跳转流程
     * 通过流程中的跳转到指定流程和在流程答案中命中新的流程的意图
     * @param logList
     * @return
     */
    private Map<NodeJumpTargetUniqueKey, AtomicInteger> calculateNodeJumpToStepCount(RobotRuntimeResource resource, List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Set<ChatManagerPriorityEnum> triggerStepByIntentSet = Collections.singleton(ChatManagerPriorityEnum.INTENT_TRIGGER_STEP);
        Map<NodeJumpTargetUniqueKey, AtomicInteger> result = new HashMap<>();
        AnswerLocateBO preNodeAnswerLocate = null;
        for (EventLog eventLog : logList) {
            if (checkAnswerSourceMatch(AnswerSourceEnum.STEP, eventLog)) {
                // 判断当前事件是否是用户输入, 如果不是的话, 说明是回答后的跳转, 不能算用户输入的
                if (checkEvent(ChatEventTypeEnum.USER_SAY_FINISH, eventLog) && Objects.nonNull(preNodeAnswerLocate)) {
                    AnswerLocateBO current = eventLog.getAnswerLocate();
                    // 不是同一个流程了, 说明通过用户输入进行了流程的切换
                    Optional<IntentPO> matchIntent = getMatchIntent(resource, eventLog);
                    if (!StringUtils.equals(current.getStepId(), preNodeAnswerLocate.getStepId())
                            && triggerStepByIntentSet.contains(eventLog.getSelectedConditionType())
                            && matchIntent.isPresent()) {
                        NodeJumpTargetUniqueKey key = NodeJumpTargetUniqueKey.builder()
                                .stepId(preNodeAnswerLocate.getStepId())
                                .nodeId(preNodeAnswerLocate.getNodeId())
                                .nodeLabel(preNodeAnswerLocate.getNodeLabel())
                                .intentId(matchIntent.get().getId())
                                .targetType(NodeJumpTargetTypeEnum.STEP)
                                .targetId(current.getStepId())
                                .targetLabel(current.getStepLabel())
                                .build();
                        result.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
                    }
                }
                preNodeAnswerLocate = eventLog.getAnswerLocate();
            }
        }
        return result;
    }


    private boolean checkEvent(ChatEventTypeEnum userSayFinish, EventLog eventLog) {
        if (Objects.isNull(userSayFinish) || Objects.isNull(eventLog) || Objects.isNull(eventLog.getEvent())) {
            return false;
        }
        return userSayFinish.equals(eventLog.getEvent());
    }

    /**
     * 节点分支命中率
     * @param logList 日志列表
     * @return key: Tuple2<nodeId, intentId>, value: count
     */
    private Map<NodeJumpTargetUniqueKey, AtomicInteger> calculateNodeBranchReachCount(List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<NodeJumpTargetUniqueKey, AtomicInteger> result = new HashMap<>();
        for (EventLog log : logList) {
            if (CollectionUtils.isNotEmpty(log.getPrevNodeInfoList())) {
                for (NodeJumpInfo prevNodeInfo : log.getPrevNodeInfoList()) {
                    if (StringUtils.isNotBlank(prevNodeInfo.getFromNodeId())
                            && !StringUtils.equals(prevNodeInfo.getFromNodeId(), prevNodeInfo.getToNodeId())) {
                        NodeJumpTargetUniqueKey key = NodeJumpTargetUniqueKey.builder()
                                .stepId(prevNodeInfo.getStepId())
                                .nodeId(prevNodeInfo.getFromNodeId())
                                .nodeLabel(prevNodeInfo.getFromNodeLabel())
                                // todo 判断节点是没有意图id的
                                .intentId(prevNodeInfo.getByIntentId())
                                .targetId(prevNodeInfo.getToNodeId())
                                .targetType(NodeJumpTargetTypeEnum.NODE)
                                .targetLabel(prevNodeInfo.getToNodeLabel())
                                .build();
                        result.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
                    }
                }
            }
        }
        return result;
    }


    /**
     * 计算答案拒绝率
     * 答案播放后, 后一个事件为用户输入, 匹配到的意图属性为拒绝, 则+1
     */
    private Map<AnswerUniqueKey, AtomicInteger> calculateAnswerDeclineCount(RobotRuntimeResource resource, List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<AnswerUniqueKey, AtomicInteger> result = new HashMap<>();

        AnswerLocateBO preAnswerLocate = null;
        String answerTemplate = null;
        for (EventLog eventLog : logList) {
            IntentPO intent;
            if (isUserSayFinishEvent(eventLog)) {
                if (Objects.nonNull(eventLog.getPredictResult())
                        && Objects.nonNull(preAnswerLocate)
                        && StringUtils.isNotBlank(answerTemplate)
                        && Objects.nonNull(intent = resource.getIntentMap().get(eventLog.getPredictResult().getIntentId()))) {

                    if (IntentPropertiesEnum.DECLINE.equals(intent.getIntentProperties())) {
                        AnswerUniqueKey key = AnswerUniqueKey.from(answerTemplate, preAnswerLocate);
                        result.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
                    }
                }
            }
            if (Objects.nonNull(eventLog.getAnswerLocate())) {
                preAnswerLocate = eventLog.getAnswerLocate();
                answerTemplate = eventLog.getAnswerTemplate();
            }
        }

        return result;
    }

    private boolean isUserSayFinishEvent(EventLog log) {
        if (Objects.isNull(log)) {
            return false;
        }
        return ChatEventTypeEnum.USER_SAY_FINISH.equals(log.getEvent());
    }

    private boolean checkAnswerSourceMatch(AnswerSourceEnum answerSource, EventLog log) {
        if (Objects.isNull(log) || Objects.isNull(log.getAnswerLocate())) {
            return false;
        }
        return answerSource.equals(log.getAnswerLocate().getAnswerSource());
    }
    private boolean checkReceptionAnswerSourceMatch(AnswerSourceEnum answerSource, EventLog log) {
        if (Objects.isNull(log) || Objects.isNull(log.getReceptionInfo()) || Objects.isNull(log.getReceptionInfo().getType())) {
            return false;
        }
        return answerSource.equals(log.getReceptionInfo().getType());
    }

    private Map<IntentTriggerUniqueKey, AtomicInteger> calculateKnowledgeReachCount(List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<IntentTriggerUniqueKey, AtomicInteger> result = new HashMap<>();
        logList.stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .filter(item -> checkAnswerSourceMatch(AnswerSourceEnum.KNOWLEDGE, item))
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .forEach(locate -> {
                    result.computeIfAbsent(IntentTriggerUniqueKey.of(AnswerSourceEnum.KNOWLEDGE, locate.getKnowledgeId(), locate.getKnowledgeLabel()), k -> new AtomicInteger(0)).incrementAndGet();
                });
        return result;
    }

    private Map<IntentTriggerUniqueKey, AtomicInteger> calculateSpecialAnswerReachCount(List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        Map<IntentTriggerUniqueKey, AtomicInteger> result = new HashMap<>();
        logList.stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .filter(item -> !ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT.equals(item.getSelectedConditionType()))
                .filter(item -> checkAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, item))
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .forEach(locate -> {
                    result.computeIfAbsent(IntentTriggerUniqueKey.of(AnswerSourceEnum.SPECIAL_ANSWER, locate.getSpecialAnswerConfigId(), locate.getSpecialAnswerConfigLabel()), k -> new AtomicInteger(0)).incrementAndGet();
                });

        logList.stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .filter(item -> !ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT.equals(item.getSelectedConditionType()))
                .filter(item -> !checkAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, item) && checkReceptionAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, item))
                .map(EventLog::getReceptionInfo)
                .forEach(receptionInfo -> {
                    result.computeIfAbsent(IntentTriggerUniqueKey.of(AnswerSourceEnum.SPECIAL_ANSWER, receptionInfo.getTargetId(), receptionInfo.getTargetLabel()), k -> new AtomicInteger(0)).incrementAndGet();
                });

        // 用户无应答
        logList.stream()
                .filter(item -> ChatEventTypeEnum.USER_SILENCE.equals(item.getEvent()))
                .filter(item -> !ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT.equals(item.getSelectedConditionType()))
                .filter(item -> checkAnswerSourceMatch(AnswerSourceEnum.SPECIAL_ANSWER, item))
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .forEach(locate -> {
                    result.computeIfAbsent(IntentTriggerUniqueKey.of(AnswerSourceEnum.SPECIAL_ANSWER, locate.getSpecialAnswerConfigId(), locate.getSpecialAnswerConfigLabel()), k -> new AtomicInteger(0)).incrementAndGet();
                });

        return result;
    }

    /**
     * 统计流程到达率, 流程自己的数据只有到达率
     * @return 流程到达次数, key: 流程id, value: 到达次数
     */
    private Map<String, AtomicInteger> calculateStepReachCount(List<EventLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }

        String currentStepId = null;
        Map<String, AtomicInteger> result = new HashMap<>();
        for (EventLog log : logList) {
            if (Objects.nonNull(log.getAnswerLocate())) {
                AnswerLocateBO locate = log.getAnswerLocate();
                if (AnswerSourceEnum.STEP.equals(locate.getAnswerSource())
                        || AnswerSourceEnum.LLM_STEP.equals(locate.getAnswerSource())) {
                    if (!StringUtils.equals(currentStepId, locate.getStepId())) {
                        currentStepId = locate.getStepId();
                        result.computeIfAbsent(currentStepId, k -> new AtomicInteger(0)).incrementAndGet();
                    }
                }
            }
        }
        return result;
    }



}
