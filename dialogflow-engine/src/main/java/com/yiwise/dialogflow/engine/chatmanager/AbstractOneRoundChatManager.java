package com.yiwise.dialogflow.engine.chatmanager;

import com.alibaba.fastjson2.JSON;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.domain.CandidateAnswer;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * 针对问答知识,特殊语境这种每次命中只有一轮对话的进行处理
 * 基本上需要处理的两种事件, 1. 处理用户输入给出答案, 2. 答案播放完成后, 执行跳转
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOneRoundChatManager extends AbstractChatManager {

    public AbstractOneRoundChatManager(RobotRuntimeResource resource) {
        super(resource);
    }

    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext) {
        switch (eventContext.getEvent()) {
            case ENTER:
                break;
            case USER_SILENCE:
                return userSilence(sessionContext, eventContext);
            case FAST_HANGUP:
            case USER_SAY_FINISH:
                return userSayFinish(sessionContext, eventContext);
            case AI_SAY_FINISH:
                return aiSayFinish(sessionContext, eventContext);
            default:
                break;
        }
        return Flux.empty();
    }

    protected Flux<ChatResponse> userSilence(SessionContext sessionContext, EventContext eventContext) {
        return Flux.empty();
    }

    protected Flux<ChatResponse> aiSayFinish(SessionContext sessionContext, EventContext eventContext) {
        Optional<KnowledgeAnswerRuntime> currentAnswer = getCurrentAnswer(sessionContext, eventContext);
        // 有些特殊逻辑要处理, 比如问答知识里有些状态需要设置
        doSpecialAction(sessionContext, eventContext);
        // 生成跳转等事件, 如果是等待用户应答, 则可能是返回空数据的
        return Mono.justOrEmpty(currentAnswer.flatMap(this::generateResponseWithAction)).flux();
    }

    protected void doSpecialAction(SessionContext sessionContext, EventContext eventContext) {

    }

    protected Optional<ChatResponse> generateResponseWithAction(KnowledgeAnswerRuntime answer) {
        List<ChatAction> actionList = answer.getActionList();
        ChatResponse response = new ChatResponse();
        // 这里需要判断一下, 如果当前的答案回答后操作是等待用户应答, 且当前对话节点开启了强制拉回, 则需要更新未回到原主流程
        response.setActionList(actionList);
        response.setAnswerLocate(answer.getLocate());
        return Optional.of(response);
    }

    protected HangupAction generateHangupAction() {
        return ActionHelper.generateHangupAction(resource);
    }

    protected abstract Optional<KnowledgeAnswerRuntime> getCurrentAnswer(SessionContext sessionContext, EventContext eventContext);

    protected Flux<ChatResponse> userSayFinish(SessionContext sessionContext, EventContext eventContext) {
        return doUserSayFinish(sessionContext, eventContext).map(Flux::just).orElseGet(Flux::empty);
    }

    protected Optional<ChatResponse> doUserSayFinish(SessionContext sessionContext, EventContext eventContext) {
        if (SessionContextHelper.isHangupDelayMode(sessionContext)) {
            if (!(this instanceof KnowledgeChatManager)) {
                log.info("延迟挂机匹配特殊语境");
                // 返回最后的答案, 比设置
                // 这是啥?
            }
        }
        // 获取答案
        Optional<CandidateAnswer<KnowledgeAnswerRuntime>> answerOptional = getAvailableAnswer(sessionContext, eventContext);
        if (!answerOptional.isPresent()) {
            log.warn("未找到可用的问答知识答案, 意图预测结果: {}", JSON.toJSONString(eventContext.getPredictResult()));
            return Optional.empty();
        }
        KnowledgeAnswerRuntime answer = answerOptional.get().getAnswer();
        RepeatAnswerPlayStrategyEnum strategy = answerOptional.get().getPlayStrategy();

        // 进行渲染
        AnswerResult answerResult = renderAnswer(answer, sessionContext, eventContext);

        // 生成debug log
        generateDebugLog(sessionContext, eventContext, answer, answerResult);

        return Optional.of(generateResponse(sessionContext, eventContext, answerResult, strategy));
    }

    protected void generateDebugLog(SessionContext sessionContext, EventContext eventContext, KnowledgeAnswerRuntime answer, AnswerResult answerResult) {
        generateIntentDebugLog(sessionContext, eventContext);
        DebugLogUtils.matchAnswer(eventContext, answerResult);
        DebugLogUtils.postAction(eventContext, answer.origin, resource);
    }

    protected AnswerResult renderAnswer(KnowledgeAnswerRuntime answer, SessionContext sessionContext, EventContext eventContext) {
        AnswerResult answerResult = AnswerRenderUtils.render(answer, sessionContext, resource);
        answerResult.setUninterrupted(BooleanUtils.isTrue(answer.origin.getEnableUninterrupted()));
        answerResult.setCustomInterruptThreshold(answer.origin.getCustomInterruptThreshold());
        if (BooleanUtils.isTrue(answer.origin.getEnableUninterrupted())
                && (Objects.isNull(answer.origin.getCustomInterruptThreshold()) || (Objects.nonNull(answer.origin.getCustomInterruptThreshold())
                && answer.origin.getCustomInterruptThreshold() > 0))) {
            if (CollectionUtils.isNotEmpty(answer.origin.getUninterruptedReplyStepIdList())
                    || CollectionUtils.isNotEmpty(answer.origin.getUninterruptedReplyKnowledgeIdList())
                    || CollectionUtils.isNotEmpty(answer.origin.getUninterruptedReplySpecialAnswerIdList())
                    || CollectionUtils.isNotEmpty(answer.origin.getUninterruptedReplyBranchIntentIdList())) {
                answerResult.setNeedTryReplyOnUninterrupted(true);
            }
        }
        return answerResult;
    }

    protected void generateIntentDebugLog(SessionContext sessionContext, EventContext eventContext) {
        if (Objects.nonNull(eventContext.getPredictResult())) {
            DebugLogUtils.predictDetail(eventContext, eventContext.getPredictResult());
        }
    }

    protected ChatResponse generateResponse(SessionContext sessionContext,
                                            EventContext eventContext,
                                            AnswerResult answerResult,
                                            RepeatAnswerPlayStrategyEnum strategy) {
        if (Objects.isNull(answerResult.getCustomInterruptThreshold())) {
            answerResult.setCustomInterruptThreshold(100);
        }
        ChatResponse response = new ChatResponse(strategy);
        response.setAnswer(answerResult);
        response.setActionList(Collections.emptyList());
        return response;
    }

    protected abstract Optional<CandidateAnswer<KnowledgeAnswerRuntime>> getAvailableAnswer(SessionContext sessionContext,
                                                                                            EventContext eventContext);
}
