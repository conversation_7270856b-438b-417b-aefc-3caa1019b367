package com.yiwise.dialogflow.engine.helper;

import com.yiwise.dialogflow.api.dto.response.BotInfo;
import com.yiwise.dialogflow.api.dto.response.SimpleBotInfo;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.LLMStepRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.dto.llmchat.*;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.LlmGuideAnswerConfigEnum;
import com.yiwise.dialogflow.entity.po.LlmStepCollectTaskConfigPO;
import com.yiwise.dialogflow.entity.po.LlmStepVariableAssignConfigPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.yiwise.dialogflow.entity.enums.llm.LlmStepTypeEnum.COLLECT_TASK;

@Slf4j
public class LLMRequestHelper {

    public static LLMChatRequest initRequest(SessionContext sessionContext,
                                             RobotRuntimeResource resource,
                                             LLMStepRuntime llmStepRuntime,
                                             EventContext eventContext) {
        LLMChatRequest request = new LLMChatRequest();
        request.setSessionId(sessionContext.getSessionId());
        request.setDialogFlowId(String.valueOf(llmStepRuntime.getBotId()));
        request.setFileIdList(resource.getUsedRagDocumentIdList());

        SimpleBotInfo botInfo = new SimpleBotInfo();
        botInfo.setBotId(resource.getBotId());
        botInfo.setName(resource.getName());
        request.setBotInfo(botInfo);
        request.setUsePrefillResponse(LlmGuideAnswerConfigEnum.ENABLE_GUIDE_ANSWER.equals(resource.getBotConfig().getLlmGuideAnswerConfig()));
        request.setCallLogId(eventContext.getLogId());
        request.setModelName(llmStepRuntime.getLlmModelName());

        if (COLLECT_TASK.equals(llmStepRuntime.getLlmStepType())) {
            LLMChatConfig config = new LLMChatConfig();
            request.setConfig(config);
            config.setBackground(renderPrompt(llmStepRuntime.getOriginStepConfig().getRoleDesc(), sessionContext));
            config.setKnowledge(renderPrompt(llmStepRuntime.getOriginStepConfig().getBackground(), sessionContext));
            List<LLMChatTask> tasks = new ArrayList<>();
            config.setTasks(tasks);
            if (CollectionUtils.isNotEmpty(llmStepRuntime.getOriginStepConfig().getCollectTaskConfigList())) {
                for (LlmStepCollectTaskConfigPO taskConfig : llmStepRuntime.getOriginStepConfig().getCollectTaskConfigList()) {
                    LLMChatTask task = new LLMChatTask();
                    task.setDescription(renderPrompt(taskConfig.getDesc(), sessionContext));
                    task.setScripts(Collections.singletonList(renderPrompt(taskConfig.getGuideAnswer(), sessionContext)));
                    tasks.add(task);
                }
            }
        } else {
            // 自由配置型流程

            // 需要进行变量的渲染

            request.setSystem(renderPrompt(llmStepRuntime.getOriginStepConfig().getPrompt(), sessionContext));
        }

        // 采集信息
        List<LlmStepVariableAssignConfigPO> variableAssignConfigList = new ArrayList<>();

        if (COLLECT_TASK.equals(llmStepRuntime.getLlmStepType())) {
            if (CollectionUtils.isNotEmpty(llmStepRuntime.getOriginStepConfig().getCollectTaskConfigList())) {
                for (LlmStepCollectTaskConfigPO llmStepCollectTaskConfigPO : llmStepRuntime.getOriginStepConfig().getCollectTaskConfigList()) {
                    if (CollectionUtils.isNotEmpty(llmStepCollectTaskConfigPO.getVariableAssignConfigList())) {
                        variableAssignConfigList.addAll(llmStepCollectTaskConfigPO.getVariableAssignConfigList());
                    }
                }
            }
        } else {
            variableAssignConfigList = llmStepRuntime.getOriginStepConfig().getVariableAssignConfigList();
        }

        if (CollectionUtils.isNotEmpty(variableAssignConfigList)) {
            List<LLMChatCollectInfo> collectInfoList = new ArrayList<>();
            for (LlmStepVariableAssignConfigPO collectConfig : variableAssignConfigList) {
                VariablePO var = resource.getVariableIdMap().get(collectConfig.getVariableId());
                if (Objects.nonNull(var)) {
                    LLMChatCollectInfo collectInfo = new LLMChatCollectInfo();
                    collectInfo.setName(renderPrompt(var.getName(), sessionContext));
                    collectInfo.setDescription(renderPrompt(collectConfig.getDesc(), sessionContext));
                    collectInfoList.add(collectInfo);
                } else {
                    log.warn("数据错误, 变量不存在, botId:{}, varId:{}", resource.getBotId(), collectConfig.getVariableId());
                }
            }
            request.setCollectInfo(collectInfoList);
        } else {
            request.setCollectInfo(Collections.emptyList());
        }

        // 工具调用, 目前仅支持跳转
        List<LLMChatTool> tools = new ArrayList<>();

        // 跳转到所有的独立对话流
        Set<String> mismatchStepIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(llmStepRuntime.getExcludeStepIdSet())) {
            mismatchStepIdSet.addAll(llmStepRuntime.getExcludeStepIdSet());
        }
        resource.getStepIdMap().forEach((stepId, step) -> {
            if (StepTypeEnum.INDEPENDENT.equals(step.getType())
                    && !mismatchStepIdSet.contains(stepId)) {
                if (StringUtils.isNotBlank(step.getDesc())) {
                    LLMChatTool tool = new LLMChatTool();
                    tool.setName(String.format("tool-%s", step.getLabel()));
                    tool.setDescription(renderPrompt(step.getDesc(), sessionContext));
                    tools.add(tool);
                }
            }
        });

        SimpleBotInfo simpleBotInfo = new SimpleBotInfo();
        simpleBotInfo.setBotId(resource.getBotId());
        simpleBotInfo.setName(resource.getName());
        request.setBotInfo(simpleBotInfo);
        // 把所有的流程
        request.setTools(tools);

        return request;
    }



    private static String renderPrompt(String template, SessionContext sessionContext) {
        return AnswerTextUtils.renderTemplate(template, sessionContext.getGlobalVariableValueMap());
    }

}
