package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.engine.chatmanager.*;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class RepeatAnswerChatManager extends AbstractChatManager {

    private final SpecialAnswerConfigPO repeatConfig;

    private final boolean enable;

    private final ChatManager stepFlowDispatchManager;
    private final ChatManager knowledgeChatManager;
    private final ChatManager llmChatManager;

    private final ActiveManagerInfo activeManagerInfo;

    public RepeatAnswerChatManager(RobotRuntimeResource resource,
                                   ChatManager stepFlowDispatchManager,
                                   ChatManager knowledgeChatManager,
                                   ChatManager llmChatManager) {
        super(resource);
        this.stepFlowDispatchManager = stepFlowDispatchManager;
        this.knowledgeChatManager = knowledgeChatManager;
        this.llmChatManager = llmChatManager;
        repeatConfig = resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.AI_REPEAT);
        enable = Objects.nonNull(repeatConfig) && EnabledStatusEnum.ENABLE.equals(repeatConfig.getEnabledStatus());
        if (enable) {
            ActiveManagerInfo info = new ActiveManagerInfo();
            info.setActiveType(ActiveTypeEnum.SPECIAL_ANSWER);
            info.setChatManagerName(getName());
            info.setOriginId(repeatConfig.getId());
            info.setOriginName(repeatConfig.getName());
            info.setOriginLabel(repeatConfig.getLabel());
            this.activeManagerInfo = info;
        } else {
            this.activeManagerInfo = null;
        }
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        RepeatAnswerContext context = new RepeatAnswerContext();
        sessionContext.setRepeatAnswerContext(context);
    }

    @Override
    public String getName() {
        return "重复上一句";
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        return sessionContext.getActiveManagerInfo();
    }

    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext) {
        if (!enable) {
            return Flux.empty();
        }
        switch (eventContext.getEvent()) {
            case USER_SAY_FINISH:
            case AI_SAY_FINISH:
                return doJumpToOriginManager(sessionContext, eventContext);
            default:
                break;
        }

        return Flux.empty();
    }

    private Flux<ChatResponse> doJumpToOriginManager(SessionContext sessionContext, EventContext eventContext) {

        RepeatAnswerContext context = sessionContext.getRepeatAnswerContext();
        if (Objects.isNull(context)) {
            return Flux.empty();
        }
        if (StringUtils.isBlank(context.getLastRepeatableAnswerId())) {
            log.info("未记录可重复的答案来源信息");
            return Flux.empty();
        }

        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(context.getLastRepeatableAnswerId());

        if (Objects.isNull(locate)) {
            log.info("未记录可重复的答案来源信息");
            return Flux.empty();
        }
        DebugLogUtils.predictDetail(eventContext, eventContext.getPredictResult());
        DebugLogUtils.aiRepeatAnswer(eventContext);

        switch (locate.getAnswerSource()) {
            case LLM_STEP:
            case STEP:
                eventContext.setRepeatAnswer(true);
                return stepFlowDispatchManager.process(sessionContext, eventContext);
            case KNOWLEDGE:
                eventContext.setRepeatAnswer(true);
                eventContext.setRepeatKnowledgeId(locate.getKnowledgeId());
                return knowledgeChatManager.process(sessionContext, eventContext);
            case SPECIAL_ANSWER:
                if (SpecialAnswerConfigPO.LLM.equals(locate.getSpecialAnswerConfigName())) {
                    eventContext.setRepeatAnswer(true);
                    return llmChatManager.process(sessionContext, eventContext);
                }
            default:
                break;
        }

        return Flux.empty();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        List<ChatManagerTriggerCondition> conditions = new ArrayList<>();
        conditions.add(triggerIntent(context));
        return conditions;
    }

    private ChatManagerTriggerCondition triggerIntent(EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTENT_TRIGGER_SPECIAL_ANSWER);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(new HashSet<>(repeatConfig.getTriggerIntentIdList()));
        condition.setIntentRefType(IntentRefTypeEnum.SPECIAL_ANSWER);
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.DELAY_HANGUP);
        if (context.getCanInterruptSpecialAnswerIdSet().contains(repeatConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", repeatConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.KEY_CAPTURE);
        condition.setDynamicPredicate((sc, ec)
                -> !SessionContextHelper.getCurrentStepExcludeSpecialAnswerConfigNameList(sc, resource).contains(SpecialAnswerConfigPO.AI_REPEAT));
        return condition;
    }
}
