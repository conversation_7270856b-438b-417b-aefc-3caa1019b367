package com.yiwise.dialogflow.engine.chatmanager;


import com.yiwise.dialogflow.engine.context.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractChatManager implements ChatManager {

    protected final RobotRuntimeResource resource;

    public AbstractChatManager(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public final Flux<ChatResponse> process(SessionContext sessionContext, EventContext eventContext) {
        Flux<ChatResponse> result = doProcess(sessionContext, eventContext);
        return result.doOnNext(r -> {
            ActiveManagerInfo activeManagerInfo = getChatManagerInfo(sessionContext);
            // todo 这里应该是多余的
//            activeManagerInfo.setChatManagerName(getName());
            sessionContext.setActiveManagerInfo(activeManagerInfo);
            eventContext.setActiveManagerInfo(activeManagerInfo);
        });
    }

    public abstract ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext);

    public abstract Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext context);

}
