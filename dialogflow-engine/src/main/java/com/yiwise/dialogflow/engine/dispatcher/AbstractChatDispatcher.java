package com.yiwise.dialogflow.engine.dispatcher;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
public abstract class AbstractChatDispatcher implements Chat<PERSON><PERSON>patcher {

    @Override
    public final Flux<ChatResponse> dispatchAsync(SessionContext sessionContext, EventContext eventContext) {
        return doDispatchAsync(sessionContext, eventContext);
    }

    protected Flux<ChatResponse> doDispatchAsync(SessionContext sessionContext, EventContext eventContext) {
        return doDispatch(sessionContext, eventContext);
    }

    protected abstract Flux<ChatResponse> doDispatch(SessionContext sessionContext, EventContext eventContext);

    @Override
    public Flux<ChatResponse> dispatchLLMEventAsync(SessionContext sessionContext, EventContext eventContext) {
        return Flux.empty();
    }
}
