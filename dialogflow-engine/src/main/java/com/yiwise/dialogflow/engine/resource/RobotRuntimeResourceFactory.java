package com.yiwise.dialogflow.engine.resource;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;


import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class RobotRuntimeResourceFactory {
    private static final RobotResourceLoader ROBOT_RESOURCE_LOADER = new DefaultRobotResourceLoader();

    private static final Cache<Key, RobotRuntimeResource> HOT_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.HOURS)
            .build();

    private static final LoadingCache<Key, Object> LOCK_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.HOURS)
            .build(CacheLoader.from(key -> new Object()));

    public static RobotResourceLoader getRobotResourceLoader() {
        return ROBOT_RESOURCE_LOADER;
    }

    public static RobotRuntimeResource getRuntimeResource(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Key key = Key.of(botId, usageTarget, version);
        RobotRuntimeResource resource = HOT_CACHE.getIfPresent(key);
        if (Objects.isNull(resource)) {
            synchronized (RobotRuntimeResourceFactory.class) {
                resource = HOT_CACHE.getIfPresent(key);
                if (Objects.isNull(resource)) {
                    resource = loadVersionResource(botId, usageTarget, version).block();
                    HOT_CACHE.put(key, resource);
                }
            }
        }
        return resource;
    }

    public static Mono<RobotRuntimeResource> asyncGetRuntimeResource(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Key key = Key.of(botId, usageTarget, version);
        RobotRuntimeResource resource = HOT_CACHE.getIfPresent(key);
        if (Objects.nonNull(resource)) {
            return Mono.just(resource);
        }
        return Mono.defer(() -> {
            Object lock = LOCK_CACHE.getUnchecked(key);
            synchronized (lock) {
                RobotRuntimeResource runtimeResource = HOT_CACHE.getIfPresent(key);
                if (Objects.nonNull(runtimeResource)) {
                    return Mono.just(runtimeResource);
                }
                return loadVersionResource(botId, usageTarget, version)
                        .doOnNext(r -> HOT_CACHE.put(key, r));
            }
        }).subscribeOn(Schedulers.elastic());
    }

    /**
     * 异步提前加载
     * 为什么需要提前加载: 从数据库中加载snapshot并预处理完成会消耗比较久的时间, 在对话的时候, 会导致加载时接口响应慢和请求堆积
     * 什么时候会提前加载: 1. 当前服务getLastVersionNumber获取到新的版本时, 就提前加载
     * 2. 其他服务加载了新的快照, 发消息通知其他服务
     */
    public static Mono<RobotRuntimeResource> asyncPreLoad(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Key key = Key.of(botId, usageTarget, version);
        RobotRuntimeResource resource = HOT_CACHE.getIfPresent(key);
        if (resource != null) {
            return Mono.just(resource);
        }
        // todo 异步加载
        return Mono.empty();
    }

    private static Mono<RobotRuntimeResource> loadVersionResource(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return ROBOT_RESOURCE_LOADER.asyncLoad(botId, usageTarget, version)
                .switchIfEmpty(Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, String.format("机器人指定版本快照不存在, botId=%s, version=%s", botId, version))));
    }

    @Data
    @AllArgsConstructor
    @EqualsAndHashCode
    private static class Key {
        Long botId;
        RobotSnapshotUsageTargetEnum usageTarget;
        Integer version;

        public static Key of(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
            return new Key(botId, usageTarget, version);
        }
    }
}
