package com.yiwise.dialogflow.engine;


import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.chatfilter.*;
import com.yiwise.dialogflow.engine.chatprocessor.*;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.dispatcher.ChatDispatcher;
import com.yiwise.dialogflow.engine.dispatcher.DynamicDispatcher;
import com.yiwise.dialogflow.engine.dispatcher.UserSayDispatcher;
import com.yiwise.dialogflow.engine.resource.JumpNodeRuntime;
import com.yiwise.dialogflow.engine.resource.NodeRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.share.ChatMetaData;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.EnterEvent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.engine.utils.SessionContextSerializeUtils;
import io.vavr.Tuple2;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 用户预测测试的对话引擎, 会少一些组件
 */
public class PredictTestChatEngine extends AbstractChatEngine {

    private DynamicDispatcher defaultDispatcher;

    private RecordLastAnswerPostProcessor recordLastAnswerPostProcessor;

    private ChatDataCollectPostProcessor chatDataCollectPostProcessor;

    private HangupDelayModeCheckPostProcessor hangupDelayModeCheckPostProcessor;

    private KeyCaptureModeCheckPostProcessor keyCaptureModeCheckPostProcessor;

    private InaudibleModeExitPostProcessor inaudibleModeExitPostProcessor;

    private IntentPersistencePostProcessor intentPersistencePostProcessor;

    private DynamicThresholdIntentPredictPreProcessor intentPredictPreProcessor;

    private EventContext eventContext;

    public PredictTestChatEngine(RobotRuntimeResource resource, Double lowerThreshold, Double upperThreshold) {
        super(resource.getBotId(), resource.getUsageTarget(), resource);
        if (Objects.nonNull(intentPredictPreProcessor)) {
            intentPredictPreProcessor.setUpperThreshold(upperThreshold);
            intentPredictPreProcessor.setLowerThreshold(lowerThreshold);
        }
    }

    public Flux<SessionContext> generateAndInitSessionContext(String nodeLabel) {
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setSessionId(new ObjectId().toHexString());
        ChatMetaData chatMetaData = new ChatMetaData();
        SessionContext sessionContext = super.generateAndInitSessionContext(sessionInfo, chatMetaData);

        // 将对话状态重置至指定节点
        Optional<NodeRuntime<?>> nodeOpt = resource.getNodeIdMap().values().stream().filter(item -> nodeLabel.equals(item.getLabel())).findFirst();
        if (!nodeOpt.isPresent()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点不存在");
        }
        NodeRuntime<?> node = nodeOpt.get();
        if (node instanceof JumpNodeRuntime) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不能指定跳转节点");
        }

        // 执行enter
        ChatRequest request = new ChatRequest();
        request.setSessionContextJson(SessionContextSerializeUtils.serialize(sessionContext));
        request.setParam(new EnterEvent());
        return super.processAsync(request)
                .map(response -> {
                    SessionContext context = SessionContextSerializeUtils.deserialize(response.getSessionContextJson());
                    StepRuntime step = resource.getStepIdMap().get(node.getStepId());
                    StepFlowContext stepFlowContext = new StepFlowContext();
                    stepFlowContext.setStepId(step.getId());
                    stepFlowContext.setStepName(step.getName());
                    stepFlowContext.setCurrentNodeId(node.getId());
                    stepFlowContext.setAssignConfig(null);
                    context.getStepFlowContextMap().put(step.getId(), stepFlowContext);
                    context.setLastActiveStepId(step.getId());
                    ActiveManagerInfo activeManagerInfo = new ActiveManagerInfo();
                    ActiveManagerInfo info = new ActiveManagerInfo();
                    info.setActiveType(ActiveTypeEnum.STEP);
                    info.setChatManagerName("StepFlowDispatchManager");
                    info.setOriginId(step.getId());
                    info.setOriginName(step.getName());
                    info.setOriginLabel(step.getLabel());
                    context.setActiveManagerInfo(activeManagerInfo);
                    return context;
                });
    }

    public Mono<Tuple2<ChatResponse, EventContext>> predictTest(ChatRequest chatRequest) {
        return Mono.empty();
//        return processAsync(chatRequest)
//                .map(response -> new Tuple2<>(response, eventContext));
    }

    @Override
    protected EventContext initEventContext() {
        eventContext = new EventContext();
        return eventContext;
    }

    @Override
    protected List<ChatFilter> initChatFilter(ChatEventTypeEnum eventType) {
        List<ChatFilter> result = new ArrayList<>();
        switch (eventType) {
            case ENTER:
            case USER_SILENCE:
            case AI_SAY_FINISH:
            case FAST_HANGUP:
                // 这三个事件不涉及到用户输入, 没有过滤逻辑要执行
                break;
            case USER_SAY:
                result.add(new ToneWordChatFilter(resource));
                result.add(new InterruptChatFilter(resource));
                result.add(new NoiseChatFilter(resource));
                result.add(new PauseAudioPlayFilter());
                break;
            case USER_SAY_FINISH:
                result.add(new AsrCorrectionChatFilter(resource));
                result.add(new ToneWordChatFilter(resource));
                result.add(new InterruptChatFilter(resource));
                result.add(new NoiseChatFilter(resource));
                break;
            default:
                break;
        }

        return result;
    }

    @Override
    protected void initChatComponent() {
        defaultDispatcher = new DynamicDispatcher(resource);
    }


    @Override
    protected List<ChatPreProcessor> initPreProcessorList(ChatEventTypeEnum eventType) {
        List<ChatPreProcessor> result = new ArrayList<>();
        switch (eventType) {
            case ENTER:
                break;
            case AI_SAY_FINISH:
                AudioPlayProgressProcessor playProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(playProgressProcessor);
                break;
            case USER_SAY:
                break;
            case USER_SAY_FINISH:
                AudioPlayProgressProcessor audioPlayProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(audioPlayProgressProcessor);

                result.add(new EntityCollectPreProcessor(resource));

                intentPredictPreProcessor = new DynamicThresholdIntentPredictPreProcessor(resource);
                result.add(intentPredictPreProcessor);

                CheckOnlyReplySpecialPreProcessor checkOnlyReplySpecialPreProcessor = new CheckOnlyReplySpecialPreProcessor(resource);
                result.add(checkOnlyReplySpecialPreProcessor);
                break;
            case USER_SILENCE:
                // 用户无应答了,正常来说录音应该是一定播放完成了
                audioPlayProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(audioPlayProgressProcessor);

                UserSilenceIntentPreProcessor userSilenceIntentPreProcessor = new UserSilenceIntentPreProcessor();
                result.add(userSilenceIntentPreProcessor);
            default:
                break;
        }
        return result;
    }

    @Override
    protected ChatDispatcher initChatDispatcher(ChatEventTypeEnum eventType) {
        if (ChatEventTypeEnum.USER_SAY.equals(eventType)) {
            return new UserSayDispatcher(resource);
        }
        return defaultDispatcher;
    }

    @Override
    protected List<ChatPostProcessor> initPostProcessorList(ChatEventTypeEnum eventType) {
        List<ChatPostProcessor> result = new ArrayList<>();

        // userSay暂不处理
        if (ChatEventTypeEnum.USER_SAY.equals(eventType)) {
            return result;
        }

        result.add(initRecordLastAnswer());

        switch (eventType) {
            case ENTER:
            case AI_SAY_FINISH:
            case USER_SAY_FINISH:
                result.add(getHangupDelayProcessor());
                result.add(getInaudibleModeExitProcessor());
                break;
            case USER_SILENCE:
            default:
                break;
        }
        result.add(initChatDataCollect());
        result.add(getKeyCaptureModeCheckPostProcessor());
        return result;
    }

    private ChatPostProcessor getInaudibleModeExitProcessor() {
        if (inaudibleModeExitPostProcessor == null) {
            inaudibleModeExitPostProcessor = new InaudibleModeExitPostProcessor(resource);
        }
        return inaudibleModeExitPostProcessor;
    }

    private ChatPostProcessor getHangupDelayProcessor() {
        if (Objects.isNull(hangupDelayModeCheckPostProcessor)) {
            hangupDelayModeCheckPostProcessor = new HangupDelayModeCheckPostProcessor(resource);
        }
        return hangupDelayModeCheckPostProcessor;
    }

    private ChatPostProcessor getKeyCaptureModeCheckPostProcessor() {
        if (Objects.isNull(keyCaptureModeCheckPostProcessor)) {
            keyCaptureModeCheckPostProcessor = new KeyCaptureModeCheckPostProcessor();
        }
        return keyCaptureModeCheckPostProcessor;
    }

    private ChatDataCollectPostProcessor initChatDataCollect() {
        if (Objects.isNull(chatDataCollectPostProcessor)) {
            chatDataCollectPostProcessor = new ChatDataCollectPostProcessor();
        }
        return chatDataCollectPostProcessor;
    }

    private RecordLastAnswerPostProcessor initRecordLastAnswer() {
        if (Objects.isNull(recordLastAnswerPostProcessor)) {
            recordLastAnswerPostProcessor = new RecordLastAnswerPostProcessor();
        }
        return recordLastAnswerPostProcessor;
    }

    private IntentPersistencePostProcessor initIntentPersistence() {
        if (Objects.isNull(intentPersistencePostProcessor)) {
            intentPersistencePostProcessor = new IntentPersistencePostProcessor(resource);
        }
        return intentPersistencePostProcessor;
    }
}
