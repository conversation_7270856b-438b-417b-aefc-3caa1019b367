package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.chatfilter.ChatFilter;
import com.yiwise.dialogflow.engine.chatprocessor.ChatPostProcessor;
import com.yiwise.dialogflow.engine.chatprocessor.ChatPreProcessor;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.dispatcher.ChatDispatcher;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EventProcessPipeline implements ChatComponent {

    private List<ChatFilter> chatFilterList;

    private List<ChatPreProcessor> preProcessorList;

    private ChatDispatcher chatDispatcher;

    private List<ChatPostProcessor> postProcessorList;

    public void initContext(SessionContext sessionContext) {
        if (CollectionUtils.isNotEmpty(chatFilterList)) {
            chatFilterList.forEach(chatFilter -> chatFilter.initContext(sessionContext));
        }
        if (CollectionUtils.isNotEmpty(preProcessorList)) {
            preProcessorList.forEach(chatPreProcessor -> chatPreProcessor.initContext(sessionContext));
        }
        if (chatDispatcher != null) {
            chatDispatcher.initContext(sessionContext);
        }
        if (CollectionUtils.isNotEmpty(postProcessorList)) {
            postProcessorList.forEach(chatPostProcessor -> chatPostProcessor.initContext(sessionContext));
        }
    }

    @Override
    public String getName() {
        return "EventProcessPipeline";
    }
}
