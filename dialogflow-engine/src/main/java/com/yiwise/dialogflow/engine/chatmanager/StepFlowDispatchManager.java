package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.StepSkipConditionPredicate;
import com.yiwise.dialogflow.engine.context.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.context.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.share.action.*;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.JumpTargetEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.po.StepPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 流程调度逻辑, 对流程的处理又包负责多个对话流之间跳转等逻辑
 * <AUTHOR>
 */
@Slf4j
public class StepFlowDispatchManager extends AbstractChatManager{
    private final Map<String, AbstractStepFlowChatManager> stepFlowChatManagerMap = new HashMap<>();

    private final List<String> mainStepIdList = new ArrayList<>();

    private final String rootStepId;

    public StepFlowDispatchManager(RobotRuntimeResource resource) {
        super(resource);
        log.info("init");
        // 初始化所有的流程
        resource.getStepIdMap().forEach((stepId, step) -> {
            if (isLLMStepFlow(step)) {
                stepFlowChatManagerMap.put(stepId, new LLMStepFlowChatManager(resource, step));
            } else {
                stepFlowChatManagerMap.put(stepId, new StepFlowChatManager(resource, step));
            }
        });

        rootStepId = resource.getMainStepList().get(0).getId();
        for (StepPO step : resource.getMainStepList()) {
            mainStepIdList.add(step.getId());
        }
    }

    private boolean isLLMStepFlow(StepRuntime stepRuntime) {
        return StepSubTypeEnum.isLlm(stepRuntime.getSubType());
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    public String getName() {
        return "StepFlowDispatchManager";
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        ActiveManagerInfo info = new ActiveManagerInfo();
        info.setActiveType(ActiveTypeEnum.STEP);
        info.setChatManagerName(getName());

        AbstractStepFlowChatManager currentStepFlowChatManager = getCurrentActiveStepManager(sessionContext);
        info.setOriginId(currentStepFlowChatManager.getChatManagerInfo(sessionContext).getOriginId());
        info.setOriginName(currentStepFlowChatManager.getChatManagerInfo(sessionContext).getOriginName());
        info.setOriginLabel(currentStepFlowChatManager.getChatManagerInfo(sessionContext).getOriginLabel());
        info.setLLM(currentStepFlowChatManager.getChatManagerInfo(sessionContext).isLLM());
        return info;
    }

    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext) {
        return execCurrentStep(sessionContext, eventContext)
                .flatMap(response -> this.loopExecJumpActionAsync(sessionContext, eventContext, response))
                .doOnTerminate(() -> {
                    ActiveManagerInfo activeManagerInfo = getChatManagerInfo(sessionContext);
                    sessionContext.setActiveManagerInfo(activeManagerInfo);
                    eventContext.setActiveManagerInfo(activeManagerInfo);
                });
    }

    /**
     * 执行流程间的跳转, 即跳转节点设置的跳转动作, 即是从流程出来之后的跳转处理
     */
    public Flux<ChatResponse> loopExecJumpActionAsync(SessionContext sessionContext, EventContext eventContext, ChatResponse response) {
        // 处理流程直接的跳转, 可能存在多次跳转?

        // 如果节点开启了强制拉回, 那么问答知识就是等待事件, 此时也需要执行跳转
        // 判断当前流程的状态是否需要拉回,
        // 如果需要拉回, 走单独的逻辑,
        AtomicBoolean enablePullback = new AtomicBoolean();
        SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext)
                .ifPresent(stepContext -> {
                    if (stepContext.isEnablePullback()) {
                        enablePullback.set(true);
                    }
                });

        boolean isKnowledge = response != null
                && response.getAnswerLocate() != null
                && AnswerSourceEnum.KNOWLEDGE.equals(response.getAnswerLocate().getAnswerSource());

        boolean isWait = response != null
                && CollectionUtils.isNotEmpty(response.getActionList())
                && response.getActionList().stream().anyMatch(action -> action instanceof WaitAction);

        Flux<ChatResponse> responseFlux;
        if (enablePullback.get() && isKnowledge && isWait) {
            log.info("当前流程开启了强制拉回, 且问答知识回答后是等待用户应答, 执行拉回逻辑");
            AnswerLocateBO locate = response.getAnswerLocate();
            JumpAction jumpAction = new JumpAction(locate.getAnswerSource(), locate.getKnowledgeId());
            jumpAction.setJumpTarget(JumpTargetEnum.ORIGINAL_STEP);
            responseFlux = execJumpAction(sessionContext, eventContext, jumpAction);
        } else {
            if (Objects.nonNull(response) && needJumpStep(response)) {
                List<JumpAction> jumpActionList = ActionHelper.getJumpActionList(response.getActionList());
                // 一般应该是只有一个jumpActionList
                JumpAction action = jumpActionList.get(0);
                responseFlux = execJumpAction(sessionContext, eventContext, action);
            } else {
                responseFlux = Mono.justOrEmpty(response).flux();
            }
        }

        return responseFlux
                .doOnNext(chatResponse -> {
                    ActiveManagerInfo activeManagerInfo = getChatManagerInfo(sessionContext);
                    sessionContext.setActiveManagerInfo(activeManagerInfo);
                    eventContext.setActiveManagerInfo(activeManagerInfo);
                })
                .map(chatResponse -> {
                    // 添加加微动作
                    addWechatAction(chatResponse, eventContext);
                    return chatResponse;
                });
    }

    private void addWechatAction(ChatResponse chatResponse, EventContext eventContext) {
        if (!eventContext.isNeedAddWechat() || chatResponse == null) {
            return;
        }
        List<ChatAction> actionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(chatResponse.getActionList())) {
            actionList.addAll(chatResponse.getActionList());
        }
        actionList.add(new AddWechatAction());
        chatResponse.setActionList(actionList);
    }

    public Flux<ChatResponse> execJumpToStepAction(SessionContext sessionContext, EventContext eventContext, ChatResponse response) {
        return loopExecJumpActionAsync(sessionContext, eventContext, response);
    }


    private Flux<ChatResponse> doExecJumpAction(SessionContext sessionContext, EventContext eventContext, JumpAction action) {
        switch (action.getJumpTarget()) {
            case NEXT_STEP:
                Optional<AbstractStepFlowChatManager> nextStepManagerOpt = getNextMainStepManager(sessionContext, eventContext);
                if (!nextStepManagerOpt.isPresent()) {
                    log.warn("[LogHub_Warn]数据异常, 在最后一个主流程继续跳转到下一个主流程, 执行挂机操作");
                    // 生成挂机响应
                    ChatResponse chatResponse = new ChatResponse();
                    chatResponse.setActionList(Collections.singletonList(new HangupAction()));
                    return Flux.just(chatResponse);
                }
                AbstractStepFlowChatManager nextStepManager = nextStepManagerOpt.get();
                nextStepManager.initContext(sessionContext);
                DebugLogUtils.commonDebugLog(eventContext, String.format("跳转到下一主动流程:%s", nextStepManager.getStepName()), false);
                return doStepProcess(sessionContext, eventContext, nextStepManager);
            case ORIGINAL_STEP:
                // 只有原主动流程这里需要这个流程的对话状态, 其他的跳转都是重新初始化为根节点之后重新开始对话
                AbstractStepFlowChatManager currentStepManager = getCurrentMainStepManager(sessionContext, eventContext);
                log.info("跳转回到原主动流程:{}", currentStepManager.getName());
                DebugLogUtils.commonDebugLog(eventContext, String.format("回到原主流程:%s", currentStepManager.getStepName()), false);
                eventContext.setPullbackToOrigin(true);
                eventContext.setJumpSource(action.getJumpSourceType());
                return doStepProcess(sessionContext, eventContext, currentStepManager)
                        .doOnTerminate(() -> {
                            eventContext.setPullbackToOrigin(false);
                            eventContext.setJumpSource(null);
                        });
            case SPECIFIED_STEP:
                // todo @产品 这里如果特殊流程就是原来的主流程要怎么处理?
                AbstractStepFlowChatManager manager = stepFlowChatManagerMap.get(action.getJumpStepId());
                manager.initContext(sessionContext);

                log.info("回到指定流程:{}, 并执行流程初始化工作", manager.getName());
                DebugLogUtils.commonDebugLog(eventContext, String.format("跳转到指定流程:%s", manager.getStepName()), false);
                return doStepProcess(sessionContext, eventContext, manager);
            case HANG_UP:
                log.warn("对话处理异常, 不应该在对话引擎中执行挂机操作");
                // 不会处理到挂机操作, 挂机由交互层处理了
                return Flux.empty();
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "未处理的新跳转动作类型");
        }
    }

    private Flux<ChatResponse> execJumpAction(SessionContext sessionContext, EventContext eventContext, JumpAction action) {
        ChatEventTypeEnum originEvent = eventContext.getEvent();
        eventContext.setEvent(ChatEventTypeEnum.ENTER);
        log.info("跳转进入新的流程, 把事件类型改为ENTER, originEvent={}, action:{}", originEvent, action);
        return doExecJumpAction(sessionContext, eventContext, action)
                .doOnTerminate(() -> {
                    eventContext.setEvent(originEvent);
                });
    }

    private Flux<ChatResponse> doStepProcess(SessionContext sessionContext, EventContext eventContext, AbstractStepFlowChatManager manager) {
        return manager.process(sessionContext, eventContext);
    }

    private boolean needJumpStep(ChatResponse chatResponse) {
        return CollectionUtils.isNotEmpty(ActionHelper.getJumpActionList(chatResponse.getActionList()));
    }

    private Flux<ChatResponse> execCurrentStep(SessionContext sessionContext, EventContext eventContext) {
        switch (eventContext.getEvent()) {
            case ENTER:
                return enter(sessionContext, eventContext);
            case AI_SAY_FINISH:
                return aiSayFinish(sessionContext, eventContext);
            case USER_SAY_FINISH:
                return userSayFinish(sessionContext, eventContext);
            case USER_SILENCE:
                return userSilence(sessionContext, eventContext);
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
                return keyCapture(sessionContext, eventContext);
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "未处理的事件");
        }
    }

    private Flux<ChatResponse> keyCapture(SessionContext sessionContext, EventContext eventContext) {
        return getCurrentActiveStepManager(sessionContext).process(sessionContext, eventContext);
    }

    private Flux<ChatResponse> userSilence(SessionContext sessionContext, EventContext eventContext) {
        return getCurrentActiveStepManager(sessionContext).process(sessionContext, eventContext);
    }

    private Flux<ChatResponse> userSayFinish(SessionContext sessionContext, EventContext eventContext) {
        Optional<StepRuntime> matchStep = checkMatchNewStep(eventContext);
        if (matchStep.isPresent()) {
            // 命中新的流程
            return matchNewStep(sessionContext, eventContext, matchStep.get());
        } else if (SessionContextHelper.isHangupDelayMode(sessionContext)) {
            log.info("当前处于延迟挂机阶段, 原流程不再响应用户输入");
            return Flux.empty();
        }
        // 由原节点继续处理
        return getCurrentActiveStepManager(sessionContext).process(sessionContext, eventContext);
    }

    private Flux<ChatResponse> matchNewStep(SessionContext sessionContext, EventContext eventContext, StepRuntime matchStep) {
        log.info("命中新的流程, 意图id={}, 流程={}", eventContext.getMatchIntentId(), matchStep.getName());
        // todo 通过意图触发流程, 这里其实就是重新进入一个流程, 需要从根节点重新开始播放
        // 但是对于重复命中流程的时候, 和从问答知识进入到原流程的时候, 又需要特殊处理
        // 如果是通过意图触发到流程了, 那么流程的状态要不要清空呢?
        // 比如在主流程A中的某个节点之后触发到独立流程B了, 然后在独立流B的某个节点又通过意图触发到流程A了, 那么A流程是从根节点重新播放, 还是算回到原流程了?
        AbstractStepFlowChatManager manager = stepFlowChatManagerMap.get(matchStep.getId());
        // 只要不是连续触发, 都需要重新初始化状态
        DebugLogUtils.predictDetail(eventContext, eventContext.getPredictResult());
        DebugLogUtils.matchStepDetail(eventContext, matchStep);
        if (isSwitchStep(sessionContext, matchStep.getId())) {
            manager.initContext(sessionContext);
        }
        try {
            log.info("命中新的流程, 重新设置事件为ENTER");
            eventContext.setEvent(ChatEventTypeEnum.ENTER);
            return doStepProcess(sessionContext, eventContext, manager);
        } finally {
            eventContext.setEvent(ChatEventTypeEnum.USER_SAY_FINISH);
        }
    }

    /**
     * 是否切换流程了, 即上一次触发的流程和这次触发的流程不是同一个
     */
    private boolean isSwitchStep(SessionContext sessionContext, String nextStepId) {
        return !nextStepId.equals(sessionContext.getLastActiveStepId());
    }

    private Optional<StepRuntime> checkMatchNewStep(EventContext context) {
        String intentId = context.getMatchIntentId();
        PredictResult predictResult = context.getPredictResult();
        if (Objects.nonNull(predictResult)
                    && IntentRefTypeEnum.STEP.equals(predictResult.getIntentRefType())
                    && StringUtils.isNotBlank(intentId)
                    && resource.getIntent2StepMap().containsKey(intentId)) {
            return Optional.of(resource.getIntent2StepMap().get(intentId));
        }
        return Optional.empty();
    }

    private Flux<ChatResponse> aiSayFinish(SessionContext sessionContext, EventContext eventContext) {
        return getCurrentActiveStepManager(sessionContext).process(sessionContext, eventContext);
    }

    private Flux<ChatResponse> enter(SessionContext sessionContext, EventContext eventContext) {
        AbstractStepFlowChatManager stepFlowChatManager = stepFlowChatManagerMap.get(rootStepId);
        return stepFlowChatManager.process(sessionContext, eventContext);
    }

    private AbstractStepFlowChatManager getCurrentMainStepManager(SessionContext sessionContext, EventContext eventContext) {
        return stepFlowChatManagerMap.get(sessionContext.getLastActiveMainStepId());
    }

    private Optional<AbstractStepFlowChatManager> getNextMainStepManager(SessionContext sessionContext, EventContext eventContext) {
        return getNextMainStepId(mainStepIdList, sessionContext, eventContext)
                .map(stepFlowChatManagerMap::get);
    }

    private AbstractStepFlowChatManager getCurrentActiveStepManager(SessionContext sessionContext) {
        return stepFlowChatManagerMap.get(sessionContext.getLastActiveStepId());
    }

    private Optional<String> getNextMainStepId(List<String> mainStepIdList, SessionContext sessionContext, EventContext eventContext) {
        String preMainStepId = sessionContext.getLastActiveMainStepId();
        Set<String> repeatIdSet = sessionContext.getRepeatStepIdSet();
        Set<String> ignoreSet = new HashSet<>(repeatIdSet);
        ignoreSet.remove(preMainStepId);

        // 处理忽略的流程
        List<String> filteredList = mainStepIdList.stream()
                .filter(stepId -> !ignoreSet.contains(stepId))
                .collect(Collectors.toList());

        int preIndex = filteredList.indexOf(preMainStepId);
        Optional<String> nextStepId = filteredList.stream()
                .skip(preIndex + 1)
                .filter(stepId -> {
                    StepPO step = resource.getStepIdMap().get(stepId);
                    if (BooleanUtils.isNotTrue(step.getEnableSkipCondition()) || Objects.isNull(step.getSkipCondition())) {
                        return true;
                    }
                    StepSkipConditionPredicate predicate = new StepSkipConditionPredicate(resource, sessionContext);
                    boolean skip = predicate.test(step.getSkipCondition());
                    if (skip) {
                        log.info("流程跳过条件命中, 跳过流程, stepId={}, stepLabel:{}", stepId, step.getLabel());
                        DebugLogUtils.commonDebugLog(eventContext, String.format("流程%s-%s满足跳过设置配置条件，执行流程跳过", step.getLabel(), step.getName()));
                    }
                    return !skip;
                }).findFirst();

        if (!nextStepId.isPresent()) {
            log.info("数据异常, 没有下一个主流程, 直接返回空, preMainStepId={}, filteredList.size={}", preMainStepId, filteredList.size());
            return Optional.empty();
        }

        return nextStepId;
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        // 什么时候算触发到流程了呢?
        // 命中流程中的意图, 默认意图, 用户无应答
        List<ChatManagerTriggerCondition> result = new ArrayList<>();
        triggerByIntent(sessionContext, context).ifPresent(result::add);

        result.addAll(getCurrentActiveStepManager(sessionContext).getTriggerConditions(sessionContext, context));

        return result;
    }

    private Optional<ChatManagerTriggerCondition> triggerByIntent(SessionContext sessionContext, EventContext context) {
        if (MapUtils.isEmpty(resource.getIntent2StepMap())) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTENT_TRIGGER_STEP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(SessionContextHelper.getCurrentNodeExpectStepIntentIdSet(resource, sessionContext, context));
        condition.setIntentRefType(IntentRefTypeEnum.STEP);
        condition.setMustNotMatchModes(Collections.singleton(SpecialChatModeEnum.INAUDIBLE_REPEAT));
        return Optional.of(condition);
    }


}
