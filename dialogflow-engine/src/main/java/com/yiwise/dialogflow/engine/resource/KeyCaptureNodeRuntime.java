package com.yiwise.dialogflow.engine.resource;

import com.yiwise.dialogflow.entity.po.DialogKeyCaptureNodePO;
import com.yiwise.dialogflow.entity.po.StepPO;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
public class KeyCaptureNodeRuntime extends ChatNodeRuntime {

    /**
     * 重试次数
     */
    private final Integer retryTimes;

    /**
     * 按键采集结果存储的动态变量id
     */
    private final String resultVarId;

    /**
     * 可以触发的意图id列表
     */
    private final Set<String> canTriggerIntentIdSet;

    public KeyCaptureNodeRuntime(DialogKeyCaptureNodePO node, StepPO step, RobotRuntimeResource resource) {
        super(node, step, resource);
        if (BooleanUtils.isTrue(node.getEnableRetryOnFailed()) && Objects.nonNull(node.getRetryTimes())) {
            this.retryTimes = node.getRetryTimes();
        } else {
            this.retryTimes = 0;
        }
        this.resultVarId = node.getResultVarId();
        List<String> uninterruptedReplyKnowledgeIdList = node.getUninterruptedReplyKnowledgeIdList();
        List<String> uninterruptedReplyStepIdList = node.getUninterruptedReplyStepIdList();

        Set<String> canTriggerIntentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(uninterruptedReplyKnowledgeIdList)) {
            canTriggerIntentIdSet.addAll(
                    uninterruptedReplyKnowledgeIdList.stream()
                            .map(resource.getKnowledgeIdMap()::get)
                            .filter(Objects::nonNull)
                            .map(KnowledgeRuntime::getTriggerIntentIdList)
                            .filter(CollectionUtils::isNotEmpty)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toSet())
            );
        }
        if (CollectionUtils.isNotEmpty(uninterruptedReplyStepIdList)) {
            canTriggerIntentIdSet.addAll(
                    uninterruptedReplyStepIdList.stream()
                            .map(resource.getStepIdMap()::get)
                            .filter(Objects::nonNull)
                            .map(StepRuntime::getTriggerIntentIdList)
                            .filter(CollectionUtils::isNotEmpty)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toSet())
            );
        }
        this.canTriggerIntentIdSet = canTriggerIntentIdSet;
    }
}