package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class IntentActionRuntimeResource {

    /**
     * 节点id关联的短信模板id
     */
    ImmutableMap<String, Set<Long>> nodeId2SmsIdMap;

    /**
     * 问答知识id关联的短信模板id
     */
    ImmutableMap<String, Set<Long>> knowledgeId2SmsIdMap;

    /**
     * 节点id关联的黑名单分组id
     */
    ImmutableMap<String, Set<Long>> nodeId2WhiteGroupIdMap;

    /**
     * 配置了加微的节点id列表
     */
    ImmutableList<String> addWechatNodeIdList;

    /**
     * 问答知识id关联的黑名单分组id
     */
    ImmutableMap<String, Set<Long>> knowledgeId2WhiteGroupIdMap;

    /**
     * 节点id关联的标签id
     */
    ImmutableMap<String, Set<Long>> nodeId2tagIdMap;

    /**
     * 知识id关联的标签id
     */
    ImmutableMap<String, Set<Long>> knowledgeId2tagIdMap;

    /**
     * 配置了加微的知识id列表
     */
    ImmutableList<String> addWechatKnowledgeIdList;

    /**
     * 特殊语境id关联的黑名单分组id
     */
    ImmutableMap<String, Set<Long>> specialAnswerId2WhiteGroupIdMap;
    /**
     * 特殊语境id关联的sms id
     */
    ImmutableMap<String, Set<Long>> specialAnswerId2SmsIdMap;
    /**
     * 特殊语境id关联的tag id
     */
    ImmutableMap<String, Set<Long>> specialAnswerId2TagIdMap;

    /**
     * 动作配置信息
     */
    ImmutableList<IntentRuleActionPO> intentRuleActionList;
}
