package com.yiwise.dialogflow.engine.context;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnswerInterruptConfig {
    /**
     * 当前答案是否不可打断
     */
    Boolean uninterrupted;

    /**
     * 当前答案打断比例
     */
    Integer customInterruptThreshold;

    /**
     * 在不可打断时, 尝试是否可以命中允许打断的问答知识/流程, 1.4上线的需求
     */
    Boolean needTryReplyOnUninterrupted;
}
