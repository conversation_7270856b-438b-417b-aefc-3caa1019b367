package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.resource.RobotRuntimeResourceFactory;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
public class ChatEngineFactory {

    public static Mono<ChatEngine> asyncCreateEngine(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return RobotRuntimeResourceFactory.asyncGetRuntimeResource(botId, usageTarget, version)
                .map(resource ->  new DefaultChatEngine(botId, usageTarget, resource));
    }
}
