package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.entity.vo.batchtest.EngineBatchTestRequestVO;
import com.yiwise.dialogflow.entity.vo.batchtest.EngineBatchTestResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface EngineBatchTestService {

    EngineBatchTestResultVO batchTest(EngineBatchTestRequestVO request);


    EngineBatchTestResultVO batchTestFromFile(MultipartFile file);

    List<EngineBatchTestResultVO> batchTestFromCsv(Long botId, MultipartFile file);
}
