package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.*;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.thread.ApplicationExecutorHolder;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.IntentPredictRequiredResourceLoader;
import com.yiwise.dialogflow.engine.share.action.WaitAction;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.enums.llm.RagDocumentStatusEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.po.intent.*;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import com.yiwise.dialogflow.service.StepNodeService;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import com.yiwise.dialogflow.service.entitycollect.EntityPreprocessService;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
class DefaultRobotResourceLoader implements RobotResourceLoader {

    private static final RobotSnapshotService robotSnapshotService = AppContextUtils.getBean(RobotSnapshotService.class);
    private static final StepNodeService stepNodeService = AppContextUtils.getBean(StepNodeService.class);
    private static final BotService botService = AppContextUtils.getBean(BotService.class);
    private static final IntentLevelTagDetailService intentLevelTagDetailService = AppContextUtils.getBean(IntentLevelTagDetailService.class);
    private static final IntentRuleService intentRuleService = AppContextUtils.getBean(IntentRuleService.class);
    private static final AsrErrorCorrectionService asrErrorCorrectionService = AppContextUtils.getBean(AsrErrorCorrectionService.class);

    private static final EntityPreprocessService entityPreprocessService = AppContextUtils.getBean(EntityPreprocessService.class);

    private static final EntityService entityService = AppContextUtils.getBean(EntityService.class);

    @Override
    public Optional<RobotRuntimeResource> load(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        RobotSnapshotPO snapshot = robotSnapshotService.getByVersion(botId, usageTarget, version);
        if (Objects.isNull(snapshot)) {
            log.warn("加载话术快照失败, botId={}, version={}", botId, version);
            return Optional.empty();
        }
        BotPO botPO = botService.selectByKey(botId);
        if (Objects.isNull(botPO)) {
            log.warn("bot不存在，botId={}", botId);
            return Optional.empty();
        }
        return Optional.of(prepareRuntimeResource(botPO, snapshot, usageTarget));
    }

    private RobotRuntimeResource prepareRuntimeResource(BotPO bot, RobotSnapshotPO snapshot, RobotSnapshotUsageTargetEnum usageTarget) {
        Long botId = bot.getBotId();
        Integer version = snapshot.getVersion();
        // 创建的快照理论上都是经过校验的, 在数据和结构上都应该不会出错的,
        // 任何在这个构造运行时资源阶段需要校验的, 都可以提前到创建快照阶段进行校验
        RobotRuntimeResource resource = new RobotRuntimeResource();

        resource.setBotId(bot.getBotId());
        resource.setType(bot.getType());
        resource.setMagicTemplateId(bot.getMagicTemplateId());
        resource.setVersion(snapshot.getVersion());
        resource.setUsageTarget(usageTarget);

        resource.setName(bot.getName());

        // 基础信息
        resource.setAudioType(snapshot.getBotConfig().getAudioConfig().getAudioType());

        // 延迟挂机设置
        prepareHangupDelayConfig(snapshot, resource);

        // 准备录音
        prepareAudio(snapshot, resource);

        // 意图
        prepareIntent(snapshot, resource);

        // bot设置
        prepareBotConfig(snapshot, resource);

        // 实体
        prepareEntity(snapshot, resource);

        // 变量
        prepareVariable(snapshot, resource);

        // 关注点
        prepareCustomerFocus(snapshot, resource);

        // asr纠错
        prepareAsrCorrection(snapshot, resource);

        Map<String, AnswerLocateBO> locateMap = new HashMap<>();
        Map<String, BaseAnswerContent> answerMap = new HashMap<>();

        // 意向等级规则
        prepareIntentRule(snapshot, resource);
        //动作配置
        prepareIntentRuleAction(snapshot, resource);
        prepareIntentLevelCode(snapshot, resource);

        // 问答知识
        prepareKnowledge(snapshot, resource, locateMap, answerMap);

        // 特殊语境配置
        prepareSpecialAnswer(snapshot, resource, locateMap, answerMap);

        // 流程和节点
        prepareStep(snapshot, resource, locateMap, answerMap);

        // 答案文本
        resource.setAnswerMap(ImmutableMap.copyOf(answerMap));
        resource.setAnswerId2LocateMap(ImmutableMap.copyOf(locateMap));

        // 准备全局采集
        prepareGlobalCollectConfig(snapshot, resource);

        // 算法标签
        prepareAlgorithmLabel(snapshot, resource);

        // 文档知识
        prepareRagDocument(snapshot, resource);

        // 大模型分类
        prepareLlmLabel(snapshot, resource);

        resource.setResourceId2NameBO(intentRuleService.getResourceNameMap(
                snapshot.getStepList(),
                snapshot.getNodeList(),
                snapshot.getSpecialAnswerConfigList(),
                snapshot.getKnowledgeList(),
                snapshot.getIntentLevelDetailCode2NameMap(),
                snapshot.getEntityList(),
                snapshot.getIntentList(),
                snapshot.getVariableList(),
                snapshot.getLlmLabelList()
        ));
        return resource;
    }

    private void prepareLlmLabel(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        Map<String, LlmLabelPO> llmLabelMap = MyCollectionUtils.listToMap(snapshot.getLlmLabelList(), LlmLabelPO::getId);

        List<String> usedLlmTagList = new ArrayList<>();
        List<LlmLabelPO> usedLlmLabelList = new ArrayList<>();

        Consumer<IntentRuleConditionPO> consumer = condition -> {
            if (DialogFlowConditionTypeEnum.LLM_CUSTOM_TAG.equals(condition.getType()) && CollectionUtils.isNotEmpty(condition.getDescList())) {
                usedLlmTagList.addAll(condition.getDescList());
            }
            if (DialogFlowConditionTypeEnum.LLM_BUILT_IN_TAG.equals(condition.getType()) && CollectionUtils.isNotEmpty(condition.getDescList())) {
                usedLlmTagList.addAll(condition.getDescList());
            }
            if (DialogFlowConditionTypeEnum.LLM_LABEL.equals(condition.getType()) && llmLabelMap.containsKey(condition.getLlmLabelId())) {
                usedLlmLabelList.add(llmLabelMap.get(condition.getLlmLabelId()));
            }
        };

        List<IntentRulePO> intentRuleList = snapshot.getIntentRuleList();
        if (CollectionUtils.isNotEmpty(intentRuleList)) {
            for (IntentRulePO intentRule : intentRuleList) {
                intentRule.getConditionList().forEach(consumer);
            }
        }
        List<IntentRuleActionPO> intentRuleActionList = snapshot.getIntentRuleActionList();
        if (CollectionUtils.isNotEmpty(intentRuleActionList)) {
            for (IntentRuleActionPO intentRuleAction : intentRuleActionList) {
                intentRuleAction.getConditionList().forEach(consumer);
            }
        }
        resource.setUsedLlmLabelList(ImmutableList.copyOf(usedLlmLabelList.stream().distinct().collect(Collectors.toList())));
        resource.setUsedLlmTagList(ImmutableList.copyOf(usedLlmTagList.stream().distinct().collect(Collectors.toList())));
    }

    private void prepareRagDocument(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        List<RagDocumentPO> ragDocumentList = snapshot.getRagDocumentList();
        if (CollectionUtils.isEmpty(ragDocumentList)) {
            resource.setUsedRagDocumentIdList(ImmutableList.copyOf(Collections.emptyList()));
        } else {
            List<String> docIdList = ragDocumentList.stream().filter(doc -> RagDocumentStatusEnum.isSuccess(doc.getStatus()) && EnabledStatusEnum.ENABLE.equals(doc.getEnabledStatus()))
                    .map(RagDocumentPO::getId).collect(Collectors.toList());
            resource.setUsedRagDocumentIdList(ImmutableList.copyOf(docIdList));
        }
    }

    /**
     * 准备全局采集配置
     */
    private void prepareGlobalCollectConfig(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        // 全局提取
        EntityCollectConfigPO entityCollectConfig = snapshot.getEntityCollectConfig();
        List<GlobalCollectConfig> globalCollectConfigs = new ArrayList<>();

        // 实体的全局采集
        if (Objects.nonNull(entityCollectConfig) && BooleanUtils.isTrue(entityCollectConfig.getEnableGlobalCollect())) {
            for (EntityCollectConfigPO.EntityVariableMapping mapping : entityCollectConfig.getEntityVariableMappingList()) {
                GlobalCollectConfig globalCollectConfig = new GlobalCollectConfig();
                globalCollectConfig.setEntityId(mapping.getEntityId());
                globalCollectConfig.setVariableId(mapping.getVariableId());
                globalCollectConfig.setEnableAssign(true);
                globalCollectConfig.setSourceLabel("全局采集");
                globalCollectConfigs.add(globalCollectConfig);
            }
        }

        // 采集节点开启了读取之前通话内容
        resource.getNodeIdMap().forEach((nodeId, nodeRuntime) -> {
            if (nodeRuntime instanceof CollectNodeRuntime) {
                CollectNodeRuntime collectNodeRuntime = (CollectNodeRuntime) nodeRuntime;
                if (BooleanUtils.isTrue(collectNodeRuntime.getOrigin().getEnableCollectWithPreInput())) {

                    // 解析实体配置
                    for (CollectNodeEntityItemPO entityCollect : collectNodeRuntime.getOrigin().getEntityCollectList()) {
                        GlobalCollectConfig globalCollectConfig = new GlobalCollectConfig();
                        globalCollectConfig.setEntityId(entityCollect.getEntityId());
                        globalCollectConfig.setVariableId(entityCollect.getVariableId());
                        globalCollectConfig.setSourceLabel(nodeRuntime.origin.getLabel());
                        globalCollectConfig.setSourceId(nodeRuntime.getId());
                        globalCollectConfigs.add(globalCollectConfig);
                    }
                }
            }
        });

        log.info("全局采集配置:{}", globalCollectConfigs);

        resource.setGlobalCollectList(ImmutableList.copyOf(globalCollectConfigs));
    }

    private void prepareAlgorithmLabel(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        List<AlgorithmLabelPO> algorithmLabelList = snapshot.getAlgorithmLabelList();
        if (CollectionUtils.isEmpty(algorithmLabelList)) {
            resource.setNodeIdAlgorithmLabelMap(ImmutableMap.copyOf(Collections.emptyMap()));
        } else {
            Map<String, String> map = MyCollectionUtils.listToMap(algorithmLabelList, AlgorithmLabelPO::getNodeId, AlgorithmLabelPO::getLabel);
            resource.setNodeIdAlgorithmLabelMap(ImmutableMap.copyOf(map));
        }
    }

    @Override
    public Mono<RobotRuntimeResource> asyncLoad(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        Mono<BotPO> monoBot = Mono.defer(() -> {
            BotPO bot = botService.selectByKey(botId);
            return Mono.justOrEmpty(bot);
        }).subscribeOn(Schedulers.elastic());
        return Mono.zip(monoBot, robotSnapshotService.asyncGetByVersion(botId, usageTarget, version))
                .map(tuple -> prepareRuntimeResource(tuple.getT1(), tuple.getT2(), usageTarget));
    }

    private boolean containsAlgorithmPredictCondition(IntentLevelRuleResource intentLevelRuleResource, DialogFlowConditionTypeEnum targetType) {
        return intentLevelRuleResource.getIntentRuleList().stream()
                .filter(item -> Objects.isNull(item.getSpecialIntentRuleType()))
                .filter(rule -> CollectionUtils.isNotEmpty(rule.getConditionList()))
                .anyMatch(rule -> {
                    for (IntentRuleConditionPO condition : rule.getConditionList()) {
                        if (targetType.equals(condition.getType())) {
                            return true;
                        }
                    }
                    return false;
                });
    }

    private void prepareAsrCorrection(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        AsrErrorCorrectionDetailPO asrErrorCorrectionDetail = snapshot.getAsrErrorCorrectionDetail();
        if (Objects.isNull(asrErrorCorrectionDetail)) {
            String asrCorrectionDetailId = snapshot.getBot().getAsrErrorCorrectionDetailId();
            if (StringUtils.isNotBlank(asrCorrectionDetailId)) {
                asrErrorCorrectionDetail = asrErrorCorrectionService.get(asrCorrectionDetailId);
            }
        }
        AsrCorrectionRuntimeConfig config = new AsrCorrectionRuntimeConfig();
        config.setAsrCorrectionWhiteList(ImmutableSet.of());
        if (Objects.nonNull(asrErrorCorrectionDetail)) {
            config.setAsrErrorCorrectionDetailId(asrErrorCorrectionDetail.getAsrErrorCorrectionDetailId());
            if (CollectionUtils.isNotEmpty(asrErrorCorrectionDetail.getWhiteList())) {
                config.setAsrCorrectionWhiteList(ImmutableSet.copyOf(asrErrorCorrectionDetail.getWhiteList()));
            }
            config.setModelId(asrErrorCorrectionDetail.getModelId());
            config.setThreshold(asrErrorCorrectionDetail.getThreshold());
        }
        resource.setAsrCorrectionRuntimeConfig(config);
    }

    private void prepareBotConfig(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        BotConfigPO config = snapshot.getBotConfig();
        BotConfigRuntime botConfigRuntime = new BotConfigRuntime();
        if (Objects.nonNull(config) && Objects.nonNull(config.getSpeechConfig())) {
            botConfigRuntime.setLlmGuideAnswerConfig(config.getSpeechConfig().getLlmGuideAnswerConfig());
            if (CollectionUtils.isNotEmpty(config.getSpeechConfig().getToneWordList())) {
                botConfigRuntime.setToneWordSet(ImmutableSet.copyOf(config.getSpeechConfig().getToneWordList()));
            } else {
                botConfigRuntime.setToneWordSet(ImmutableSet.of());
            }
            botConfigRuntime.setEnableToneInterrupt(BooleanUtils.isTrue(config.getSpeechConfig().getEnableToneInterrupt()));
            if (Objects.nonNull(config.getSpeechConfig().getToneInterruptPercent())) {
                botConfigRuntime.setToneInterruptPercent(config.getSpeechConfig().getToneInterruptPercent());
            } else {
                log.warn("话术快照中的话术配置中的语音配置中的话术中断百分比为空, 默认设置为100, botId={}, version={}", resource.getBotId(), resource.getVersion());
                botConfigRuntime.setToneInterruptPercent(100);
            }
            if (Objects.nonNull(config.getSpeechConfig().getUserSilenceThreshold())) {
                botConfigRuntime.setUserSilenceMs((int)(config.getSpeechConfig().getUserSilenceThreshold() * 1000));
            }
            botConfigRuntime.setEnableGenerateAnswerReplace(BooleanUtils.isTrue(config.getSpeechConfig().getEnableGenerateAnswerReplace()));
            botConfigRuntime.setGenerateAnswerReplaceList(config.getSpeechConfig().getGenerateAnswerReplaceList());
        }
        resource.setBotConfig(botConfigRuntime);
    }

    private void prepareCustomerFocus(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        Table<AnswerSourceEnum,String,String> customerFocusTable = HashBasedTable.create();
        snapshot.getStepList().stream().filter(s -> BooleanUtils.isTrue(s.getIsCustomerConcern())).forEach(s -> customerFocusTable.put(AnswerSourceEnum.STEP, s.getId(),s.getName()));
        snapshot.getKnowledgeList().stream().filter(s -> BooleanUtils.isTrue(s.getIsCustomerConcern())).forEach(s -> customerFocusTable.put(AnswerSourceEnum.KNOWLEDGE, s.getId(),s.getName()));
        snapshot.getSpecialAnswerConfigList().stream().filter(s -> BooleanUtils.isTrue(s.getIsCustomerConcern())).forEach(s -> customerFocusTable.put(AnswerSourceEnum.SPECIAL_ANSWER, s.getId(),s.getName()));
        resource.setCustomerFocusTable(ImmutableTable.copyOf(customerFocusTable));
    }

    private void prepareHangupDelayConfig(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        SpecialAnswerConfigPO hangupConfig = snapshot.getSpecialAnswerConfigList().stream()
                .filter(item -> SpecialAnswerConfigPO.HANGUP_DELAY.equals(item.getName()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(hangupConfig) || !EnabledStatusEnum.ENABLE.equals(hangupConfig.getEnabledStatus())) {
            resource.setHangupDelay(false);
            resource.setHangupDelayMs(0);
        } else {
            if (Objects.isNull(hangupConfig.getHangupDelaySeconds())) {
                log.warn("挂机延迟设置异常, 延迟时间为空, 设置延迟开关为关闭");
                resource.setHangupDelay(false);
                resource.setHangupDelayMs(0);
            } else {
                resource.setHangupDelay(true);
                resource.setHangupDelayMs((int) (hangupConfig.getHangupDelaySeconds() * 1000));
            }
        }
    }

    private boolean containsIntentHitRule(List<IntentRuleConditionPO> conditionList) {
        if (CollectionUtils.isEmpty(conditionList)) {
            return false;
        }
        return conditionList.stream().anyMatch(
                condition -> DialogFlowConditionTypeEnum.HIT_INTENT.equals(condition.getType()));
    }

    private boolean containsLlmLabelRule(List<IntentRuleConditionPO> conditionList) {
        if (CollectionUtils.isEmpty(conditionList)) {
            return false;
        }
        return conditionList.stream().anyMatch(condition ->
                DialogFlowConditionTypeEnum.LLM_LABEL.equals(condition.getType()) ||
                        DialogFlowConditionTypeEnum.LLM_BUILT_IN_TAG.equals(condition.getType()) ||
                        DialogFlowConditionTypeEnum.LLM_CUSTOM_TAG.equals(condition.getType())
        );
    }

    private void prepareIntentRuleAction(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        if (CollectionUtils.isEmpty(snapshot.getIntentRuleActionList())) {
            resource.getIntentActionResource().setIntentRuleActionList(ImmutableList.copyOf(Collections.emptyList()));
            return;
        }
        List<IntentRuleActionPO> actionList = snapshot.getIntentRuleActionList();
        actionList.forEach(action -> {
            if (CollectionUtils.isNotEmpty(action.getConditionList())) {
                action.getConditionList().forEach(condition -> {
                    if (CollectionUtils.isNotEmpty(condition.getKeywords())) {
                        condition.setKeywordPatterns(generatePatterns(snapshot.getBotId(), condition.getKeywords()));
                    }
                });
                if (BooleanUtils.isNotTrue(resource.getContainsIntentHitRule())) {
                    resource.setContainsIntentHitRule(containsIntentHitRule(action.getConditionList()));
                }
                if (BooleanUtils.isNotTrue(resource.getContainsLlmLabelRule())) {
                    resource.setContainsLlmLabelRule(containsLlmLabelRule(action.getConditionList()));
                }
            }
        });
        resource.getIntentActionResource().setIntentRuleActionList(ImmutableList.copyOf(actionList));
    }

    private void prepareVariable(RobotSnapshotPO snapshot, RobotRuntimeResource resource){
        resource.setVariableIdMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(snapshot.getVariableList(), VariablePO::getId)));
        if (Objects.isNull(snapshot.getPlayableVariableNameSet())) {
            resource.setPlayableDynamicVariableNameSet(ImmutableSet.copyOf(Collections.emptySet()));
        } else {
            Set<String> dynamicVariableNameSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(snapshot.getVariableList())) {
                snapshot.getVariableList().forEach(variable -> {
                    if (VariableTypeEnum.isDynamicVariable(variable.getType())) {
                        dynamicVariableNameSet.add(variable.getName());
                    }
                });
            }
            dynamicVariableNameSet.retainAll(snapshot.getPlayableVariableNameSet());
            resource.setPlayableDynamicVariableNameSet(ImmutableSet.copyOf(dynamicVariableNameSet));
        }
        if (Objects.isNull(snapshot.getUsedVariableNameSet())) {
            resource.setUsedVariableNameSet(ImmutableSet.copyOf(Collections.emptySet()));
        } else {
            resource.setUsedVariableNameSet(ImmutableSet.copyOf(snapshot.getUsedVariableNameSet()));
        }
        if (Objects.isNull(snapshot.getTemplateVarNameValueMap())) {
            resource.setTemplateVarNameValueMap(ImmutableMap.copyOf(Collections.emptyMap()));
        } else {
            resource.setTemplateVarNameValueMap(ImmutableMap.copyOf(snapshot.getTemplateVarNameValueMap()));
        }
    }

    private void prepareIntentRule(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        if (CollectionUtils.isEmpty(snapshot.getIntentRuleList())) {
            log.warn("数据异常, 意向等级规则为空, botId={}", snapshot.getBotId());
            resource.getIntentLevelRuleResource().setIntentRuleList(ImmutableList.copyOf(Collections.emptyList()));
            return;
        }
        List<IntentRulePO> poList = snapshot.getIntentRuleList();
        poList.forEach(rule -> {
            // 加载快照时修复内置意向等级中通话状态的数据
            intentRuleService.resetBuildInRuleConditionList(rule);
            if (CollectionUtils.isNotEmpty(rule.getConditionList())) {
                rule.getConditionList().forEach(condition -> {
                    if (CollectionUtils.isNotEmpty(condition.getKeywords())) {
                        condition.setKeywordPatterns(generatePatterns(snapshot.getBotId(), condition.getKeywords()));
                    }
                });
                if (BooleanUtils.isNotTrue(resource.getContainsIntentHitRule())) {
                    resource.setContainsIntentHitRule(containsIntentHitRule(rule.getConditionList()));
                }
                if (BooleanUtils.isNotTrue(resource.getContainsLlmLabelRule())) {
                    resource.setContainsLlmLabelRule(containsLlmLabelRule(rule.getConditionList()));
                }
            }
        });

        resource.getIntentLevelRuleResource().setIntentRuleList(ImmutableList.copyOf(poList));
        IntentLevelRuleResource intentLevelRuleResource = resource.getIntentLevelRuleResource();
        intentLevelRuleResource.setHasActivityCondition(containsAlgorithmPredictCondition(intentLevelRuleResource, DialogFlowConditionTypeEnum.ALGORITHM_ACTIVITY_INTENT_LEVEL));
        intentLevelRuleResource.setHasPrivateDomainCondition(containsAlgorithmPredictCondition(intentLevelRuleResource, DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL));
        intentLevelRuleResource.setHasPassivePrivateDomainCondition(containsAlgorithmPredictCondition(intentLevelRuleResource, DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE));
        intentLevelRuleResource.setHasEducationActivityCondition(containsAlgorithmPredictCondition(intentLevelRuleResource, DialogFlowConditionTypeEnum.ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL));
    }

    private List<PatternEnhance> generatePatterns(Long botId, List<String> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return Collections.emptyList();
        }
        List<PatternEnhance> patterns = new ArrayList<>();
        for (String keyword : keywords) {
            try {
                Pattern pattern = PatternCache.compile(keyword);
                PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(keyword, pattern, false);
                patterns.add(patternEnhance);
            } catch (Exception e) {
                log.error("[LogHub_Warn] 包含条件正则表达式解析出错, botId:{}, keyword={}", botId, keyword);
            }
        }
        return patterns;
    }

    private void prepareIntentLevelCode(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        // 需要从engine-web加载最新的
        if (MapUtils.isNotEmpty(snapshot.getIntentLevelDetailCode2NameMap())) {
            resource.getIntentLevelRuleResource().setIntentLevelCode2NameMap(ImmutableMap.copyOf(snapshot.getIntentLevelDetailCode2NameMap()));
        } else {
            resource.getIntentLevelRuleResource().setIntentLevelCode2NameMap(ImmutableMap.copyOf(Collections.emptyMap()));
        }
        // 异步加载最新的意向等级code到名称得映射
        ApplicationExecutorHolder.execute("异步加载意向等级名称详情", () -> {
            try {
                BotPO bot = snapshot.getBot();
                Map<Integer, String> intentLevelCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(bot.getIntentLevelTagId());
                Map<Integer, String> oldIntentLevelCode2NameMap = resource.getIntentLevelRuleResource().getIntentLevelCode2NameMap();
                if (MapUtils.isNotEmpty(intentLevelCode2NameMap)) {
                    resource.getIntentLevelRuleResource().setIntentLevelCode2NameMap(ImmutableMap.copyOf(intentLevelCode2NameMap));
                    log.info("异步更新意向等级名称, 原数据:{}, 最新数据:{}", oldIntentLevelCode2NameMap, intentLevelCode2NameMap);
                }
            } catch (Exception e) {
                log.warn("异步加载意向等级代码到名称映射失败, botId={}", snapshot.getBotId(), e);
            }
        });
    }

    private static void prepareLLMStepAnswer(RobotSnapshotPO snapshot,
                                             RobotRuntimeResource resource,
                                             LLMStepRuntime llmStepRuntime,
                                             Map<String, AnswerLocateBO> locateMap,
                                             Map<String, BaseAnswerContent> answerMap) {
        // 使用大模型特殊语境的承接话术, 作为大模型流程的承接话术, 且回答后操作均为等待用户应答
        Optional<SpecialAnswerConfigPO> llmSpecialAnswerOpt = snapshot.getSpecialAnswerConfigList()
                .stream()
                .filter(item -> SpecialAnswerConfigPO.LLM.equals(item.getName()))
                .findAny();

        Optional<SpecialAnswerConfigPO> aiUnknownSpecialAnswerOpt = snapshot.getSpecialAnswerConfigList()
                .stream()
                .filter(item -> SpecialAnswerConfigPO.AI_UNKNOWN.equals(item.getName()))
                .findAny();


        List<NodeAnswerRuntime> guideAnswerList = new ArrayList<>();
        List<NodeAnswerRuntime> unknownAnswerList = new ArrayList<>();

        if (llmSpecialAnswerOpt.isPresent()
                && EnabledStatusEnum.ENABLE.equals(llmSpecialAnswerOpt.get().getEnabledStatus())
                && BooleanUtils.isTrue(llmSpecialAnswerOpt.get().getEnableGuideAnswer())
                && CollectionUtils.isNotEmpty(llmSpecialAnswerOpt.get().getAnswerList())) {
            SpecialAnswerConfigPO llmSpecialAnswer = llmSpecialAnswerOpt.get();
            AtomicInteger index = new AtomicInteger();
            for (int j = 0; j < 10; j++) {
                for (int i = 0; i < llmSpecialAnswer.getAnswerList().size(); i++) {
                    KnowledgeAnswer answer = llmSpecialAnswer.getAnswerList().get(i);
                    NodeAnswer nodeAnswer = MyBeanUtils.copy(answer, NodeAnswer.class);
                    nodeAnswer.setLabel(llmStepRuntime.getLabel() + "_" + nodeAnswer.getLabel() + "_" + index.getAndIncrement());
                    AnswerLocateBO locate = new AnswerLocateBO();
                    locate.setAnswerSource(AnswerSourceEnum.LLM_STEP);
                    locate.setStepId(llmStepRuntime.getId());
                    locate.setStepName(llmStepRuntime.getName());
                    locate.setStepLabel(llmStepRuntime.getLabel());
                    locate.setAnswerId(nodeAnswer.getUniqueId());
                    locate.setAnswerLabel(nodeAnswer.getLabel());
                    locate.setIndex(i);
                    locateMap.put(locate.getAnswerId(), locate);
                    answerMap.put(nodeAnswer.getUniqueId(), nodeAnswer);
                    NodeAnswerRuntime nodeAnswerRuntime = new NodeAnswerRuntime(nodeAnswer, resource, locate);
                    guideAnswerList.add(nodeAnswerRuntime);
                }
            }

        } else {
            for (int i = 0; i < 10; i++) {
                NodeAnswer nodeAnswer = new NodeAnswer();
                nodeAnswer.setLabel(llmStepRuntime.getLabel() + "_" + i);
                nodeAnswer.setText("&停顿0.1秒&");
                AnswerLocateBO locate = new AnswerLocateBO();
                locate.setAnswerSource(AnswerSourceEnum.LLM_STEP);
                locate.setStepId(llmStepRuntime.getId());
                locate.setStepName(llmStepRuntime.getName());
                locate.setStepLabel(llmStepRuntime.getLabel());
                locate.setAnswerId(nodeAnswer.getUniqueId());
                locate.setAnswerLabel(nodeAnswer.getLabel());
                locate.setIndex(0);
                locateMap.put(locate.getAnswerId(), locate);
                answerMap.put(nodeAnswer.getUniqueId(), nodeAnswer);
                NodeAnswerRuntime nodeAnswerRuntime = new NodeAnswerRuntime(nodeAnswer, resource, locate);
                guideAnswerList.add(nodeAnswerRuntime);
            }
        }

        if (aiUnknownSpecialAnswerOpt.isPresent()
                && EnabledStatusEnum.ENABLE.equals(aiUnknownSpecialAnswerOpt.get().getEnabledStatus())
                && CollectionUtils.isNotEmpty(aiUnknownSpecialAnswerOpt.get().getAnswerList())) {
            SpecialAnswerConfigPO llmUnknownAnswerConfig = aiUnknownSpecialAnswerOpt.get();
            for (int i = 0; i < llmUnknownAnswerConfig.getAnswerList().size(); i++) {
                KnowledgeAnswer answer = llmUnknownAnswerConfig.getAnswerList().get(i);
                NodeAnswer nodeAnswer = MyBeanUtils.copy(answer, NodeAnswer.class);
                nodeAnswer.setLabel(llmStepRuntime.getLabel() + "_" + nodeAnswer.getLabel());
                AnswerLocateBO locate = new AnswerLocateBO();

                locate.setAnswerSource(AnswerSourceEnum.LLM_STEP);
                locate.setStepId(llmStepRuntime.getId());
                locate.setStepName(llmStepRuntime.getName());
                locate.setStepLabel(llmStepRuntime.getLabel());
                locate.setAnswerId(nodeAnswer.getUniqueId());
                locate.setAnswerLabel(nodeAnswer.getLabel());
                locate.setIndex(i);
                locateMap.put(locate.getAnswerId(), locate);
                answerMap.put(nodeAnswer.getUniqueId(), nodeAnswer);
                NodeAnswerRuntime nodeAnswerRuntime = new NodeAnswerRuntime(nodeAnswer, resource, locate);
                unknownAnswerList.add(nodeAnswerRuntime);
            }
        }

        llmStepRuntime.setLlmFlowActionList(ImmutableList.of(new WaitAction(resource.getBotConfig().getUserSilenceMs())));

        llmStepRuntime.setLlmFlowGuideAnswerList(ImmutableList.copyOf(guideAnswerList));
        llmStepRuntime.setLlmFlowGuideAnswerMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(guideAnswerList, NodeAnswerRuntime::getUniqueId)));

        llmStepRuntime.setLlmFlowUnknownAnswerList(ImmutableList.copyOf(unknownAnswerList));
        llmStepRuntime.setLlmFlowUnknownAnswerMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(unknownAnswerList, NodeAnswerRuntime::getUniqueId)));
    }

    private void prepareAudio(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        Map<String, String> textAudioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(snapshot.getAnswerAudioMappingList())) {
            snapshot.getAnswerAudioMappingList().forEach(mapping -> {
                if (StringUtils.isNotBlank(mapping.getUrl())) {
                    textAudioMap.put(mapping.getText(), mapping.getUrl());
                }
            });
        }
        resource.setAnswerText2AudioUrlMap(ImmutableMap.copyOf(textAudioMap));
    }

    private void prepareSpecialAnswer(RobotSnapshotPO snapshot, RobotRuntimeResource resource,
                                      Map<String, AnswerLocateBO> locateMap,
                                      Map<String, BaseAnswerContent> answerMap) {
        Map<String, Integer> specialAnswerId2intentLevelCodeMap = new HashMap<>(16);
        Map<String, Set<Long>> specialAnswerId2SmsIdMap = new HashMap<>(16);
        Map<String, Set<Long>> specialAnswerId2TagIdMap = new HashMap<>(16);
        Map<String, Set<Long>> specialAnswerId2WhiteGroupIdMap = new HashMap<>(16);
        Map<String, ImmutableSet<String>> specialAnswerId2IntentIdSetMap = new HashMap<>(16);
        Map<String, SpecialAnswerRuntime> specialAnswerRuntimeIdMap = new HashMap<>(16);
        Map<String, SpecialAnswerRuntime> specialAnswerRuntimeNameMap = new HashMap<>(16);

        snapshot.getSpecialAnswerConfigList().forEach(specialAnswerConfig -> {
            List<KnowledgeAnswerRuntime> answerList = Collections.emptyList();
            if (EnabledStatusEnum.ENABLE.equals(specialAnswerConfig.getEnabledStatus())) {
                if (CollectionUtils.isNotEmpty(specialAnswerConfig.getTriggerIntentIdList())) {
                    specialAnswerId2IntentIdSetMap.put(specialAnswerConfig.getId(), ImmutableSet.copyOf(specialAnswerConfig.getTriggerIntentIdList()));
                }
                if (BooleanUtils.isTrue(specialAnswerConfig.getEnableIntentLevel())
                        && Objects.nonNull(specialAnswerConfig.getIntentLevelDetailCode())) {
                    specialAnswerId2intentLevelCodeMap.put(specialAnswerConfig.getId(), specialAnswerConfig.getIntentLevelDetailCode());
                }
                if (BooleanUtils.isTrue(specialAnswerConfig.getIsEnableAction())
                        && CollectionUtils.isNotEmpty(specialAnswerConfig.getActionList())) {

                    //发短信
                    composeActionMap(specialAnswerConfig.getActionList(), ActionCategoryEnum.SEND_SMS, specialAnswerId2SmsIdMap, specialAnswerConfig.getId());
                    //加黑
                    composeActionMap(specialAnswerConfig.getActionList(), ActionCategoryEnum.WHITE_LIST, specialAnswerId2WhiteGroupIdMap, specialAnswerConfig.getId());
                    //打标签
                    composeActionMap(specialAnswerConfig.getActionList(), ActionCategoryEnum.ADD_TAG, specialAnswerId2TagIdMap, specialAnswerConfig.getId());
                }
                answerList = prepareSpecialAnswerInfo(resource, locateMap, answerMap, specialAnswerConfig);
            }
            SpecialAnswerRuntime runtime = MyBeanUtils.copy(specialAnswerConfig, SpecialAnswerRuntime.class);

            runtime.setAnswerRuntimeList(ImmutableList.copyOf(answerList));
            runtime.setAnswerMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(answerList, KnowledgeAnswerRuntime::getUniqueId)));

            runtime.setUninterruptedOnlyReplyStepIdSet(UninterruptedRuntime.canReplyStepIdSet(specialAnswerConfig));
            runtime.setUninterruptedOnlyReplyKnowledgeIdSet(UninterruptedRuntime.canReplyKnowledgeIdSet(specialAnswerConfig));
            runtime.setUninterruptedOnlyReplySpecialAnswerIdSet(UninterruptedRuntime.canReplySpecialAnswerIdSet(specialAnswerConfig));

            specialAnswerRuntimeIdMap.put(runtime.getId(), runtime);
            specialAnswerRuntimeNameMap.put(runtime.getName(), runtime);
        });

        resource.setSpecialAnswerNameMap(ImmutableMap.copyOf(specialAnswerRuntimeNameMap));
        resource.setSpecialAnswerIdMap(ImmutableMap.copyOf(specialAnswerRuntimeIdMap));

        resource.getIntentLevelRuleResource().setSpecialAnswerId2IntentLevelCodeMap(ImmutableMap.copyOf(specialAnswerId2intentLevelCodeMap));
        resource.getIntentActionResource().setSpecialAnswerId2SmsIdMap(ImmutableMap.copyOf(specialAnswerId2SmsIdMap));
        resource.getIntentActionResource().setSpecialAnswerId2TagIdMap(ImmutableMap.copyOf(specialAnswerId2TagIdMap));
        resource.getIntentActionResource().setSpecialAnswerId2WhiteGroupIdMap(ImmutableMap.copyOf(specialAnswerId2WhiteGroupIdMap));
        resource.setSpecialAnswerTriggerIntentIdSetMap(ImmutableMap.copyOf(specialAnswerId2IntentIdSetMap));
    }

    private static List<KnowledgeAnswerRuntime> prepareSpecialAnswerInfo(RobotRuntimeResource resource,
                                                                         Map<String, AnswerLocateBO> locateMap,
                                                                         Map<String, BaseAnswerContent> answerMap,
                                                                         SpecialAnswerConfigPO specialAnswerConfig) {
        List<KnowledgeAnswerRuntime> result = new ArrayList<>();

        if (SpecialAnswerConfigPO.LLM.equals(specialAnswerConfig.getName())) {
            if (BooleanUtils.isTrue(specialAnswerConfig.getEnableGuideAnswer())
                    && CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                for (KnowledgeAnswer answer : specialAnswerConfig.getAnswerList()) {
                    AnswerLocateBO locate = AnswerLocateUtils.generate(specialAnswerConfig, answer);
                    locateMap.put(locate.getAnswerId(), locate);
                    answerMap.put(answer.getUniqueId(), answer);
                    result.add(new KnowledgeAnswerRuntime(answer, resource, locate));
                }
            } else {
                // 生成答案内容
                KnowledgeAnswer answer = new KnowledgeAnswer();
                answer.setText("&停顿0.1秒&");
                answer.setPostAction(PostActionTypeEnum.WAIT);
                answer.setLabel(specialAnswerConfig.getLabel() + "_llm");
                AnswerLocateBO locate = AnswerLocateUtils.generate(specialAnswerConfig, answer);
                locateMap.put(locate.getAnswerId(), locate);
                answerMap.put(answer.getUniqueId(), answer);
                result.add(new KnowledgeAnswerRuntime(answer, resource, locate));
            }
        } else {
            if (CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                for (KnowledgeAnswer answer : specialAnswerConfig.getAnswerList()) {
                    AnswerLocateBO locate = AnswerLocateUtils.generate(specialAnswerConfig, answer);
                    locateMap.put(locate.getAnswerId(), locate);
                    answerMap.put(answer.getUniqueId(), answer);
                    result.add(new KnowledgeAnswerRuntime(answer, resource, locate));
                }
            }
        }
        return result;
    }

    private void prepareStep(RobotSnapshotPO snapshot,
                             RobotRuntimeResource resource,
                             Map<String, AnswerLocateBO> locateMap,
                             Map<String, BaseAnswerContent> answerMap) {

        List<StepPO> stepList = snapshot.getStepList();
        List<DialogBaseNodePO> nodeList = snapshot.getNodeList();
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);

        // 把step转换成stepRuntime
        List<StepRuntime> stepRuntimeList = stepList.stream()
                .map(po -> {
                    if (StepSubTypeEnum.isLlm(po.getSubType())) {
                        return MyBeanUtils.copy(po, LLMStepRuntime.class);
                    } else {
                        return MyBeanUtils.copy(po, StepRuntime.class);
                    }
                })
                .collect(Collectors.toList());

        List<StepRuntime> mainStepList = new ArrayList<>();
        Map<String, StepRuntime> stepIdMap = new HashMap<>(16);
        Map<String, StepRuntime> intentStepMap = new HashMap<>(16);
        Map<String, ImmutableSet<String>> stepId2IntentIdMap = new HashMap<>(16);
        Map<String, Integer> nodeId2IntentLevelCodeMap = new HashMap<>();
        Map<String, Set<Long>> nodeId2TagIdMap = new HashMap<>();
        Map<String, Set<Long>> nodeId2SmsIdMap = new HashMap<>();
        Map<String, Set<Long>> nodeId2WhiteGroupIdMap = new HashMap<>();
        List<String> addWechatNodeIdList = new ArrayList<>();
        List<String> llmStepIdList = new ArrayList<>();
        for (StepRuntime step : stepRuntimeList) {
            if (StepTypeEnum.MAIN.equals(step.getType())) {
                mainStepList.add(step);
            }
            if (StepSubTypeEnum.isLlm(step.getSubType())) {
                llmStepIdList.add(step.getId());
            }
            stepIdMap.put(step.getId(), step);
            if (BooleanUtils.isTrue(step.getTriggerByIntent()) && CollectionUtils.isNotEmpty(step.getTriggerIntentIdList())) {
                for (String intentId : step.getTriggerIntentIdList()) {
                    intentStepMap.put(intentId, step);
                }
                stepId2IntentIdMap.put(step.getId(), ImmutableSet.copyOf(step.getTriggerIntentIdList()));
            }
            List<DialogBaseNodePO> stepNodeList = stepNodeListMap.get(step.getId());
            if (CollectionUtils.isNotEmpty(stepNodeList)) {
                for (DialogBaseNodePO node : stepNodeList) {
                    if (BooleanUtils.isTrue(node.getEnableIntentLevel()) && Objects.nonNull(node.getIntentLevelDetailCode())) {
                        nodeId2IntentLevelCodeMap.put(node.getId(), node.getIntentLevelDetailCode());
                    }
                    if (CollectionUtils.isNotEmpty(node.getActionList())) {
                        //发短信
                        composeActionMap(node.getActionList(), ActionCategoryEnum.SEND_SMS, nodeId2SmsIdMap, node.getId());
                        //加黑
                        composeActionMap(node.getActionList(), ActionCategoryEnum.WHITE_LIST, nodeId2WhiteGroupIdMap, node.getId());
                        //打标签
                        composeActionMap(node.getActionList(), ActionCategoryEnum.ADD_TAG, nodeId2TagIdMap, node.getId());
                        // 加微
                        if (ActionHelper.containsAddWechatAction(node.getActionList())) {
                            addWechatNodeIdList.add(node.getId());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                        for (NodeAnswer nodeAnswer : node.getAnswerList()) {
                            AnswerLocateBO locate = AnswerLocateUtils.generate(step, node, nodeAnswer);
                            locateMap.put(locate.getAnswerId(), locate);
                            answerMap.put(nodeAnswer.getUniqueId(), nodeAnswer);
                        }
                    }
                }
            }
        }

        mainStepList.sort(Comparator.comparingInt(StepPO::getOrderNum));
        Set<String> businessStepIdSet = stepList.stream()
                .filter(step -> StepCategoryEnum.BUSINESS.equals(step.getCategory()))
                .map(StepPO::getId)
                .collect(Collectors.toSet());

        resource.setLlmStepIdSet(ImmutableSet.copyOf(llmStepIdList));
        resource.setBusinessStepIdSet(ImmutableSet.copyOf(businessStepIdSet));
        resource.setMainStepList(ImmutableList.copyOf(mainStepList));
        resource.setStepIdMap(ImmutableMap.copyOf(stepIdMap));
        resource.setIntent2StepMap(ImmutableMap.copyOf(intentStepMap));
        resource.setStepTriggerIntentIdSetMap(ImmutableMap.copyOf(stepId2IntentIdMap));
        resource.getIntentLevelRuleResource().setNodeId2IntentLevelCodeMap(ImmutableMap.copyOf(nodeId2IntentLevelCodeMap));
        resource.getIntentActionResource().setNodeId2SmsIdMap(ImmutableMap.copyOf(nodeId2SmsIdMap));
        resource.getIntentActionResource().setNodeId2WhiteGroupIdMap(ImmutableMap.copyOf(nodeId2WhiteGroupIdMap));
        resource.getIntentActionResource().setNodeId2tagIdMap(ImmutableMap.copyOf(nodeId2TagIdMap));
        resource.getIntentActionResource().setAddWechatNodeIdList(ImmutableList.copyOf(addWechatNodeIdList));


        // 初始化节点和完善stepRuntime中的nodeMap和rootNode
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        List<NodeRuntime<?>> allNodeList = snapshot.getNodeList().stream()
                .map(node -> {
                    StepPO step = stepMap.get(node.getStepId());
                    if (node instanceof DialogCollectNodePO) {
                        return new CollectNodeRuntime((DialogCollectNodePO) node, step, resource);
                    } else if (node instanceof DialogQueryNodePO) {
                        return new QueryNodeRuntime((DialogQueryNodePO) node, step, resource);
                    } else if (node instanceof DialogKeyCaptureNodePO) {
                        return new KeyCaptureNodeRuntime((DialogKeyCaptureNodePO) node, step, resource);
                    } else if (node instanceof DialogChatNodePO) {
                        return new ChatNodeRuntime((DialogChatNodePO)node, step,  resource);
                    } else if (node instanceof DialogJudgeNodePO) {
                        return new JudgeNodeRuntime((DialogJudgeNodePO)node, step, resource);
                    } else if (node instanceof DialogJumpNodePO) {
                        return new JumpNodeRuntime((DialogJumpNodePO)node, step, resource);
                    } else {
                        throw new RuntimeException("未知的节点类型");
                    }
                })
                .collect(Collectors.toList());
        Map<String, NodeRuntime<?>> nodeIdMap = MyCollectionUtils.listToMap(allNodeList, NodeRuntime::getId);
        Map<String, List<NodeRuntime<?>>> stepNodeRuntimeListMap = MyCollectionUtils.listToMapList(allNodeList, NodeRuntime::getStepId);


        Map<String, LlmStepConfigPO> llmConfigMap = MyCollectionUtils.listToMap(snapshot.getLlmStepConfigList(), LlmStepConfigPO::getStepId);

        resource.getStepIdMap().forEach((stepId, stepRuntime) -> {
            initStepRuntime(stepNodeRuntimeListMap.getOrDefault(stepId, Collections.emptyList()), stepRuntime);

            // 处理大模型流程
            if (stepRuntime instanceof LLMStepRuntime) {
                LLMStepRuntime llmStepRuntime = (LLMStepRuntime) stepRuntime;
                prepareLLMStepAnswer(snapshot, resource, llmStepRuntime, locateMap, answerMap);
                LlmStepConfigPO llmStepConfig = llmConfigMap.get(stepId);
                if (Objects.isNull(llmStepConfig)) {
                    log.warn("[LogHub_Warn] 对话数据错误,bot:{}, stepId:{}, 未查询到对于的大模型配置信息", stepRuntime.getBotId(), stepId);
                    return;
                }
                llmStepRuntime.setOriginStepConfig(llmStepConfig);

                if (BooleanUtils.isTrue(llmStepRuntime.getOriginStepConfig().getEnableUserSilence())
                        && Objects.nonNull(llmStepRuntime.getOriginStepConfig().getCustomUserSilenceSecond())) {
                    double silenceMs = llmStepRuntime.getOriginStepConfig().getCustomUserSilenceSecond() * 1000;
                    llmStepRuntime.setLlmFlowActionList(ImmutableList.of(new WaitAction((int) silenceMs)));
                }

                llmStepRuntime.setMismatchKnowledgeAndStep(BooleanUtils.isTrue(llmStepConfig.getMismatchKnowledgeAndStep()));
                llmStepRuntime.setMismatchAllStep(BooleanUtils.isTrue(llmStepConfig.getMismatchAllStep()));
                llmStepRuntime.setMismatchAllKnowledge(BooleanUtils.isTrue(llmStepConfig.getMismatchAllKnowledge()));
                llmStepRuntime.setExcludeKnowledgeIntentIdNameMap(MismatchRuntime.processExcludeKnowledgeIntentIdNameMap(llmStepConfig, resource));
                llmStepRuntime.setExcludeStepIntentIdNameMap(MismatchRuntime.processExcludeStepIntentIdNameMap(llmStepConfig, resource));
                llmStepRuntime.setExcludeStepIdSet(MismatchRuntime.processExcludeStepIdSet(llmStepConfig, resource));
                llmStepRuntime.setExcludeSpecialAnswerConfigNameList(MismatchRuntime.processExcludeSpecialAnswerConfigNameList(llmStepConfig, resource));
                llmStepRuntime.setUninterruptedOnlyReplyStepIdSet(UninterruptedRuntime.canReplyStepIdSet(llmStepConfig));
                llmStepRuntime.setUninterruptedOnlyReplyKnowledgeIdSet(UninterruptedRuntime.canReplyKnowledgeIdSet(llmStepConfig));
                llmStepRuntime.setUninterruptedOnlyReplySpecialAnswerIdSet(UninterruptedRuntime.canReplySpecialAnswerIdSet(llmStepConfig));
            }
        });

        resource.setNodeIdMap(ImmutableMap.copyOf(nodeIdMap));
    }

    private void initStepRuntime(List<NodeRuntime<?>> stepNodeList, StepRuntime stepRuntime) {
        if (stepRuntime instanceof LLMStepRuntime) {
            stepRuntime.setNodeMap(ImmutableMap.of());
            stepRuntime.setRootNode(null);
        } else {
            Map<String, NodeRuntime<?>> nodeMap = MyCollectionUtils.listToMap(stepNodeList, NodeRuntime::getId);
            stepRuntime.setNodeMap(ImmutableMap.copyOf(nodeMap));
            List<DialogBaseNodePO> rootNode = stepNodeService.getStepRootNode(stepNodeList.stream().map(NodeRuntime::getOrigin).collect(Collectors.toList()));
            String nodeId = rootNode.get(0).getId();
            stepRuntime.setRootNode(stepNodeList.stream().filter(node -> nodeId.equals(node.getId())).findFirst().orElse(null));
        }
    }

    private void prepareKnowledge(RobotSnapshotPO snapshot,
                                  RobotRuntimeResource resource,
                                  Map<String, AnswerLocateBO> locateMap,
                                  Map<String, BaseAnswerContent> answerMap) {

        List<KnowledgePO> knowledgeList = snapshot.getKnowledgeList();
        if (Objects.isNull(knowledgeList)) {
            knowledgeList = Collections.emptyList();
        }
        Map<String, KnowledgeRuntime> knowledgeMap = new HashMap<>();
        Map<String, KnowledgeRuntime> intentKnowledgeMap = new HashMap<>();
        Map<String, ImmutableSet<String>> knowledgeId2IntentIdMap = new HashMap<>();
        Map<String, Integer> knowledgeId2IntentLevelCodeMap = new HashMap<>();
        Map<String, Set<Long>> knowledgeId2TagIdMap = new HashMap<>();
        Map<String, Set<Long>> knowledgeId2SmsIdMap = new HashMap<>();
        Map<String, Set<Long>> knowledgeId2WhiteGroupIdMap = new HashMap<>();
        List<String> addWechatKnowledgeIdList = new ArrayList<>();

        for (KnowledgePO knowledge : knowledgeList) {
            KnowledgeRuntime knowledgeRuntime = MyBeanUtils.copy(knowledge, KnowledgeRuntime.class);

            knowledgeMap.put(knowledge.getId(), knowledgeRuntime);
            for (String intentId : knowledge.getTriggerIntentIdList()) {
                intentKnowledgeMap.put(intentId, knowledgeRuntime);
            }
            knowledgeId2IntentIdMap.put(knowledge.getId(), ImmutableSet.copyOf(knowledge.getTriggerIntentIdList()));
            List<KnowledgeAnswerRuntime> answerRuntimeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                for (KnowledgeAnswer knowledgeAnswer : knowledge.getAnswerList()) {
                    AnswerLocateBO locate = AnswerLocateUtils.generate(knowledge, knowledgeAnswer);
                    answerRuntimeList.add( new KnowledgeAnswerRuntime(knowledgeAnswer, resource, locate));
                    locateMap.put(locate.getAnswerId(), locate);
                    answerMap.put(knowledgeAnswer.getUniqueId(), knowledgeAnswer);
                }
            }
            knowledgeRuntime.setAnswerRuntimeList(ImmutableList.copyOf(answerRuntimeList));
            knowledgeRuntime.setAnswerMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(answerRuntimeList, KnowledgeAnswerRuntime::getUniqueId)));
            if (BooleanUtils.isTrue(knowledge.getEnableIntentLevel()) && Objects.nonNull(knowledge.getIntentLevelDetailCode())) {
                knowledgeId2IntentLevelCodeMap.put(knowledge.getId(), knowledge.getIntentLevelDetailCode());
            }
            //触发动作
            if (CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                //发短信
                composeActionMap(knowledge.getActionList(), ActionCategoryEnum.SEND_SMS, knowledgeId2SmsIdMap, knowledge.getId());
                //加黑
                composeActionMap(knowledge.getActionList(), ActionCategoryEnum.WHITE_LIST, knowledgeId2WhiteGroupIdMap, knowledge.getId());
                //打标签
                composeActionMap(knowledge.getActionList(), ActionCategoryEnum.ADD_TAG, knowledgeId2TagIdMap, knowledge.getId());
                //加微
                if (ActionHelper.containsAddWechatAction(knowledge.getActionList())) {
                    addWechatKnowledgeIdList.add(knowledge.getId());
                }
            }
        }
        Set<String> businessKnowledgeIdSet = knowledgeList.stream()
                .filter(knowledge -> KnowledgeCategoryEnum.BUSINESS.equals(knowledge.getCategory()))
                .map(KnowledgePO::getId)
                .collect(Collectors.toSet());

        resource.setBusinessKnowledgeIdSet(ImmutableSet.copyOf(businessKnowledgeIdSet));
        resource.setIntent2KnowledgeMap(ImmutableMap.copyOf(intentKnowledgeMap));
        resource.setKnowledgeTriggerIntentIdSetMap(ImmutableMap.copyOf(knowledgeId2IntentIdMap));
        resource.setKnowledgeIdMap(ImmutableMap.copyOf(knowledgeMap));
        resource.getIntentLevelRuleResource().setKnowledgeId2IntentLevelCodeMap(ImmutableMap.copyOf(knowledgeId2IntentLevelCodeMap));
        resource.getIntentActionResource().setKnowledgeId2SmsIdMap(ImmutableMap.copyOf(knowledgeId2SmsIdMap));
        resource.getIntentActionResource().setKnowledgeId2WhiteGroupIdMap(ImmutableMap.copyOf(knowledgeId2WhiteGroupIdMap));
        resource.getIntentActionResource().setKnowledgeId2tagIdMap(ImmutableMap.copyOf(knowledgeId2TagIdMap));
        resource.getIntentActionResource().setAddWechatKnowledgeIdList(ImmutableList.copyOf(addWechatKnowledgeIdList));
    }

    private void composeActionMap(List<RuleActionParam> actionList,
                                  ActionCategoryEnum actionType,
                                  Map<String, Set<Long>> sourceMap,
                                  String key) {
        Set<Long> idSet = actionList.stream()
                .filter(action -> action.getActionType().equals(actionType))
                .filter(action -> CollectionUtils.isNotEmpty(action.getSourceIdList()))
                .flatMap(action -> action.getSourceIdList().stream().map(IdNamePair::getId))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(idSet)) {
            sourceMap.put(key, idSet);
        }
    }

    /**
     * 载入意图资源：
     * - 意图设置
     * - 排序后的关键词列表
     * - 排序后的拼音关键词列表
     * - 正则-意图的映射
     * - 拼音正则-意图的映射
     * - 组合意图列表
     *
     * 正则排序规则：
     * 1. 知识=流程>分支
     * 2. 业务知识/流程>一般知识/流程
     * 3. 同为业务知识/业务流程，则按正则表达式长度降序排序
     */
    private void prepareIntent(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {

        IntentPredictRequiredResource intentPredictRequiredResource = IntentPredictRequiredResourceLoader.loadFromSnapshot(snapshot, resource.getUsageTarget());

        resource.setIntentConfigPO(snapshot.getIntentConfig());

        Map<String, String> intentId2NameMap = new HashMap<>(MyCollectionUtils.listToConvertMap(snapshot.getIntentList(), IntentPO::getId, IntentPO::getName));
        intentId2NameMap.put(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.DEFAULT_INTENT_NAME);
        intentId2NameMap.put(ApplicationConstant.USER_SILENCE_INTENT_ID, ApplicationConstant.USER_SILENCE_INTENT_NAME);
        intentId2NameMap.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        intentId2NameMap.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, ApplicationConstant.COLLECT_FAILED_INTENT_NAME);
        resource.setIntentId2NameMap(ImmutableMap.copyOf(intentId2NameMap));
        resource.setIntentMap(ImmutableMap.copyOf(MyCollectionUtils.listToMap(snapshot.getIntentList(), IntentPO::getId)));
        resource.setIntentPredictRequiredResource(intentPredictRequiredResource);
    }

    private void prepareEntity(RobotSnapshotPO snapshot, RobotRuntimeResource resource) {
        List<BaseEntityPO> entityList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(snapshot.getEntityList())) {
            entityList.addAll(snapshot.getEntityList());
        }
        entityList.add(entityService.getOriginInputSystemEntity());
        List<RuntimeEntityBO> preprocessEntityList = entityPreprocessService.preprocess(entityList);
        Map<String, RuntimeEntityBO> map = MyCollectionUtils.listToMap(preprocessEntityList, RuntimeEntityBO::getId);
        resource.setEntityIdMap(ImmutableMap.copyOf(map));
    }
}
