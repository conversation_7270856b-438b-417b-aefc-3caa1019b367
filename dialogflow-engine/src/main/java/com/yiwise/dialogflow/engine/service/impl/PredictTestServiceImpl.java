package com.yiwise.dialogflow.engine.service.impl;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.PredictTestChatEngine;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResourceFactory;
import com.yiwise.dialogflow.engine.service.IntentPredictService;
import com.yiwise.dialogflow.engine.service.PredictTestService;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.utils.SessionContextSerializeUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class PredictTestServiceImpl implements PredictTestService {

    @Resource
    private BotService botService;

    @Resource
    private RobotSnapshotService snapshotService;

    @Resource
    private IntentPredictService intentPredictService;

    private Mono<EventContext> predictTest(Long botId, String input, String nodeLabel, Double lowerThreshold, Double upperThreshold) {
        if (StringUtils.isBlank(input)) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "输入内容不能为空"));
        }
        if (Objects.isNull(botId)) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空"));
        }
        if (StringUtils.isBlank(nodeLabel)) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "节点label不能为空"));
        }
        // 查询版本号
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "bot不存在"));
        }
        RobotSnapshotUsageTargetEnum target = RobotSnapshotUsageTargetEnum.CALL_OUT;

        return Mono.empty();
//        return snapshotService.asyncGetLastVersionNumber(botId, target)
//                .flatMap(version -> {
//                    if (Objects.isNull(version) || version <= 0) {
//                        return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "bot版本不存在, 请先发布审核"));
//                    }
//                    return  RobotRuntimeResourceFactory.asyncGetRuntimeResource(botId, target, version);
//                })
//                // 创建特定测试引擎
//                .map(resource -> new PredictTestChatEngine(resource, lowerThreshold, upperThreshold))
//                // 创建会话
//                .flatMap(engine -> {
//                    return engine.generateAndInitSessionContext(nodeLabel)
//                            .flatMap(sessionContext -> {
//                                ChatRequest chatRequest = new ChatRequest();
//                                chatRequest.setBotId(botId);
//                                chatRequest.setUsageTarget(target);
//                                chatRequest.setVersion(engine.getResource().getVersion());
//                                chatRequest.setRequestHostIp("127.0.0.1");
//                                UserSayFinishEvent userSayFinishEvent = new UserSayFinishEvent();
//                                userSayFinishEvent.setInputText(input);
//                                userSayFinishEvent.setPlayProgress(100.0);
//                                chatRequest.setParam(userSayFinishEvent);
//                                chatRequest.setSessionContextJson(SessionContextSerializeUtils.serialize(sessionContext));
//                                return engine.predictTest(chatRequest)
//                                        .map(tup -> tup._2);
//                            });
//                });
    }

    @Override
    public Mono<PredictTestResultVO> predictTest(PredictTestRequestVO request) {
        if (Objects.isNull(request.getBotId())) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空"));
        }
        if (StringUtils.isBlank(request.getText())) {
            return Mono.error(new ComException(ComErrorCode.VALIDATE_ERROR, "输入内容不能为空"));
        }
        if (StringUtils.isBlank(request.getNodeLabel())) {
            // 执行全量意图预测
            return intentPredictService.predict(request.getBotId(), request.getText(), request.getLowerThreshold(), request.getUpperThreshold())
                    .map(result -> {
                        PredictTestResultVO vo = new PredictTestResultVO();
                        vo.setPredictList(result);
                        return vo;
                    });
        }
        return Mono.empty();

//        return predictTest(request.getBotId(), request.getText(), request.getNodeLabel(), request.getLowerThreshold(), request.getUpperThreshold())
//                .map(eventContext -> {
//                    PredictTestResultVO predictTestResultVO = new PredictTestResultVO();
//                    predictTestResultVO.setDebugLog(String.join(";", eventContext.getDebugLog()));
//                    Map<String, SimplePredictResultVO> resultMap = new HashMap<>();
//
//                    List<SimplePredictResultVO> resultList = new ArrayList<>();
//                    if (Objects.nonNull(eventContext.getPredictResult())) {
//                        List<PredictResult> candidatePredictResultList = new ArrayList<>();
//                        candidatePredictResultList.add(eventContext.getPredictResult());
//                        if (CollectionUtils.isNotEmpty(eventContext.getCandidatePredictResultList())) {
//                            for (PredictResult predictResult : eventContext.getCandidatePredictResultList()) {
//                                if (predictResult.equals(eventContext.getPredictResult())) {
//                                    continue;
//                                }
//                                candidatePredictResultList.add(predictResult);
//                            }
//                        }
//
//                        for (PredictResult pr : candidatePredictResultList) {
//                            String intentId = pr.getIntentId();
//                            String key = intentId + pr.getPredictType();
//                            // 算法和正则分开聚合
//                            SimplePredictResultVO vo = resultMap.computeIfAbsent(key, k -> {
//                                SimplePredictResultVO simplePredictResultVO = new SimplePredictResultVO();
//                                simplePredictResultVO.setIntentId(intentId);
//                                simplePredictResultVO.setIntentName(pr.getIntentName());
//                                simplePredictResultVO.setIntentProperties(pr.getSimpleIntentInfo().getIntentProperties());
//                                simplePredictResultVO.setMatchKeywordList(new ArrayList<>());
//                                simplePredictResultVO.setIntentType(pr.getSimpleIntentInfo().getIntentType());
//                                simplePredictResultVO.setCorpusType(pr.getSimpleIntentInfo().getCorpusType());
//                                resultList.add(simplePredictResultVO);
//                                return simplePredictResultVO;
//                            });
//                            if (StringUtils.isNotBlank(pr.getKeyword())) {
//                                vo.getMatchKeywordList().add(pr.getKeyword());
//                            }
//                            if (Objects.nonNull(pr.getConfidence())) {
//                                vo.setConfidence(pr.getConfidence());
//                            }
//                        }
//                    }
//                    MatchChatManagerInfoVO matchChatManagerInfo = new MatchChatManagerInfoVO();
//                    matchChatManagerInfo.setId(eventContext.getReceptionInfo().getTargetId());
//                    matchChatManagerInfo.setName(eventContext.getReceptionInfo().getTargetName());
//                    matchChatManagerInfo.setType(eventContext.getReceptionInfo().getType().name());
//                    predictTestResultVO.setPredictList(resultList);
//                    predictTestResultVO.setMatchChatManager(matchChatManagerInfo);
//                    return predictTestResultVO;
//                });
    }

}
