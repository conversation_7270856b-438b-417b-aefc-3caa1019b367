package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.TextPlaceholderSplitter;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.api.dto.response.BotInfo;
import com.yiwise.dialogflow.api.dto.response.SimpleBotInfo;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.LLMAnswerHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.SpecialAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.action.JumpAction;
import com.yiwise.dialogflow.engine.share.action.LLMRequestAction;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.JumpTargetEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.engine.share.request.UserSayEvent;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.AnswerAudioPlayConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatRequest;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatResponse;
import com.yiwise.dialogflow.entity.enums.PredictTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.LlmGuideAnswerConfigEnum;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.reactor.FluxAccumulator;
import com.yiwise.dialogflow.service.impl.llm.CustomGenerateAnswerReplaceProcessor;
import com.yiwise.dialogflow.service.impl.llm.LLMGenerateResponsePostProcessor;
import com.yiwise.dialogflow.service.llm.LLMChatService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.common.ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;

@Slf4j
public class LLMSpecialAnswerChatManager extends AbstractCommonSpecialAnswerChatManager {

    private static final LLMChatService llmChatService = AppContextUtils.getBean(LLMChatService.class);

    public LLMSpecialAnswerChatManager(RobotRuntimeResource resource) {
        super(resource, resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.LLM));
    }

    @Override
    protected boolean isEnable(SpecialAnswerRuntime specialAnswerConfig) {
        return Objects.nonNull(specialAnswerConfig)
                && EnabledStatusEnum.ENABLE.equals(specialAnswerConfig.getEnabledStatus());
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        LLMContext llmContext = new LLMContext();
        if (enable) {
            llmContext.setSpacialAnswerId(specialAnswerConfig.getId());
            llmContext.setEnable(true);
            if (Objects.nonNull(specialAnswerConfig.getMinInputLength())) {
                llmContext.setMinInputLength(specialAnswerConfig.getMinInputLength());
            }
        }
        sessionContext.setLlmContext(llmContext);
    }

    @Override
    public String getName() {
        return SpecialAnswerConfigPO.LLM;
    }

    @Override
    protected Flux<ChatResponse> userSayFinish(SessionContext sessionContext, EventContext eventContext) {
        if (!enable) {
            return Flux.empty();
        }

        Optional<ChatResponse> chatResponse;
        // 判断是否重复上一句
        if (eventContext.isRepeatAnswer()) {
            chatResponse = repeatAnswer(sessionContext, eventContext);
        } else {
            chatResponse = processUserSayFinish(sessionContext, eventContext);
        }
        chatResponse.ifPresent(response -> {
            if (Objects.nonNull(response.getAnswer())) {
                response.getAnswer().setIsCompleted(false);
            }
        });

        // 修复：直接返回合并的Flux流，而不是使用异步订阅
        return chatResponse.map(response -> Flux.concat(
                Flux.just(response),
                processLLMEvent(sessionContext, eventContext)
                        .subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic())
        )).orElseGet(Flux::empty);
    }

    private Optional<ChatResponse> repeatAnswer(SessionContext sessionContext, EventContext eventContext) {
        LLMContext llmContext = sessionContext.getLlmContext();

        // 判断是否正在播放, 如果正在播放, 则直接返回继续播放指令
        if (isInterruptLLMAnswer(eventContext)) {
            // 用户打断当前正在播放的大模型回复, 又命中再说一遍
            ChatResponse response = new ChatResponse();
            AnswerAudioPlayConfig playConfig = new AnswerAudioPlayConfig();
            playConfig.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
            response.setAnswerAudioPlayConfig(playConfig);

            response.setActionList(Collections.emptyList());
            DebugLogUtils.commonDebugLog(eventContext, "大模型对话未播放完成, 继续播放");
            return Optional.of(response);
        }

        // 已经播放完成了, 需要更新状态
        llmContext.setIsRepeatMode(true);
        // 同时返回上一次返回的答案, 之后客户端会再次发送llmRequest, 在处理该事件时, 返回上一轮生成的答案

        // 每次开始生成时, 都重置状态
        llmContext.setIsBeginGenerate(false);
        llmContext.setIsGenerateComplete(false);

        // 获取答案
        Optional<KnowledgeAnswerRuntime> answerOptional = getCurrentAnswer(sessionContext, eventContext);
        if (!answerOptional.isPresent()) {
            log.warn("对话状态错误, 未找到当前的答案");
            return Optional.empty();
        }
        KnowledgeAnswerRuntime answer = answerOptional.get();
        RepeatAnswerPlayStrategyEnum strategy = RepeatAnswerPlayStrategyEnum.REPLAY;

        // 进行渲染
        AnswerResult answerResult = renderAnswer(answer, sessionContext, eventContext);

        DebugLogUtils.matchAnswer(eventContext, answerResult);
        DebugLogUtils.postAction(eventContext, answer.origin, resource);
        return Optional.of(generateResponse(sessionContext, eventContext, answerResult, strategy));
    }

    private Optional<ChatResponse> processUserSayFinish(SessionContext sessionContext, EventContext eventContext) {
        LLMContext llmContext = sessionContext.getLlmContext();
        // 判断是否当前正在生成中
        // 判断上一轮是否播放的答案就是大模型的答案
        if (ChatManagerPriorityEnum.INTERRUPT_LLM_SPECIAL_ANSWER.equals(eventContext.getSelectedConditionType())) {
            // 用户打断当前正在播放的大模型回复, 又再次命中大模型, 此时不再生成大模型回复, 响应继续播放
            ChatResponse response = new ChatResponse();
            AnswerAudioPlayConfig playConfig = new AnswerAudioPlayConfig();
            playConfig.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
            response.setAnswerAudioPlayConfig(playConfig);

            // 继续播放录音要
            response.setActionList(Collections.emptyList());
            DebugLogUtils.commonDebugLog(eventContext, "打断大模型对话, 继续播放");
            return Optional.of(response);
        }

        // 每次开始生成时, 都重置状态

        llmContext.setIsBeginGenerate(false);
        llmContext.setIsGenerateComplete(false);
        llmContext.setLastGenerateAnswerContent(new ArrayList<>());
        llmContext.setIsRepeatMode(false);

        // 如果没有答案, 则直接返回 action
        return super.doUserSayFinish(sessionContext, eventContext);
    }

    private boolean disableGuideAnswer() {
        return BooleanUtils.isNotTrue(specialAnswerConfig.getEnableGuideAnswer());
    }

    @Override
    protected ChatResponse generateResponse(SessionContext sessionContext,
                                            EventContext eventContext,
                                            AnswerResult answerResult,
                                            RepeatAnswerPlayStrategyEnum strategy) {
        ChatResponse response = super.generateResponse(sessionContext, eventContext, answerResult, strategy);
        List<ChatAction> actionList = new ArrayList<>(response.getActionList());
        actionList.add(new LLMRequestAction(eventContext.getUserInput()));
        response.setActionList(actionList);
        return response;
    }

    @Override
    protected Flux<ChatResponse> aiSayFinish(SessionContext sessionContext, EventContext eventContext) {
        if (!enable) {
            return Flux.empty();
        }
        LLMContext llmContext = sessionContext.getLlmContext();

        // 大模型还没有生成完成, 就执行了 aiSayFinish, 则说明生成超时了, 执行跳转到原主动流程
        if (BooleanUtils.isNotTrue(llmContext.getIsBeginGenerate())) {
            log.warn("大模型生成失败, 回到原主流程逻辑");
            ChatResponse chatResponse = new ChatResponse();
            JumpAction jumpAction = new JumpAction(AnswerSourceEnum.SPECIAL_ANSWER, specialAnswerConfig.getId());
            jumpAction.setJumpTarget(JumpTargetEnum.ORIGINAL_STEP);
            chatResponse.setActionList(Collections.singletonList(jumpAction));
            return Flux.just(chatResponse);
        }

        // 判断大模型生成的内容是否包含指定的正则，如果包含的话， 则将回答后动作设置为挂机
        String aiGenerateAnswer = CollectionUtils.isEmpty(llmContext.getLastGenerateAnswerContent()) ?
                "": String.join("", llmContext.getLastGenerateAnswerContent());
        log.debug("大模型生成的内容: {}", aiGenerateAnswer);

        Optional<String> hangupAnswer = LLMAnswerHelper.findHangupAnswer(aiGenerateAnswer);
        if (hangupAnswer.isPresent()) {
            log.debug("大模型生成的内容包含指定的内容, 设置回答后动作为挂机:{}", hangupAnswer.get());
            ChatResponse chatResponse = new ChatResponse();
            HangupAction hangupAction = ActionHelper.generateHangupAction(resource);
            chatResponse.setActionList(Collections.singletonList(hangupAction));
            return Flux.just(chatResponse);
        }

        return super.aiSayFinish(sessionContext, eventContext);
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }

        if (!(context.getOriginEventParam() instanceof UserSayFinishEvent)) {
            return Collections.emptyList();
        }
        int minInputLength = specialAnswerConfig.getMinInputLength() == null ? 4 : specialAnswerConfig.getMinInputLength();

        // 判断是否是打断大模型生成的答案
        if (isInterruptLLMAnswer(sessionContext, context) && StringUtils.length(context.getUserInput()) < minInputLength) {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTERRUPT_LLM_SPECIAL_ANSWER);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.setDynamicPredicate((sc, ec) -> true);
            return Collections.singletonList(condition);
        }

        if (StringUtils.length(context.getUserInput()) < minInputLength) {
            return Collections.emptyList();
        }

        List<ChatManagerTriggerCondition> conditionList = new ArrayList<>();
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.LLM_SPECIAL_ANSWER);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setDynamicPredicate((sc, ec) -> {
            boolean hasNluOrNlpResult = false;
            if (CollectionUtils.isNotEmpty(ec.getCandidatePredictResultList())) {
                for (PredictResult predictResult : ec.getCandidatePredictResultList()) {
                    if (PredictTypeEnum.ALGORITHM.equals(predictResult.getPredictType())) {
                        hasNluOrNlpResult = true;
                        break;
                    }
                }
            }
            if (hasNluOrNlpResult) {
                return false;
            }

            // 判断当前流程是否是大模型流程, 如果是, 则直接返回 false
            String lastStepId = sc.getLastActiveStepId();
            if (StringUtils.isNotBlank(lastStepId)) {
                StepRuntime lastStep = resource.getStepIdMap().get(lastStepId);
                if (Objects.nonNull(lastStep) && StepSubTypeEnum.isLlm(lastStep.getSubType())) {
                    return false;
                }
            }
            return !SessionContextHelper.getCurrentStepExcludeSpecialAnswerConfigNameList(sc, resource).contains(SpecialAnswerConfigPO.LLM);
        });

        if (context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        conditionList.add(condition);

        ChatManagerTriggerCondition defaultCondition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.DEFAULT_LLM_SPECIAL_ANSWER);
        defaultCondition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        defaultCondition.setDynamicPredicate((sc, ec) -> {
            return !SessionContextHelper.getCurrentStepExcludeSpecialAnswerConfigNameList(sc, resource).contains(SpecialAnswerConfigPO.LLM);
        });

        conditionList.add(defaultCondition);
        return conditionList;
    }

    private boolean isInterruptLLMAnswer(SessionContext sessionContext, EventContext eventContext) {
        if (!(eventContext.getOriginEventParam() instanceof UserSayFinishEvent)) {
            return false;
        }
        // 判断是否当前正在生成中
        // 判断上一轮是否播放的答案就是大模型的答案
        UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
        ActiveManagerInfo preActiveManagerInfo = sessionContext.getActiveManagerInfo();
        ActiveManagerInfo currentActiveManagerInfo = getChatManagerInfo(sessionContext);
        if (Objects.nonNull(preActiveManagerInfo)
                && preActiveManagerInfo.getOriginId().equals(currentActiveManagerInfo.getOriginId())
                && userSayFinishEvent.getPlayProgress() != AUDIO_PLAY_FINISH_PROGRESS) {
            return true;
        }
        return false;
    }

    private boolean isInterruptLLMAnswer(EventContext eventContext) {
        if (!(eventContext.getOriginEventParam() instanceof UserSayFinishEvent)) {
            return false;
        }
        // 判断是否当前正在生成中
        // 判断上一轮是否播放的答案就是大模型的答案
        UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
        if (userSayFinishEvent.getPlayProgress() != null
                && userSayFinishEvent.getPlayProgress() != AUDIO_PLAY_FINISH_PROGRESS) {
            return true;
        }
        return false;
    }
    @Override
    protected CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext) {
        return sessionContext.getLlmContext();
    }

    public Flux<ChatResponse> processLLMEvent(SessionContext sessionContext, EventContext eventContext) {
        return doLlmGenerateResponse(sessionContext, eventContext)
                .doOnNext(response -> updateUninterruptedConfig(response))
                .doOnNext(response -> {
                    sessionContext.setActiveManagerInfo(getChatManagerInfo(sessionContext));
                    eventContext.setActiveManagerInfo(getChatManagerInfo(sessionContext));
                });
    }

    private Flux<LLMChatResponse> generateAnswer(SessionContext sessionContext, EventContext eventContext) {
        LLMContext llmContext = sessionContext.getLlmContext();
        if (BooleanUtils.isTrue(llmContext.getIsRepeatMode())) {
            if (CollectionUtils.isNotEmpty(llmContext.getLastGenerateAnswerContent())) {
                log.debug("重复模式, 返回上一次生成的答案");
                AtomicInteger index = new AtomicInteger();
                List<String> list = llmContext.getLastGenerateAnswerContent();
                llmContext.setLastGenerateAnswerContent(new ArrayList<>());
                return Flux.fromIterable(list)
                        .map(answerText -> {
                            LLMChatResponse response = new LLMChatResponse();
                            response.setIsResponded(true);
                            response.setIsFinished(false);
                            response.setResponse(answerText);
                            response.setLastResponse(index.incrementAndGet() == list.size());
                            return response;
                        });
            } else {
                log.warn("对话处理异常, 大模型上一次生成的答案内容为空, 但是进入了重复模式");
            }
        }

        EventParam llmRequestEvent = eventContext.getOriginEventParam();
        String query = "";
        if (llmRequestEvent instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) llmRequestEvent;
            query = userSayEvent.getInputText();
        }
        LLMChatRequest request = new LLMChatRequest();
        request.setSessionId(sessionContext.getSessionId());
        request.setQuery(query);
        request.setModelName(specialAnswerConfig.getLlmModelName());
        request.setDialogFlowId(String.valueOf(sessionContext.getBotId()));
        request.setFileIdList(resource.getUsedRagDocumentIdList());
        request.setHistory(llmRequestEvent.getChatHistoryList());
        request.setBackground(specialAnswerConfig.getBackground());
        request.setRoleDescription(specialAnswerConfig.getRoleDescription());
        SimpleBotInfo botInfo = new SimpleBotInfo();
        botInfo.setBotId(resource.getBotId());
        botInfo.setName(resource.getName());
        request.setBotInfo(botInfo);
        request.setUsePrefillResponse(LlmGuideAnswerConfigEnum.ENABLE_GUIDE_ANSWER.equals(resource.getBotConfig().getLlmGuideAnswerConfig()));
        request.setCallLogId(eventContext.getLogId());
        CustomGenerateAnswerReplaceProcessor replaceProcessor = new CustomGenerateAnswerReplaceProcessor(resource.getBotConfig().getEnableGenerateAnswerReplace(),
                resource.getBotConfig().getGenerateAnswerReplaceList());

        sessionContext.setRequestLLM(true);
        return llmChatService.chat(request)
                        .transformDeferred(flux -> FluxAccumulator.accumulator(flux, new LLMGenerateResponsePostProcessor(sessionContext.getGlobalVariableValueMap())))
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, replaceProcessor));
    }

    /**
     * 请求大模型接口, 生成回复
     */
    private Flux<ChatResponse> doLlmGenerateResponse(SessionContext sessionContext,
                                                  EventContext eventContext) {

        LLMContext llmContext = sessionContext.getLlmContext();

        return generateAnswer(sessionContext, eventContext)
                .filter(item -> BooleanUtils.isTrue(item.getIsResponded()))
                .map(llmChatResponse -> {
                    String answerTemplate = llmChatResponse.getResponse();

                    AnswerLocateBO locate = AnswerLocateUtils.generate(specialAnswerConfig);
                    boolean generateComplete = BooleanUtils.isTrue(llmChatResponse.isLastResponse());
                    llmContext.setIsGenerateComplete(generateComplete);
                    String realAnswer = renderRealAnswer(sessionContext, answerTemplate);
                    AnswerPlaceholderElement element = new AnswerPlaceholderElement();

                    // 生成的答案都是需要 tts 合成的
                    element.setType(TextPlaceholderTypeEnum.TTS_SENTENCE);
                    element.setValue(realAnswer);
                    element.setRealValue(realAnswer);

                    AnswerResult answerResult = new AnswerResult();
                    answerResult.setTemplate(answerTemplate);
                    answerResult.setRealAnswer(realAnswer);
                    answerResult.setLocate(locate);
                    answerResult.setAnswerElementList(Collections.singletonList(element));
                    answerResult.setIsCompleted(generateComplete);

                    Optional<KnowledgeAnswerRuntime> currentAnswerOpt = getCurrentAnswer(sessionContext, eventContext);

                    currentAnswerOpt.ifPresent(answerRuntime -> {
                        answerResult.setLabel(answerRuntime.getLabel());
                        answerResult.setId(answerRuntime.getUniqueId());
                    });

                    // 设置打断配置
                    if (!disableGuideAnswer()) {
                        currentAnswerOpt.ifPresent(currentAnswer -> {
                            answerResult.setUninterrupted(BooleanUtils.isTrue(currentAnswer.origin.getEnableUninterrupted()));
                        });
                    }

                    ChatResponse chatResponse = new ChatResponse();
                    chatResponse.setAnswer(answerResult);
                    chatResponse.setComplete(generateComplete);
                    return chatResponse;
                })
                .doOnNext(response -> {
                    llmContext.setIsBeginGenerate(true);
                    if (Objects.isNull(llmContext.getLastGenerateAnswerContent())) {
                        llmContext.setLastGenerateAnswerContent(new ArrayList<>());
                    }
                    if (Objects.nonNull(response) && Objects.nonNull(response.getAnswer())) {
                        llmContext.getLastGenerateAnswerContent().add(response.getAnswer().getRealAnswer());
                    }

                    // 判断生成内容是否包含挂机
                    // 判断大模型生成的内容是否包含指定的正则，如果包含的话， 则将回答后动作设置为挂机
                    String aiGenerateAnswer = CollectionUtils.isEmpty(llmContext.getLastGenerateAnswerContent()) ?
                            "": String.join("", llmContext.getLastGenerateAnswerContent());
                    log.debug("大模型生成的内容: {}", aiGenerateAnswer);
                    Optional<String> hangupAnswerOpt = LLMAnswerHelper.findHangupAnswer(aiGenerateAnswer);
                    if (hangupAnswerOpt.isPresent()) {
                        log.debug("大模型生成的内容包含指定的内容:{}, 设置为允许打断", hangupAnswerOpt.get());
                        response.getAnswer().setUninterrupted(false);
                    }
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.warn("大模型生成结果为空, 执行回到原主流程逻辑");
                    ChatResponse chatResponse = new ChatResponse();
                    JumpAction jumpAction = new JumpAction(AnswerSourceEnum.SPECIAL_ANSWER, specialAnswerConfig.getId());
                    jumpAction.setJumpTarget(JumpTargetEnum.ORIGINAL_STEP);
                    chatResponse.setActionList(Collections.singletonList(jumpAction));
                    return Mono.just(chatResponse);
                }))
                .doOnError(throwable -> {
                    log.error("[LogHub_Warn]请求大模型接口失败", throwable);
                });
    }

    @NotNull
    private static String renderRealAnswer(SessionContext sessionContext, String answerTemplate) {
        TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(answerTemplate);
        Map<String, String> varValueMap = sessionContext.getGlobalVariableValueMap();
        return splitter.getTextPlaceholderList().stream()
                .map(item -> {
                    if (TextPlaceholderTypeEnum.TEXT.equals(item.getType())) {
                        return item.getValue();
                    } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(item.getType())) {
                        return varValueMap.getOrDefault(item.getValue(), "");
                    } else {
                        return "";
                    }
                }).collect(Collectors.joining());
    }

    private void updateUninterruptedConfig(ChatResponse response) {
        if (Objects.nonNull(response.getAnswer())) {
            response.getAnswer().setUninterrupted(specialAnswerConfig.getEnableUninterrupted());
            response.getAnswer().setCustomInterruptThreshold(specialAnswerConfig.getCustomInterruptThreshold());

            if (CollectionUtils.isNotEmpty(specialAnswerConfig.getUninterruptedReplyStepIdList())
                    || CollectionUtils.isNotEmpty(specialAnswerConfig.getUninterruptedReplyKnowledgeIdList())
                    || CollectionUtils.isNotEmpty(specialAnswerConfig.getUninterruptedReplySpecialAnswerIdList())) {
                response.getAnswer().setNeedTryReplyOnUninterrupted(true);
            }
        }
    }
}
