package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface IntentLevelAnalysisService {

    /**
     * 通话结束后对意向等级进行分析判断
     * @param resource 对话数据
     * @param sessionInfo 会话数据
     * @param callDataInfo 通话数据, 由交互侧获取
     * @param originChatData 对话数据, 由引擎侧获取
     * @return 意向等级分析结果
     */
    Mono<IntentLevelAnalysisResult> analysisAsync(RobotRuntimeResource resource,
                                                  SessionContext sessionContext,
                                                  SessionInfo sessionInfo,
                                                  CallDataInfo callDataInfo,
                                                  OriginChatData originChatData);
}
