package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.action.WaitAction;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class HangupDelayChatManager extends AbstractCommonSpecialAnswerChatManager {
    final AnswerLocateBO locate;
    final WaitAction waitAction;
    public HangupDelayChatManager(RobotRuntimeResource resource) {
        super(resource, resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.HANGUP_DELAY));
        if (enable) {
            this.locate = initLocate();
            this.waitAction = new WaitAction(resource.getHangupDelayMs());
        } else {
            this.locate = null;
            this.waitAction = null;
        }
    }

    private AnswerLocateBO initLocate() {
        AnswerLocateBO locate = new AnswerLocateBO();
        locate.setAnswerSource(AnswerSourceEnum.SPECIAL_ANSWER);
        locate.setSpecialAnswerConfigId(specialAnswerConfig.getId());
        locate.setSpecialAnswerConfigName(specialAnswerConfig.getName());
        locate.setSpecialAnswerConfigLabel(specialAnswerConfig.getLabel());
        return locate;
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        HangupDelayContext hangupDelayContext = new HangupDelayContext();
        if (enable) {
            hangupDelayContext.setAllowDelayCount(specialAnswerConfig.getHangupDelayCount());
        }
        sessionContext.setHangupDelayContext(hangupDelayContext);
    }

    @Override
    public String getName() {
        return "延迟挂机";
    }

    @Override
    protected Flux<ChatResponse> aiSayFinish(SessionContext sessionContext, EventContext eventContext) {
        Optional<KnowledgeAnswerRuntime> currentAnswer = getCurrentAnswer(sessionContext, eventContext);
        // 生成跳转等事件, 如果是等待用户应答, 则可能是返回空数据的
        Optional<ChatResponse> response = currentAnswer.flatMap(this::generateResponseWithAction);
        response.ifPresent(res -> {
            // 重新生成回答后操作, 全部默认为挂机, 切如果超过回答后次数了, 立即挂机
            HangupAction hangupAction = new HangupAction(true, resource.getHangupDelayMs());
            if (sessionContext.getHangupDelayContext().getDelayCount() >= sessionContext.getHangupDelayContext().getAllowDelayCount()) {
                hangupAction.setHangupDelay(false);
                hangupAction.setHangupDelayMs(0);
            }
            res.setActionList(Collections.singletonList(hangupAction));
        });
        return Mono.justOrEmpty(response).flux();
    }

    @Override
    protected void generateDebugLog(SessionContext sessionContext,
                                    EventContext eventContext,
                                    KnowledgeAnswerRuntime answer, AnswerResult answerResult) {
        if (ChatManagerPriorityEnum.IMMEDIATELY_HANGUP.equals(eventContext.getSelectedConditionType())) {
            // 命中立即挂断了
            generateIntentDebugLog(sessionContext, eventContext);
            DebugLogUtils.commonDebugLog(eventContext, String.format("命中特殊语境: %s(%s) 不关联意图", specialAnswerConfig.getName(), specialAnswerConfig.getLabel()));
            DebugLogUtils.commonDebugLog(eventContext, "执行立即挂机操作");
        } else {
            DebugLogUtils.matchAnswer(eventContext, answerResult);
            DebugLogUtils.postAction(eventContext, answer.origin, resource);
        }
    }

    @Override
    protected CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext) {
        return sessionContext.getHangupDelayContext();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        List<ChatManagerTriggerCondition> result = new ArrayList<>();
        triggerByUnknown(sessionContext, context).ifPresent(result::add);
        immediateHangup(sessionContext, context).ifPresent(result::add);
        moreThanDelayCount(sessionContext, context).ifPresent(result::add);
        userSilenceCondition(sessionContext, context).ifPresent(result::add);
        return result;
    }


    @Override
    protected ChatResponse generateResponse(SessionContext sessionContext, EventContext eventContext,
                                            AnswerResult answerResult,
                                            RepeatAnswerPlayStrategyEnum strategy) {
        // 如果是命中立即挂断策略了, 直接返回空答案, 并返回挂断指令给交互层
        if (ChatManagerPriorityEnum.IMMEDIATELY_HANGUP.equals(eventContext.getSelectedConditionType())) {
            ChatResponse response = new ChatResponse(strategy);
            response.setAnswerLocate(answerResult.getLocate());
            response.setActionList(Collections.singletonList(ActionHelper.generateHangupAction()));
            return response;
        } else {
            return super.generateResponse(sessionContext, eventContext, answerResult, strategy);
        }
    }

    @Override
    protected Flux<ChatResponse> userSilence(SessionContext sessionContext, EventContext eventContext) {
        return super.userSayFinish(sessionContext, eventContext);
    }

    /**
     * 已经处于延迟挂机模式, 且用户输入时, 未命中任务特殊语境/问答知识/流程
     */
    private Optional<ChatManagerTriggerCondition> triggerByUnknown(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.DELAY_HANGUP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.DELAY_HANGUP));
        if (context.getSpecialChatModes().contains(SpecialChatModeEnum.UNINTERRUPTED)
                && context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        return Optional.of(condition);
    }

    private Optional<ChatManagerTriggerCondition> immediateHangup(SessionContext sessionContext, EventContext context) {
        if (CollectionUtils.isEmpty(specialAnswerConfig.getExcludeIntentIdList())) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.IMMEDIATELY_HANGUP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.DELAY_HANGUP));
        if (context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        condition.setIntentIdSet(new HashSet<>(specialAnswerConfig.getExcludeIntentIdList()));
        return Optional.of(condition);
    }

    private Optional<ChatManagerTriggerCondition> moreThanDelayCount(SessionContext sessionContext, EventContext eventContext) {
        HangupDelayContext context = sessionContext.getHangupDelayContext();
        if (context.getDelayCount() < context.getAllowDelayCount()) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.MORE_THAN_DELAY_COUNT);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.DELAY_HANGUP));
        return Optional.of(condition);
    }

    private Optional<ChatManagerTriggerCondition> userSilenceCondition(SessionContext sessionContext, EventContext eventContext) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.USER_SILENCE_ON_DELAY_HANGUP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SILENCE));
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.DELAY_HANGUP));
        return Optional.of(condition);
    }

    /**
     * 其他流程/问答知识响应完成, 此时直接响应等待用户应答
     */
    public Flux<ChatResponse> generateWaitUserSay(SessionContext sessionContext, EventContext eventContext) {
        ChatResponse response = new ChatResponse();
        response.setActionList(Collections.singletonList(waitAction));
        response.setAnswerLocate(locate);
        return Flux.just(response);
    }
}
