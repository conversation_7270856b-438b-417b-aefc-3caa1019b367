package com.yiwise.dialogflow.engine;

import com.google.common.collect.ImmutableList;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class ChatEnginePool {

    // todo 使用apache object pool 替换
    private static final ConcurrentHashMap<Key, ImmutableList<EngineWrap>> POOL = new ConcurrentHashMap<>();

    private static final int MAX_SIZE = 1024;

    // 2小时
    private static final long MAX_IDLE_TIME = 2 * 60 * 60 * 1000;

    // 最大清理时间, 单位毫秒
    private static final long MAX_EXECUTE_CLEAN_MS = 5;

    // 5分钟, 检查清理一次
    private static final long CLEAN_INTERVAL = 5 * 60 * 1000;

    private static long lastCleanTimestamp = System.currentTimeMillis();

    private static final AtomicBoolean CLEANING = new AtomicBoolean(false);

    public static Mono<ChatEngine> asyncBorrowEngine(Long botId, RobotSnapshotUsageTargetEnum target, Integer version) {
        if (Objects.isNull(botId) || Objects.isNull(target) || Objects.isNull(version)) {
            throw new IllegalArgumentException("botId, target, version can not be null");
        }
        Key key = new Key(botId, target, version);
        ImmutableList<EngineWrap> list = POOL.computeIfAbsent(key, ChatEnginePool::generateEngineWrapList);
        ChatEngine engine;
        for (EngineWrap engineWrap : list) {
             engine = engineWrap.getReference().get();
            if (engine != null) {
                if (engineWrap.getReference().compareAndSet(engine, null)) {
                    engineWrap.setLastAccessTime(System.currentTimeMillis());
                    return Mono.just(engine);
                }
            }
        }
        return ChatEngineFactory.asyncCreateEngine(botId, target, version);
    }

    public static void returnEngine(ChatEngine chatEngine) {
        if (Objects.isNull(chatEngine)) {
            return;
        }
        Key key = new Key(chatEngine.getResource().getBotId(), chatEngine.getResource().getUsageTarget(), chatEngine.getResource().getVersion());
        ImmutableList<EngineWrap> list = POOL.computeIfAbsent(key, ChatEnginePool::generateEngineWrapList);
        if (Objects.isNull(list)) {
            return;
        }
        for (EngineWrap engineWrap : list) {
            if (engineWrap.getReference().get() == null) {
                if (engineWrap.getReference().compareAndSet(null, chatEngine)) {
                    engineWrap.setLastAccessTime(System.currentTimeMillis());
                    checkCleanIfNeed();
                    return;
                }
            }
        }
    }

    private static ImmutableList<EngineWrap> generateEngineWrapList(Key k) {
        ImmutableList.Builder<EngineWrap> builder = ImmutableList.builder();
        for (int i = 0; i < MAX_SIZE; i++) {
            builder.add(new EngineWrap());
        }
        return builder.build();
    }

    @Getter
    static class EngineWrap {
        @Setter
        private long lastAccessTime;
        private final AtomicReference<ChatEngine> reference = new AtomicReference<>();
    }

    private static void checkCleanIfNeed() {
        // 随机检查
        long now = System.currentTimeMillis();

        // 如果没有到阈值, 不进行清理
        if (now - lastCleanTimestamp < CLEAN_INTERVAL) {
            return;
        }

        if (CLEANING.compareAndSet(false, true)) {
            try {
                lastCleanTimestamp = now;
                long start = System.currentTimeMillis();
                doClean();
                long end = System.currentTimeMillis();
                if (end - start > MAX_EXECUTE_CLEAN_MS) {
                    log.debug("清理过期的引擎实例, cost: {} ms", end - start);
                }
            } finally {
                CLEANING.set(false);
            }
        }
    }

    private static void doClean() {
        Set<Key> keySet = POOL.keySet();
        long begin = System.currentTimeMillis();
        List<Key> deleteKeys = new ArrayList<>(128);
        for (Key key : keySet) {
            ImmutableList<EngineWrap> list = POOL.get(key);
            if (Objects.isNull(list)) {
                continue;
            }
            if (isAllIdle(list, begin)) {
                deleteKeys.add(key);
            }
            long now = System.currentTimeMillis();
            if (begin + MAX_EXECUTE_CLEAN_MS < now) {
                log.info("clean pool cost {} ms, 达到阈值, 暂停清理", now - begin);
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(deleteKeys)) {
            for (Key key : deleteKeys) {
                POOL.remove(key);
            }
        }
    }

    private static boolean isIdle(EngineWrap engineWrap, long begin) {
        return engineWrap.getLastAccessTime() + MAX_IDLE_TIME < begin;
    }

    private static boolean isAllIdle(ImmutableList<EngineWrap> list, long begin) {
        for (EngineWrap engineWrap : list) {
            if (!isIdle(engineWrap, begin)) {
                return false;
            }
        }
        return true;
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode
    static class Key {
        Long botId;
        RobotSnapshotUsageTargetEnum target;
        Integer version;
    }
}
