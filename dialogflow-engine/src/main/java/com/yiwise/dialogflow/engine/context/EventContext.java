package com.yiwise.dialogflow.engine.context;

import com.yiwise.dialogflow.engine.ReceptionInfo;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.domain.NodeJumpInfo;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.algorithm.IntentResultDetailBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
public class EventContext {

    long startTime = System.currentTimeMillis();

    Integer seq;

    EventParam originEventParam;

    volatile ChatEventTypeEnum event;

    String userInput;

    /**
     * 对于断句补齐时, 内容为补齐前当前的输入内容, 如果非断句补齐, 则和userInput一致
     */
    String originUserInput;

    String clientLastPlayAnswerId;

    int roundCount;

    boolean isPullbackToOrigin;

    /**
     * 跳转来源
     */
    AnswerSourceEnum jumpSource;

    /**
     * 意图预测的候选结果
     */
    List<PredictResult> candidatePredictResultList;

    /**
     * 备选意图id列表
     */
    List<String> candidateIntentIdList;

    /**
     * 最终匹配意图
     */
    String matchIntentId;

    /**
     * 意图预测最终结果
     */
    PredictResult predictResult;

    PredictResult uninterruptedPredictResult;

    boolean ignoreByNoise;

    List<String> debugLog = new ArrayList<>();

    List<String> simpleDebugLog = new ArrayList<>();

    ReceptionInfo receptionInfo;

    ActiveManagerInfo activeManagerInfo;

    boolean isRepeatAnswer;

    String repeatKnowledgeId;

    List<NodeJumpInfo> prevNodeInfoList = new ArrayList<>();

    /**
     * 是否是断句补齐
     */
    boolean isMergeRequest;

    ChatManagerPriorityEnum selectedConditionType;

    /**
     * 处于不可打断状态, 这个时候允许打断的只有下面的这些意图和流程,问答知识等
     */
    boolean isUninterrupted;

    Set<String> canInterruptIntentIdSet = new HashSet<>();

    Set<String> canInterruptStepIdSet = new HashSet<>();

    Set<String> canInterruptKnowledgeIdSet = new HashSet<>();

    Set<String> canInterruptSpecialAnswerIdSet = new HashSet<>();

    Set<String> canInterruptBranchIntentIdSet = new HashSet<>();

    Set<SpecialChatModeEnum> specialChatModes = new HashSet<>();

    /**
     * 复用前面步骤意图预测的结果
     */
    boolean reusePreStepPredictResult;

    IntentResultDetailBO algorithmPredictIntent;

    String logId;

    /**
     * 当前输入提取的所有实体值列表
     */
    List<EntityValueBO> entityValueList = Collections.emptyList();

    Map<String, String> playableVariableValueMap = new HashMap<>();

    /**
     * 是否需要加微
     */
    boolean needAddWechat;

    public EventContext() {
    }
}
