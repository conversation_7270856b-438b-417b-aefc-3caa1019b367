package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
public abstract class AbstractContextChatManager implements ChatManager {

    protected final RobotRuntimeResource resource;

    public AbstractContextChatManager(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public final Flux<ChatResponse> process(SessionContext sessionContext, EventContext eventContext) {
        return Flux.defer(() -> {
            long start = System.currentTimeMillis();
            log.info("开始执行 [{}] 对话管理器", getName());
            return Flux.using(
                    // 资源初始化
                    () -> {
                        resetContext(sessionContext, eventContext);
                        return new Object(); // 返回一个标记对象，表示资源已初始化
                    },
                    // 使用资源的主要逻辑
                    resource -> doProcess()
                            .doOnNext(response -> {
                                ActiveManagerInfo activeManagerInfo = getChatManagerInfo(sessionContext);
                                sessionContext.setActiveManagerInfo(activeManagerInfo);
                                eventContext.setActiveManagerInfo(activeManagerInfo);
                            }).doOnComplete(() -> {
                                long end = System.currentTimeMillis();
                                log.info("执行完成 [{}] 对话管理器, 耗时={}", getName(), (end - start));
                            }),
                    // 资源清理
                    resource -> clearContext()
            );
        });
    }

    protected abstract void resetContext(SessionContext sessionContext, EventContext eventContext);

    protected abstract void clearContext();

    public abstract ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext);

    public abstract Flux<ChatResponse> doProcess();
}