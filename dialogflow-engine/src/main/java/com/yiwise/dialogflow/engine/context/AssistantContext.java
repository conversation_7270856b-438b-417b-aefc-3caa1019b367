package com.yiwise.dialogflow.engine.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssistantContext extends CommonSpecialAnswerContext {
    /**
     * 是否开启了最高优先级
     */
    boolean enableHighestPriority;


    /**
     * 特殊语境配置 id
     */
    String specialAnswerConfigId;
}
