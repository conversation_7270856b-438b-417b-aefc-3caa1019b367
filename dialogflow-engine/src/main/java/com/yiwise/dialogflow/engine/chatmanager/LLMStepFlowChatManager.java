package com.yiwise.dialogflow.engine.chatmanager;

import com.google.common.collect.Sets;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.TextPlaceholderSplitter;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.AnswerPredicate;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.LLMAnswerHelper;
import com.yiwise.dialogflow.engine.helper.LLMRequestHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.action.*;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.enums.*;
import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import com.yiwise.dialogflow.engine.share.request.*;
import com.yiwise.dialogflow.engine.share.response.AnswerAudioPlayConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.AnswerSelectUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.dto.llmchat.*;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.reactor.FluxAccumulator;
import com.yiwise.dialogflow.service.impl.llm.CustomGenerateAnswerReplaceProcessor;
import com.yiwise.dialogflow.service.impl.llm.LLMGenerateResponsePostProcessor;
import com.yiwise.dialogflow.service.llm.LLMChatService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.reactivestreams.Publisher;
import org.slf4j.MDC;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.common.ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;
import static com.yiwise.dialogflow.engine.share.model.SimpleChatHistory.ROLE_USER;


@Slf4j
public class LLMStepFlowChatManager extends AbstractStepFlowChatManager {
    private static final LLMChatService llmChatService = AppContextUtils.getBean(LLMChatService.class);

    private final LLMStepRuntime llmStepRuntime;

    public LLMStepFlowChatManager(RobotRuntimeResource resource, StepRuntime stepRuntime) {
        super(resource, stepRuntime);
        llmStepRuntime = (LLMStepRuntime) stepRuntime;
    }

    volatile SessionContext sessionContext;
    volatile EventContext eventContext;

    @Override
    public String getName() {
        return step.getType() + ":" + step.getLabel() + ":"+ step.getName();
    }

    @Override
    protected void resetContext(SessionContext sessionContext, EventContext eventContext) {
        this.sessionContext = sessionContext;
        this.eventContext = eventContext;
    }

    @Override
    protected void clearContext() {
        this.sessionContext = null;
        this.eventContext = null;
    }

    @Override
    public Flux<ChatResponse> doProcess() {
        StepFlowContext context = getStepFlowContext(sessionContext);
        Flux<ChatResponse> response = executeEvent(sessionContext, eventContext, context);

        return response.doOnNext(r -> {
            // 记录当前流程为最后活跃流程
            sessionContext.setLastActiveStepId(stepId);
            if (StepTypeEnum.MAIN.equals(step.getType())) {
                sessionContext.setLastActiveMainStepId(stepId);
            }
        });

//        updateContextState(sessionContext, eventContext, context, response.orElse(null));

//        return response;
    }

    /**
     */
    private void updateContextState(SessionContext sessionContext,
                                    EventContext eventContext,
                                    StepFlowContext context,
                                    ChatResponse chatResponse) {
        if (chatResponse != null && CollectionUtils.isNotEmpty(chatResponse.getActionList())) {
            for (ChatAction chatAction : chatResponse.getActionList()) {
                if (ActionTypeEnum.LLM_REQUEST.equals(chatAction.getType())) {
                    context.setIsRequestGenerate(true);
                }
            }
        }
    }


    private Flux<ChatResponse> executeEvent(SessionContext sessionContext,
                                                EventContext eventContext,
                                                StepFlowContext context) {
        Flux<ChatResponse> responseFlux = Flux.empty();
        switch (eventContext.getEvent()) {
            case ENTER:
                if (BooleanUtils.isTrue(eventContext.isPullbackToOrigin())) {
                    responseFlux = doPullback(sessionContext, eventContext, context);
                } else {
                    responseFlux = enter(sessionContext, context);
                }
                break;
            case AI_SAY_FINISH:
                responseFlux = aiSayFinish(context, (AiSayFinishEvent) eventContext.getOriginEventParam());
                break;
            case USER_SAY_FINISH:
                responseFlux = userSayFinish(sessionContext, context, eventContext);
                break;
            case USER_SILENCE:
                responseFlux = userSilence(context);
                break;
            case LLM_REQUEST:
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
            default:
                break;
        }

        return responseFlux.doOnNext(response -> {
            updateUninterruptedConfig(response);
            if (response.getAnswer() != null) {
                response.getAnswer().setIsCompleted(false);
            }
        });
    }


    private void updateUninterruptedConfig(ChatResponse response) {
        if (Objects.nonNull(response.getAnswer())) {
            response.getAnswer().setUninterrupted(llmStepRuntime.getOriginStepConfig().getEnableUninterrupted());
            response.getAnswer().setCustomInterruptThreshold(llmStepRuntime.getOriginStepConfig().getCustomInterruptThreshold());

            if (CollectionUtils.isNotEmpty(llmStepRuntime.getOriginStepConfig().getUninterruptedReplyStepIdList())
                    || CollectionUtils.isNotEmpty(llmStepRuntime.getOriginStepConfig().getUninterruptedReplyKnowledgeIdList())
                    || CollectionUtils.isNotEmpty(llmStepRuntime.getOriginStepConfig().getUninterruptedReplySpecialAnswerIdList())) {
                response.getAnswer().setNeedTryReplyOnUninterrupted(true);
            }
        }
    }

    private Flux<LLMChatResponse> generateAnswer(SessionContext sessionContext,
                                                 StepFlowContext context,
                                                 String userInput,
                                                 List<SimpleChatHistory> historyList) {
        if (BooleanUtils.isTrue(context.getIsRepeatMode())) {
            if (CollectionUtils.isNotEmpty(context.getLastGenerateAnswerContent())) {
                log.debug("重复模式, 返回上一次生成的答案");
                AtomicInteger index = new AtomicInteger();
                List<String> list = new ArrayList<>(context.getLastGenerateAnswerContent());
                return Flux.fromIterable(list)
                        .map(answerText -> {
                            LLMChatResponse response = new LLMChatResponse();
                            response.setIsResponded(true);
                            response.setIsFinished(false);
                            response.setResponse(answerText);
                            response.setLastResponse(index.incrementAndGet() == list.size());
                            return response;
                        });
            } else {
                log.warn("对话处理异常, 大模型上一次生成的答案内容为空, 但是进入了重复模式");
            }
        }

        context.setLastGenerateAnswerContent(new ArrayList<>());

        EventParam llmRequestEvent = eventContext.getOriginEventParam();

        String query = "";
        if (llmRequestEvent instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) llmRequestEvent;
            query = userSayEvent.getInputText();
        }
        LLMChatRequest request = LLMRequestHelper.initRequest(sessionContext, resource, llmStepRuntime, eventContext);
        request.setQuery(query);
        if (CollectionUtils.isNotEmpty(llmRequestEvent.getChatHistoryList())) {
            for (SimpleChatHistory item : llmRequestEvent.getChatHistoryList()) {
                if (StringUtils.equals("用户无应答", item.getContent()) && ROLE_USER.equals(item.getRole())) {
                    item.setContent("");
                }
            }
        }
        request.setHistory(historyList);
        if (StringUtils.isBlank(context.getTrackInfo())) {
            request.setTrackInfo("");
        } else {
            request.setTrackInfo(context.getTrackInfo());
        }
        context.setTrackInfo("");
        CustomGenerateAnswerReplaceProcessor replaceProcessor = new CustomGenerateAnswerReplaceProcessor(resource.getBotConfig().getEnableGenerateAnswerReplace(),
                resource.getBotConfig().getGenerateAnswerReplaceList());

        sessionContext.setRequestLLM(true);
        return llmChatService.llmStepChat(request)
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, new LLMGenerateResponsePostProcessor(sessionContext.getGlobalVariableValueMap())))
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, replaceProcessor));
    }

    private Flux<ChatResponse> llmRequest(SessionContext sessionContext,
                                          StepFlowContext context,
                                          EventContext eventContext,
                                          String userInput,
                                          List<SimpleChatHistory> historyList) {

        List<ResponseTool> allTools = new ArrayList<>();
        EventParam eventParam = eventContext.getOriginEventParam();
        StepFlowContext stepFlowContext = getStepFlowContext(sessionContext);
        List<String> responseAnswerTextList = new ArrayList<>();
        AtomicBoolean jumped = new AtomicBoolean();
        return generateAnswer(sessionContext, context, userInput, historyList)
                .doOnNext((response) -> {
                    log.debug("大模型响应:{}", response);
                    if (BooleanUtils.isTrue(response.getIsFinished())) {
                        log.debug("任务已完成");
                        context.setIsLlmTaskFinished(true);
                    }
                    if (StringUtils.isNotBlank(response.getTrackInfo())) {
                        context.setTrackInfo(context.getTrackInfo() + response.getTrackInfo());
                    }
                })
                .filter(item -> BooleanUtils.isTrue(item.getIsResponded()))
                .filter(item -> StringUtils.isNotBlank(item.getResponse())
                        || CollectionUtils.isNotEmpty(item.getTools())
                        || MapUtils.isNotEmpty(item.getCollectInfo())
                        || item.isLastResponse())
                .switchIfEmpty(Mono.defer(() -> {
                    // 流程完成了, 没有生成响应信息, 判断是否是最后一个流程, 是的话, 则生成默认的挂机话术
                    if (BooleanUtils.isTrue(context.getIsLlmTaskFinished())) {
                        if (isLastMainStep(step)) {
                            log.debug("最后一个流程, 且当前流程已完成, 回复挂机话术");
                            return Mono.just(generateHangupAnswer());
                        } else {
                            log.debug("流程已完成, 且不是最后一个流程, 响应信息为空");
                            return Mono.empty();
                        }
                    }
                    log.debug("大模型拒答, 生成 ai 无法应答话术");
                    return Mono.justOrEmpty(generateUnknownAnswer());
                }))
                .onErrorResume((e) -> {
                    log.debug("生成答案文本失败, 使用 无法应答响应:{}", e.getMessage(), e);
                     return Mono.justOrEmpty(generateUnknownAnswer());
                })
                .flatMap(llmChatResponse -> {
                    // 执行跳转动作, 对于跳转动作, 因为流式生成的结果全部都是一致的, 所以响应第一个结果, 其他结果忽略
                    if (CollectionUtils.isNotEmpty(llmChatResponse.getTools())) {
                        allTools.addAll(
                                llmChatResponse.getTools().stream()
                                        .filter(item -> StringUtils.isNotBlank(item.getName()))
                                        .filter(item -> item.getName().startsWith("tool-"))
                                        .collect(Collectors.toList())
                        );
                    }
                    AtomicBoolean assign = new AtomicBoolean();
                    if (MapUtils.isNotEmpty(llmChatResponse.getCollectInfo())) {
                        // 执行赋值
                        Map<String, String> varNameIdMap = new HashMap<>();
                        resource.getVariableIdMap().forEach((k, v) -> {
                            varNameIdMap.put(v.getName(), v.getId());
                        });
                        llmChatResponse.getCollectInfo().forEach((name, value) -> {
                            String oldValue = sessionContext.getGlobalVariableValueMap().get(name);
                            if (!StringUtils.equals(oldValue, value)) {
                                String varId = varNameIdMap.get(name);
                                if (StringUtils.isNotBlank(varId)) {
                                    SessionContextHelper.variableAssign(resource, sessionContext, eventContext, varId, value);
                                    assign.set(true);
                                }
                            }
                        });
                    }

                    if (CollectionUtils.isNotEmpty(allTools)) {
                        if (llmChatResponse.isLastResponse()) {
                            // 最后一个再处理
                            return processLLMTools(allTools);
                        } else {
                            // 仅响应了动态赋值信息
                            if (assign.get() && StringUtils.isBlank(llmChatResponse.getResponse())) {
                                return generateAssignDebugLogResponse(context, llmChatResponse);
                            }
                            log.debug("大模型响应工具, 等待流式结果响应完成, 再处理 tools, 先忽略当前结果");
                            return Mono.empty();
                        }
                    }

                    // 仅响应了动态赋值信息
                    if (assign.get() && StringUtils.isBlank(llmChatResponse.getResponse())) {
                        return generateAssignDebugLogResponse(context, llmChatResponse);
                    }
                    // 处理大模型答案
                    return processLLMAnswer(sessionContext, context, llmChatResponse);
                })
                .doOnNext(response -> {
                    context.setIsBeginGenerate(true);
                    if (Objects.isNull(context.getLastGenerateAnswerContent())) {
                        context.setLastGenerateAnswerContent(new ArrayList<>());
                    }
                    if (Objects.nonNull(response)
                            && Objects.nonNull(response.getAnswer())
                            && StringUtils.isNotBlank(response.getAnswer().getRealAnswer())
                            && BooleanUtils.isNotTrue(context.getIsRepeatMode())) {
                        context.getLastGenerateAnswerContent().add(response.getAnswer().getRealAnswer());
                    }
                })
                .doOnError(throwable -> {
                    log.error("[LogHub_Warn]请求大模型接口失败", throwable);
                }).doOnNext(response -> {
                    if (Objects.nonNull(response.getAnswer())
                            && Objects.isNull(response.getAnswer().getUninterrupted())) {
                        updateUninterruptedConfig(response);
                    }
                    if (Objects.nonNull(response.getAnswer()) && StringUtils.isNotBlank(response.getAnswer().getRealAnswer())) {
                        responseAnswerTextList.add(response.getAnswer().getRealAnswer());
                    }
                    response.setStream(true);
                })
                .flatMap(response -> {
                    if (CollectionUtils.isEmpty(responseAnswerTextList)
                            && BooleanUtils.isTrue(stepFlowContext.getIsLlmTaskFinished())) {
                        log.debug("接收到任务完成信号, 且未向客户端生成播放答案, 执行跳转到下一主动流程");
                        if (jumped.get()) {
                            log.debug("已执行跳转, 忽略后续响应信息");
                            return Mono.empty();
                        }
                        jumped.set(true);
                        return Mono.just(generateJumpToNextStepResponse());
                    }
                    return Mono.just(response);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.debug("未响应答案, 判断任务是否完成");
                    if (BooleanUtils.isTrue(stepFlowContext.getIsLlmTaskFinished())) {
                        return Mono.just(generateJumpToNextStepResponse());
                    } else {
                        // 判断是否是打断大模型答案, 如果是, 则恢复之前的答案播放
                        if (eventParam instanceof UserSayEvent) {
                            UserSayFinishEvent userSayEvent = (UserSayFinishEvent) eventParam;
                            if (Objects.nonNull(userSayEvent.getPlayProgress())
                                    && StringUtils.isBlank(userSayEvent.getLastAnswerId())
                                    && userSayEvent.getPlayProgress() < AUDIO_PLAY_FINISH_PROGRESS ) {
                                return Mono.just(generateResumePlayAction(userSayEvent.getLastAnswerId()));
                            }
                        }

                        return Mono.empty();
                    }
                }));
    }

    private Mono<ChatResponse> generateAssignDebugLogResponse(StepFlowContext context,  LLMChatResponse llmChatResponse) {
        NodeAnswerRuntime currentAnswer = llmStepRuntime.getLlmFlowGuideAnswerMap().get(context.getPreLLMGuideAnswerId());
        if (Objects.isNull(currentAnswer)) {
            log.warn("[LogHub_Warn]对话状态错误, 获取当前答案失败");
            return Mono.empty();
        }
        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(currentAnswer.getUniqueId());
        boolean generateComplete = BooleanUtils.isTrue(llmChatResponse.isLastResponse());
        String realAnswer = "";
        AnswerPlaceholderElement element = new AnswerPlaceholderElement();

        // 生成的答案都是需要 tts 合成的
        element.setValue(realAnswer);
        element.setRealValue(realAnswer);

        AnswerResult answerResult = new AnswerResult();
        answerResult.setTemplate(realAnswer);
        answerResult.setRealAnswer(realAnswer);
        answerResult.setLocate(locate);
        answerResult.setAnswerElementList(Collections.singletonList(element));
        answerResult.setLabel(currentAnswer.getLabel());
        answerResult.setId(currentAnswer.getUniqueId());
        answerResult.setIsCompleted(generateComplete);

        ChatResponse chatResponse = new ChatResponse();
        chatResponse.setAnswer(answerResult);
        chatResponse.setComplete(generateComplete);
        chatResponse.setStream(true);
        return Mono.just(chatResponse);
    }

    private LLMChatResponse generateHangupAnswer() {
        LLMChatResponse response = new LLMChatResponse();
        response.setIsResponded(true);
        response.setIsFinished(true);
        // todo apollo 配置
        response.setResponse("好的, 那先这样, 这边就先不打扰您了, 祝您生活愉快, 再见哈.");
        response.setLastResponse(true);
        return response;
    }

    private Optional<LLMChatResponse> generateUnknownAnswer() {
        Optional<NodeAnswerRuntime> answer = getNextAvailableUnknownAnswer(sessionContext, getStepFlowContext(sessionContext));
        if (!answer.isPresent()) {
            log.warn("[LogHub_Warn]对话状态错误, 未获取到可用的答案, botId:{}", resource.getBotId());
            return Optional.empty();
        }
        LLMChatResponse response = new LLMChatResponse();
        response.setIsResponded(true);
        response.setIsFinished(false);
        // todo apollo 配置
        response.setResponse(answer.get().getText());
        response.setLastResponse(true);
        return Optional.of(response);
    }

    private boolean isLastMainStep(StepRuntime step) {
        int index = resource.getMainStepList().lastIndexOf(step);
        return index == resource.getMainStepList().size() - 1;
    }

    private @NotNull Mono<ChatResponse> processLLMAnswer(SessionContext sessionContext, StepFlowContext context, LLMChatResponse llmChatResponse) {
        String answerTemplate = llmChatResponse.getResponse();
        boolean generateComplete = BooleanUtils.isTrue(llmChatResponse.isLastResponse());
        NodeAnswerRuntime currentAnswer = llmStepRuntime.getLlmFlowGuideAnswerMap().get(context.getPreLLMGuideAnswerId());
        if (Objects.isNull(currentAnswer)) {
            log.warn("[LogHub_Warn]对话状态错误, 获取当前答案失败");
            return Mono.empty();
        }
        if (BooleanUtils.isTrue(context.getIsLlmTaskFinished())
                && StringUtils.isBlank(answerTemplate)
                && CollectionUtils.isEmpty(context.getLastGenerateAnswerContent())
                && generateComplete) {
            log.debug("任务已完成, 当前答案为空, 不生成响应");
            return Mono.empty();
        }
        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(currentAnswer.getUniqueId());
        context.setIsGenerateComplete(generateComplete);

        String realAnswer = renderRealAnswer(sessionContext, answerTemplate);
        AnswerPlaceholderElement element = new AnswerPlaceholderElement();

        // 生成的答案都是需要 tts 合成的
        element.setType(TextPlaceholderTypeEnum.TTS_SENTENCE);
        element.setValue(realAnswer);
        element.setRealValue(realAnswer);

        AnswerResult answerResult = new AnswerResult();
        answerResult.setTemplate(answerTemplate);
        answerResult.setRealAnswer(realAnswer);
        answerResult.setLocate(locate);
        answerResult.setAnswerElementList(Collections.singletonList(element));
        answerResult.setLabel(currentAnswer.getLabel());
        answerResult.setId(currentAnswer.getUniqueId());
        answerResult.setIsCompleted(generateComplete);

        ChatResponse chatResponse = new ChatResponse();
        chatResponse.setAnswer(answerResult);
        chatResponse.setComplete(generateComplete);
        return Mono.just(chatResponse);
    }

    private Publisher<? extends ChatResponse> processLLMTools(List<ResponseTool> tools) {
        log.debug("处理大模型调用工具:{}", tools);

        // 支持的指令
        // 1. 跳转到流程
        List<LLMAction> actions = tools.stream()
                .map(this::parseLLMAction)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        LLMJumpAction jumpAction = actions.stream()
                .filter(item -> LLMAction.TYPE_JUMP.equals(item.getType()))
                .map(item -> (LLMJumpAction)item)
                .findAny()
                .orElse(null);

        if (Objects.nonNull(jumpAction)) {
            // 生成跳转到流程的响应
            JumpAction dialogJumpAction = new JumpAction(AnswerSourceEnum.STEP, stepId);
            dialogJumpAction.setJumpTarget(JumpTargetEnum.SPECIFIED_STEP);
            // 根据流程名称获取流程 id
            String jumpStepId = resource.getStepIdMap().values().stream()
                    .filter(item -> item.getLabel().equals(jumpAction.getStepLabel()))
                    .map(StepRuntime::getId)
                    .findAny().orElse(null);
            dialogJumpAction.setJumpStepId(jumpStepId);
            ChatResponse response = new ChatResponse();
            response.setActionList(Collections.singletonList(dialogJumpAction));
            return Mono.just(response);
        }
        return Mono.empty();
    }

    private Flux<ChatResponse> userSilence(StepFlowContext context) {
        context.setIsRequestGenerate(false);
        context.setIsBeginGenerate(false);
        context.setIsGenerateComplete(false);
        context.setIsRepeatMode(false);

        // 判断流程是否完成了
        Optional<Boolean> completeResult = queryTaskComplete(sessionContext, context, eventContext);
        if (completeResult.isPresent()) {
            if (BooleanUtils.isTrue(completeResult.get())) {
                log.debug("流程任务完成");
                // 跳转到下一主动流程
                DebugLogUtils.commonDebugLog(eventContext, "大模型流程任务完成");
                DebugLogUtils.commonDebugLog(eventContext, "流程名称:" + step.getLabel() + ":" + step.getName());
                return Flux.just(generateResponseOnStepFinish());
            } else {
                DebugLogUtils.commonDebugLog(eventContext, "大模型流程对话, 用户无应答");
                DebugLogUtils.commonDebugLog(eventContext, "流程名称:" + step.getLabel() + ":" + step.getName());
                return generateGuideAnswerResponse(sessionContext, context, "", AUDIO_PLAY_FINISH_PROGRESS, context.getPreLLMAnswerId());
            }
        } else {
            return Flux.empty();
        }
    }

    private Flux<ChatResponse> userSayFinish(SessionContext sessionContext, StepFlowContext context, EventContext eventContext) {
        context.setIsRequestGenerate(false);
        if (eventContext.isRepeatAnswer()) {
            // 重复
            context.setIsBeginGenerate(false);
            context.setIsGenerateComplete(false);
            context.setIsRepeatMode(false);
            return repeatAnswer(sessionContext, context, eventContext);
        }

        UserSayFinishEvent finishEvent = (UserSayFinishEvent)eventContext.getOriginEventParam();
        context.setIsBeginGenerate(false);
        context.setIsGenerateComplete(false);
        context.setIsRepeatMode(false);

        boolean isInterruptLLMAnswer = isInterruptLLMAnswer(sessionContext, eventContext);

        // 判断流程是否完成了
        Optional<Boolean> complete = queryTaskComplete(sessionContext, context, eventContext);
        if (complete.isPresent()) {
            if (BooleanUtils.isTrue(complete.get())) {
                if (isInterruptLLMAnswer && isLastMainStep(step)) {
                    log.debug("流程任务已完成, 且最后一个流程, 且打断大模型, 返回继续播放指令");
                    DebugLogUtils.commonDebugLog(eventContext, "大模型流程任务完成, 打断最后一个大模型流程, 继续播放");
                    return Flux.just(generateResumePlayResponse(sessionContext));
                }
                log.debug("流程任务完成");
                // 跳转到下一主动流程
                DebugLogUtils.commonDebugLog(eventContext, "大模型流程任务完成");
                DebugLogUtils.commonDebugLog(eventContext, "流程名称:" + step.getLabel() + ":" + step.getName());
                return Flux.just(generateResponseOnStepFinish());
            } else {
                DebugLogUtils.commonDebugLog(eventContext, "大模型流程对话");
                DebugLogUtils.commonDebugLog(eventContext, "流程名称:" + step.getLabel() + ":" + step.getName());
                return generateGuideAnswerResponse(sessionContext, context, eventContext.getUserInput(), finishEvent.getPlayProgress(), finishEvent.getLastAnswerId());
            }
        } else {
            return Flux.empty();
        }
    }

    private ChatResponse generateResumePlayResponse(SessionContext sessionContext) {
        ChatResponse response = new ChatResponse();
        String lastAnswerId = sessionContext.getLastAnswerId();
        response.setAnswer(AnswerRenderUtils.render(lastAnswerId, sessionContext, resource));
        AnswerAudioPlayConfig playConfig = new AnswerAudioPlayConfig();
        playConfig.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
        response.setAnswerAudioPlayConfig(playConfig);

        // 继续播放录音要
        response.setActionList(Collections.emptyList());
        return response;
    }


    private Flux<ChatResponse> repeatAnswer(SessionContext sessionContext,
                                            StepFlowContext stepFlowContext,
                                            EventContext eventContext) {
        // 判断是否正在播放, 如果正在播放, 则直接返回继续播放指令
        if (isInterruptLLMAnswer(sessionContext, eventContext)) {
            // 用户打断当前正在播放的大模型回复, 又命中再说一遍
            ChatResponse response = new ChatResponse();
            AnswerAudioPlayConfig playConfig = new AnswerAudioPlayConfig();
            playConfig.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
            response.setAnswerAudioPlayConfig(playConfig);

            response.setActionList(Collections.emptyList());
            DebugLogUtils.commonDebugLog(eventContext, "大模型流程答案未播放完成, 继续播放");
            return Flux.just(response);
        }

        // 已经播放完成了, 需要更新状态
        stepFlowContext.setIsRepeatMode(true);
        // 同时返回上一次返回的答案, 之后客户端会再次发送llmRequest, 在处理该事件时, 返回上一轮生成的答案

        // 每次开始生成时, 都重置状态
        stepFlowContext.setIsBeginGenerate(false);
        stepFlowContext.setIsGenerateComplete(false);

        // 获取答案
        Optional<NodeAnswerRuntime> answerOptional = getCurrentAnswer(stepFlowContext);
        if (!answerOptional.isPresent()) {
            log.warn("对话状态错误, 未找到当前的答案");
            return Flux.empty();
        }
        NodeAnswerRuntime answer = answerOptional.get();

        // 进行渲染
        AnswerResult answerResult = AnswerRenderUtils.render(answer, sessionContext, resource);

        // 修复：取消引导响应的流式播放，直接返回LLM响应
        // 这样可以避免在流程跳转时出现重复的引导消息
        return llmRequest(sessionContext, stepFlowContext, eventContext, eventContext.getUserInput(), eventContext.getOriginEventParam().getChatHistoryList())
                .subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic())
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, MDC.get("MDC_LOG_ID")));
    }

    private Optional<NodeAnswerRuntime> getCurrentAnswer(StepFlowContext context) {
        NodeAnswerRuntime answerRuntime = llmStepRuntime.getLlmFlowGuideAnswerMap().get(context.getPreLLMAnswerId());
        if (Objects.nonNull(answerRuntime)) {
            return Optional.of(answerRuntime);
        }
        answerRuntime = llmStepRuntime.getLlmFlowUnknownAnswerMap().get(context.getPreLLMAnswerId());
        return Optional.ofNullable(answerRuntime);
    }

    private boolean isInterruptLLMAnswer(SessionContext sessionContext, EventContext eventContext) {
        if (!(eventContext.getOriginEventParam() instanceof UserSayFinishEvent)) {
            return false;
        }
        // 判断是否当前正在生成中
        // 判断上一轮是否播放的答案就是大模型的答案
        UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
        ActiveManagerInfo preActiveManagerInfo = sessionContext.getActiveManagerInfo();
        ActiveManagerInfo currentActiveManagerInfo = getChatManagerInfo(sessionContext);
        if (Objects.nonNull(preActiveManagerInfo)
                && preActiveManagerInfo.getOriginId().equals(currentActiveManagerInfo.getOriginId())
                && userSayFinishEvent.getPlayProgress() != AUDIO_PLAY_FINISH_PROGRESS) {
            return true;
        }
        return false;
    }


    private Flux<ChatResponse> generateGuideAnswerResponse(SessionContext sessionContext,
                                                               StepFlowContext context,
                                                               String userInput,
                                                               Double playProgress,
                                                               String preAnswerId) {

        Optional<NodeAnswerRuntime> answer = getNextAvailableGuideAnswer(sessionContext, context);
        if (!answer.isPresent()) {
            log.warn("[LogHub_Warn]对话状态错误, 未获取到可用的答案, botId:{}", resource.getBotId());
            return Flux.empty();
        }
        context.setPreLLMGuideAnswerId(answer.get().getUniqueId());
        context.setPreLLMAnswerId(answer.get().getUniqueId());

        // 修复：取消引导响应的流式播放，直接返回LLM响应
        // 这样可以避免在流程跳转时出现重复的引导消息
        return llmRequest(sessionContext, context, eventContext, userInput, eventContext.getOriginEventParam().getChatHistoryList())
                .subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic())
                .subscriberContext(cx -> cx.put(ApplicationConstant.MDC_LOG_ID, MDC.get("MDC_LOG_ID")));
    }

    private Optional<ChatResponse> generateUnknownAnswerResponse(SessionContext sessionContext, StepFlowContext context) {
        Optional<NodeAnswerRuntime> answer = getNextAvailableUnknownAnswer(sessionContext, context);
        if (!answer.isPresent()) {
            log.warn("[LogHub_Warn]对话状态错误, 未获取到可用的答案, botId:{}", resource.getBotId());
            return Optional.empty();
        }

        ChatResponse response = new ChatResponse();
        AnswerResult answerResult = AnswerRenderUtils.render(answer.get(), sessionContext, resource);
        response.setAnswer(answerResult);
        response.setComplete(true);
        context.setLastGenerateAnswerContent(Collections.singletonList(answerResult.getRealAnswer()));
        return Optional.of(response);
    }

    private ChatResponse generateJumpToNextStepResponse() {
        ChatResponse response = new ChatResponse();
        JumpAction action = new JumpAction(AnswerSourceEnum.STEP, stepId);
        action.setJumpTarget(JumpTargetEnum.NEXT_STEP);
        response.setActionList(Collections.singletonList(action));
        return response;
    }

    private ChatResponse generateJumpToOriginalStepResponse() {
        ChatResponse response = new ChatResponse();
        JumpAction action = new JumpAction(AnswerSourceEnum.STEP, stepId);
        action.setJumpTarget(JumpTargetEnum.ORIGINAL_STEP);
        response.setActionList(Collections.singletonList(action));
        return response;
    }

    private ChatResponse generateResponseOnStepFinish() {
        if (StepTypeEnum.INDEPENDENT.equals(step.getType())) {
            return generateJumpToOriginalStepResponse();
        }
        return generateJumpToNextStepResponse();
    }


    private Flux<ChatResponse> aiSayFinish(StepFlowContext context, AiSayFinishEvent aiSayFinishEvent) {
        // 1. 判断 ai 播放完成是否播放了 ai 生成的内容
        // 2. 判断当前任务是否完成, 如果完成了, 则跳转到下一主动流程, 如果没有完成, 也可以记录一下状态, 待处理用户下一句话的时候, 不用再判断了
        if (isGenerateFail(context, aiSayFinishEvent)
                && CollectionUtils.isNotEmpty(llmStepRuntime.getLlmFlowUnknownAnswerList())) {
            // 大模型生成失败了(或者生成的答案合成超时了)
            // 需要使用 unknown 答案进行响应
            log.debug("大模型生成失败, 使用 ai 无法应答的答案进行响应");
            context.setIsRequestGenerate(false);
            return Mono.justOrEmpty(generateUnknownAnswerResponse(sessionContext, context)).flux();
        }
        Optional<ChatResponse> response = generateHangupActionIfHangupAnswer(context);
        return response.map(Flux::just)
                .orElseGet(() -> Mono.justOrEmpty(generateWaitActionResponse()).flux());
    }

    /**
     * 如果生成的答案文本中包含了挂机的内容, 则响应挂机指令
     * 如果生成的不是挂机内容, 则返回 Empty
     */
    private Optional<ChatResponse> generateHangupActionIfHangupAnswer(StepFlowContext context) {
        if (CollectionUtils.isEmpty(context.getLastGenerateAnswerContent())) {
            return Optional.empty();
        }

        String aiGenerateAnswer = String.join("", context.getLastGenerateAnswerContent());

        Optional<String> hangupAnswer = LLMAnswerHelper.findHangupAnswer(aiGenerateAnswer);
        if (hangupAnswer.isPresent()) {
            log.debug("大模型生成的内容包含指定的内容, 设置回答后动作为挂机:{}", hangupAnswer.get());
            ChatResponse chatResponse = new ChatResponse();
            HangupAction hangupAction = ActionHelper.generateHangupAction(resource);
            chatResponse.setActionList(Collections.singletonList(hangupAction));
            return Optional.of(chatResponse);
        }

        return Optional.empty();
    }

    private ChatResponse generateResumePlayAction(String answerId) {
        ChatResponse chatResponse = new ChatResponse();
        ResumePlayAction resumePlayAction = new ResumePlayAction(answerId);
        chatResponse.setActionList(Collections.singletonList(resumePlayAction));
        return chatResponse;
    }

    private boolean isGenerateFail(StepFlowContext context, AiSayFinishEvent aiSayFinishEvent) {
        if (BooleanUtils.isNotTrue(context.getIsRequestGenerate())) {
            return false;
        }
        // 判断大模型是否生成成功了,
        // 判断生成的内容是否成功播放出来了
        if (BooleanUtils.isNotTrue(context.getIsBeginGenerate())) {
            log.debug("还未开始生成");
            return true;
        }

        // 生成完成
        return false;
    }

    private Optional<ChatResponse> generateWaitActionResponse() {
        ChatResponse response = new ChatResponse();
//        response.setAnswerLocate(node.getNodeLocate());
        response.setActionList(llmStepRuntime.getLlmFlowActionList());

        return Optional.of(response);
    }

    // 请求算法接口判断流程任务是否完成了
    private Optional<Boolean> queryTaskComplete(SessionContext sessionContext, StepFlowContext context, EventContext eventContext) {
        return Optional.of(BooleanUtils.isTrue(context.getIsLlmTaskFinished()));
    }

    //
    private Flux<ChatResponse> doPullback(SessionContext sessionContext, EventContext eventContext, StepFlowContext context) {
        // 检查流程是否完成
        context.setIsBeginGenerate(false);
        context.setIsGenerateComplete(false);
        context.setIsRepeatMode(false);

        Optional<Boolean> completeOpt = queryTaskComplete(sessionContext, context, eventContext);

        if (completeOpt.isPresent()) {
            if (BooleanUtils.isTrue(completeOpt.get())) {
                // 跳转到下一主动流程
                log.debug("流程完成, 跳转到下一流程");
                return Flux.just(generateJumpToNextStepResponse());
            } else {
                // 使用承接话术生成响应
                log.debug("流程未完成, 使用承接话术生成响应");
                return generateGuideAnswerResponse(sessionContext, context, eventContext.getUserInput(), null, null);
            }
        } else {
            return Flux.empty();
        }
    }

    /**
     * 第一次进入流程, 不用判断是否完成, 这里直接响应承接话术
     */
    private Flux<ChatResponse> enter(SessionContext sessionContext, StepFlowContext context) {
        context.setIsBeginGenerate(false);
        context.setIsGenerateComplete(false);
        context.setIsRepeatMode(false);
        return generateGuideAnswerResponse(sessionContext, context, eventContext.getUserInput(), null, null);
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        // 判断是否是打断大模型生成的答案
        if (enableInterrupt() && isInterruptLLMAnswer(sessionContext, context)) {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTERRUPT_LLM_STEP_FLOW);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.setDynamicPredicate((sc, ec) -> true);
            return Collections.singletonList(condition);
        }

        List<ChatManagerTriggerCondition> result = new ArrayList<>();
        result.add(defaultBranch());
        userSilenceCondition().ifPresent(result::add);
        return result;
    }

    private Optional<ChatManagerTriggerCondition> userSilenceCondition() {
        if (BooleanUtils.isNotTrue(llmStepRuntime.getOriginStepConfig().getEnableUserSilence())) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.LLM_STEP_USER_SILENCE_INTENT);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SILENCE));
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        return Optional.of(condition);
    }

    private boolean enableInterrupt() {
        return BooleanUtils.isNotTrue(llmStepRuntime.getOriginStepConfig().getEnableUninterrupted());
    }

    private ChatManagerTriggerCondition defaultBranch() {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.LLM_STEP_FLOW);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setMustNotMatchModes(Sets.newHashSet(SpecialChatModeEnum.DELAY_HANGUP, SpecialChatModeEnum.INAUDIBLE_REPEAT, SpecialChatModeEnum.UNINTERRUPTED));
        return condition;
    }

    private Optional<NodeAnswerRuntime> getNextAvailableGuideAnswer(SessionContext sessionContext,
                                                                    StepFlowContext context) {
        return AnswerSelectUtils.getNextAvailableId(context.getPreLLMGuideAnswerId(),
                llmStepRuntime.getLlmFlowGuideAnswerList(),
                        new AnswerPredicate(resource, sessionContext)
                ).map(llmStepRuntime.getLlmFlowGuideAnswerMap()::get);
    }

    private Optional<NodeAnswerRuntime> getNextAvailableUnknownAnswer(SessionContext sessionContext,
                                                                    StepFlowContext context) {
        Optional<NodeAnswerRuntime> runtimeOptional =  AnswerSelectUtils.getNextAvailableId(context.getPreLLMUnknownAnswerId(),
                llmStepRuntime.getLlmFlowUnknownAnswerList(),
                        new AnswerPredicate(resource, sessionContext)
                ).map(llmStepRuntime.getLlmFlowUnknownAnswerMap()::get);
        runtimeOptional.ifPresent(answer -> {
            context.setPreLLMUnknownAnswerId(answer.getUniqueId());
            context.setPreLLMAnswerId(answer.getUniqueId());
        });
        return runtimeOptional;
    }

    private Optional<LLMAction> parseLLMAction(ResponseTool tool) {
        if (StringUtils.isNotBlank(tool.getName())) {
            LLMJumpAction action = new LLMJumpAction();
            action.setStepLabel(tool.getName().replace("tool-", ""));
            return Optional.of(action);
        }
        return Optional.empty();
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        StepFlowContext stepFlowContext = new StepFlowContext();
        stepFlowContext.setStepId(stepId);
        stepFlowContext.setStepName(stepName);
        stepFlowContext.setKeyCaptureNodeRetryTimesMap(new HashMap<>());
        stepFlowContext.setLLMStep(true);
        sessionContext.getStepFlowContextMap().put(stepId, stepFlowContext);
    }

    private static String renderRealAnswer(SessionContext sessionContext, String answerTemplate) {
        TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(answerTemplate);
        Map<String, String> varValueMap = sessionContext.getGlobalVariableValueMap();
        return splitter.getTextPlaceholderList().stream()
                .map(item -> {
                    if (TextPlaceholderTypeEnum.TEXT.equals(item.getType())) {
                        return item.getValue();
                    } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(item.getType())) {
                        return varValueMap.getOrDefault(item.getValue(), "");
                    } else {
                        return "";
                    }
                }).collect(Collectors.joining());
    }

    @Data
    public static class LLMAction {
        public static final String TYPE_JUMP = "jump";
        public static final String TYPE_ASSIGN = "assign";

        final String type;

        public LLMAction(String type) {
            this.type = type;
        }

        private LLMAction() {
            type = "";
        }
    }

    @Data
    public static class LLMJumpAction extends LLMAction {
        public LLMJumpAction() {
            super(TYPE_JUMP);
        }

        String stepLabel;
    }

    @Data
    public static class LLMAssignAction extends LLMAction {
        public LLMAssignAction() {
            super(TYPE_ASSIGN);
        }
        String varName;
        String varValue;
    }
}
