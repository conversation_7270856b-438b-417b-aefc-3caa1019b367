package com.yiwise.dialogflow.engine.dispatcher;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.request.DeliverHttpResponseEvent;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpResponseDispatcher extends AbstractChatDispatcher {

    protected RobotRuntimeResource resource;

    public HttpResponseDispatcher(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "HttpResponseDispatcher";
    }

    @Override
    public Flux<ChatResponse> doDispatch(SessionContext sessionContext, EventContext eventContext) {
        ChatResponse response = new ChatResponse();

        EventParam originEventParam = eventContext.getOriginEventParam();
        if (originEventParam instanceof DeliverHttpResponseEvent) {
            DeliverHttpResponseEvent deliverHttpResponseEvent = (DeliverHttpResponseEvent) originEventParam;

            SessionContextHelper.leaveWaitingList(sessionContext, deliverHttpResponseEvent.getHttpRequestId());

            Map<String, String> varIdValueMap = deliverHttpResponseEvent.getVarIdValueMap();
            if (MapUtils.isNotEmpty(varIdValueMap)) {
                varIdValueMap.forEach(
                        (varId, varValue) ->
                                SessionContextHelper.variableAssign(resource, sessionContext, eventContext, varId, varValue)
                );
            }
        }

        if (MapUtils.isNotEmpty(eventContext.getPlayableVariableValueMap())) {
            response.setPlayableVariableValueMap(eventContext.getPlayableVariableValueMap());
        }

        return Flux.just(response);
    }
}
