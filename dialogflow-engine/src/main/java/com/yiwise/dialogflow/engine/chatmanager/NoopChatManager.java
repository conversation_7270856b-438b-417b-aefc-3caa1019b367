package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.context.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class NoopChatManager extends AbstractChatManager implements ChatManager{
    private final String name;
    public NoopChatManager(RobotRuntimeResource resource, String name) {
        super(resource);
        this.name = name;
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        ActiveManagerInfo info = new ActiveManagerInfo();
        info.setActiveType(ActiveTypeEnum.NOOP);
        info.setChatManagerName(getName());
        return info;
    }

    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext context) {
        return Flux.empty();
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return String.format("%s-%s",name, NoopChatManager.class.getSimpleName());
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        return Collections.emptyList();
    }
}
