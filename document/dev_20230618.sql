CREATE TABLE IF NOT EXISTS `variable_bind`
(
    `variable_bind_id`        bigint(20)                                NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `tenant_id`        bigint(20)                                NOT NULL COMMENT '租户ID',
    `bot_id`            bigint(20) not null COMMENT 'botId',
    `variable_id`      varchar(128) not null comment 'variableId',
    `customer_attribute_id` bigint(20) NOT NULL COMMENT '客户属性id',
    `bind_variable_name`      varchar(128) null comment '绑定变量名称',
    `bind_attribute_name`      varchar(128) null comment '绑定属性名称',
    create_user_id     bigint unsigned default 0                 not null,
    update_user_id     bigint unsigned default 0                 not null,
    create_time        timestamp       default CURRENT_TIMESTAMP not null,
    update_time        timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (variable_bind_id) USING BTREE,
    unique key uniq_idx_botid_tenantid_variableid_customerattributeid(bot_id, tenant_id, variable_id, customer_attribute_id) using btree
    ) ENGINE = InnoDB
    AUTO_INCREMENT = 1
    COMMENT = '变量绑定表'
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    ROW_FORMAT = Dynamic;