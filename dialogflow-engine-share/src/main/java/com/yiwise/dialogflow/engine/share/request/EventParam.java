package com.yiwise.dialogflow.engine.share.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "event", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = EnterEvent.class, name = EventParam.ENTER),
        @JsonSubTypes.Type(value = UserSayEvent.class, name = EventParam.USER_SAY),
        @JsonSubTypes.Type(value = UserSayFinishEvent.class, name = EventParam.USER_SAY_FINISH),
        @JsonSubTypes.Type(value = UserSilenceEvent.class, name = EventParam.USER_SILENCE),
        @JsonSubTypes.Type(value = AiSayFinishEvent.class, name = EventParam.AI_SAY_FINISH),
        @JsonSubTypes.Type(value = FastHangupEvent.class, name = EventParam.FAST_HANGUP),
        @JsonSubTypes.Type(value = DeliverHttpResponseEvent.class, name = EventParam.DELIVER_HTTP_RES),
        @JsonSubTypes.Type(value = KeyCaptureSuccessEvent.class, name = EventParam.KEY_CAPTURE_SUCCESS),
        @JsonSubTypes.Type(value = KeyCaptureFailedEvent.class, name = EventParam.KEY_CAPTURE_FAILED),
        @JsonSubTypes.Type(value = LLMRequestEvent.class, name = EventParam.LLM_REQUEST),
})
public class EventParam {

    public static final String ENTER = "ENTER";
    public static final String USER_SAY = "USER_SAY";
    public static final String USER_SAY_FINISH = "USER_SAY_FINISH";
    public static final String USER_SILENCE = "USER_SILENCE";
    public static final String AI_SAY_FINISH = "AI_SAY_FINISH";

    public static final String FAST_HANGUP = "FAST_HANGUP";
    public static final String DELIVER_HTTP_RES = "DELIVER_HTTP_RESPONSE";

    public static final String KEY_CAPTURE_SUCCESS = "KEY_CAPTURE_SUCCESS";
    public static final String KEY_CAPTURE_FAILED = "KEY_CAPTURE_FAILED";
    public static final String LLM_REQUEST = "LLM_REQUEST";

    ChatEventTypeEnum event;

    long offset;

    List<SimpleChatHistory> chatHistoryList;
}
