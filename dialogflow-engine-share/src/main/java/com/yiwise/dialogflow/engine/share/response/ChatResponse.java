package com.yiwise.dialogflow.engine.share.response;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.utils.PrintLogUtils;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ChatResponse {

    public ChatResponse() {
        this(RepeatAnswerPlayStrategyEnum.REPLAY);
    }
    public ChatResponse(RepeatAnswerPlayStrategyEnum strategy) {
        if (Objects.isNull(strategy)) {
            strategy = RepeatAnswerPlayStrategyEnum.REPLAY;
        }
        answerAudioPlayConfig = new AnswerAudioPlayConfig();
        answerAudioPlayConfig.setRepeatPlayStrategy(strategy);
    }

    Integer sequence;

    String sessionId;

    String inputText;

    AnswerResult answer;

    AnswerAudioPlayConfig answerAudioPlayConfig;

    AnswerLocateBO answerLocate;


    /**
     * 流式响应的序号, 从0开始, 每次+1
     */
    boolean stream = false;

    /**
     * 响应是否完成,
     * 用于流式响应的时候, 判断是否还有后续的响应
     */
    boolean complete = true;

    public void setAnswer(AnswerResult answer) {
        if (Objects.nonNull(answer) && Objects.nonNull(answer.getLocate())) {
            answerLocate = answer.getLocate();
        }
        this.answer = answer;
    }

    List<ChatAction> actionList = new ArrayList<>();

    /**
     * 详细版debug log
     */
    List<String> debugLogList;

    /**
     * 简版debug log
     */
    List<String> simpleDebugLogList;

    String sessionContextJson;

    String preSessionContextJson;

    /**
     * 事件日志信息, 在对话结束后对对话分析需要用的, 可以暂存在客户端侧, 在需要分析时, 在连同对话数据一起发送给过来进行分析
     * 后面如果把对话数据持久化到数据库, 也就不需要这个字段了
     * 目前考虑到把这个字段放在客户端, 主要是每次对话中调用都传来传去, 比较消耗性能
     */
    String eventLogContent;

    /**
     * 可播放的变量键值对
     * 主要是对话过程中收集到的动态变量的值, 且该动态变量在答案文本中将要播放
     */
    Map<String, String> playableVariableValueMap;

    /**
     * 无需重置按键采集配置
     */
    Boolean noNeedResetKeyCaptureConfig;

    @JsonIgnore
    public String toPrintString() {
        StringBuilder sb = new StringBuilder(512);
        sb.append("{\"sequence\": ").append(sequence);
        sb.append(", \"answer\": ");
        answerToPrintString(sb, answer);
        sb.append(", \"debugLog\": ");
        PrintLogUtils.stringCollectionToPrintLog(sb, debugLogList);
        sb.append(", \"playableVariableValueMap\": ");
        mapToPrintString(sb, playableVariableValueMap);
        sb.append(", \"noNeedResetKeyCaptureConfig\": ").append(noNeedResetKeyCaptureConfig).append("}");
        return sb.toString();
    }

    private static void answerToPrintString(StringBuilder sb, AnswerResult answer) {
        if (Objects.isNull(answer)) {
            sb.append("null");
            return;
        }
        sb.append("{\"id\": ").append(answer.getId());
        sb.append(", \"template\": \"").append(answer.getTemplate());
        sb.append("\", \"realAnswer\": \"").append(answer.getRealAnswer());
        sb.append("\", \"uninterrupted\": ").append(answer.getUninterrupted());
        sb.append(", \"customInterruptThreshold\": ").append(answer.getCustomInterruptThreshold());
        sb.append(", \"needTryReplyOnUninterrupted\": ").append(answer.getNeedTryReplyOnUninterrupted()).append("}");
    }

    private static void mapToPrintString(StringBuilder sb, Map<String, String> map) {
        if (Objects.isNull(map)) {
            sb.append("null");
            return;
        }
        boolean first = true;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (!first) {
                sb.append(", ");
            }
            if (first) {
                first = false;
            }
            sb.append("\"").append(entry.getKey()).append("\": \"")
                    .append(entry.getValue()).append("\"");
        }
        sb.append("}");
    }

}
