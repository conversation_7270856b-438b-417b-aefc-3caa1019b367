package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum JumpTargetEnum  implements CodeDescEnum {
    HANG_UP(0, "挂机"), // 挂机
    NEXT_STEP(1, "下一主动流程"), // 只用于主动流程
    ORIGINAL_STEP(2, "回到原主动流程"), // 只用于多轮对话
    SPECIFIED_STEP(3, "指定主动流程"),
    ;
    JumpTargetEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    Integer code;
    String desc;
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}

