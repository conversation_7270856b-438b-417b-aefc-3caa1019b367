package com.yiwise.dialogflow.api.dto.response.douyin;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 抖音回调和查询的数据
 */
@Data
public class DouyinBotInfoDTO {
    public static final String STATUS_ENABLE = "ENABLE";
    public static final String STATUS_DISABLE = "DISABLE";

    /**
     * 供应商 code
     */
    String provider;

    /**
     * 话术 id
     */
    String script;

    /**
     * 并发数, 来源呼入接待平台提供接口
     */
    Integer concurrency;

    /**
     * 剧本归属业务线
     * 配置在话术描述里
     */
    Integer businessLineId;

    /**
     * 自定义变量, key 自定义变量名称, value: 自定义变量描述
     */
    Map<String, String> params;

    /**
     * 意向等级列表
     */
    List<DouyinGradingItemDTO> gradings;

    /**
     * 状态, 启用:ENABLE, 停用: DISABLE
     */
    String status;
}
