package com.yiwise.dialogflow.api.dto.response.specialAnswerConfig;

import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioDetail;
import lombok.Data;

import java.util.List;

@Data
public class SimpleSpecialAnswerConfig {

    /**
     * 机器人id
     */
    Long botId;

    /**
     * 话术id
     */
    Long dialogFlowId;

    /**
     * 特殊语境id
     */
    String id;

    /**
     * 特殊语境名称
     */
    String name;

    /**
     * 特殊语境标签
     */
    String label;

    /**
     * 答案列表
     */
    List<AnswerAudioDetail> answerList;
}
